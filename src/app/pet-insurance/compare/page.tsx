"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Shield,
  Check,
  X,
  Star,
  DollarSign,
  Heart,
  Phone,
  Clock,
  Award,
  TrendingUp,
  Users,
  CreditCard
} from "lucide-react"
import { motion } from "framer-motion"

interface InsurancePlan {
  id: string
  provider: string
  planName: string
  monthlyPrice: {
    dog: string
    cat: string
  }
  deductible: string[]
  reimbursement: string[]
  annualLimit: string
  coverage: {
    accidents: boolean
    illnesses: boolean
    hereditary: boolean
    behavioral: boolean
    wellness: boolean
    dental: boolean
    prescription: boolean
  }
  features: string[]
  rating: number
  waitingPeriod: string
  ageLimit: string
  popular?: boolean
  recommended?: boolean
}

const insurancePlans: InsurancePlan[] = [
  {
    id: "petplan-complete",
    provider: "PetPlan",
    planName: "Complete Coverage",
    monthlyPrice: { dog: "$45-85", cat: "$25-55" },
    deductible: ["$250", "$500", "$1000"],
    reimbursement: ["70%", "80%", "90%"],
    annualLimit: "Unlimited",
    coverage: {
      accidents: true,
      illnesses: true,
      hereditary: true,
      behavioral: true,
      wellness: false,
      dental: true,
      prescription: true
    },
    features: [
      "24/7 vet helpline",
      "No breed restrictions",
      "Direct vet payments",
      "Mobile app claims"
    ],
    rating: 4.8,
    waitingPeriod: "15 days",
    ageLimit: "No upper age limit",
    recommended: true
  },
  {
    id: "healthy-paws",
    provider: "Healthy Paws",
    planName: "Accident & Illness",
    monthlyPrice: { dog: "$35-75", cat: "$20-45" },
    deductible: ["$250", "$500"],
    reimbursement: ["70%", "80%"],
    annualLimit: "Unlimited",
    coverage: {
      accidents: true,
      illnesses: true,
      hereditary: true,
      behavioral: false,
      wellness: false,
      dental: false,
      prescription: true
    },
    features: [
      "Fast claim processing",
      "No caps on payouts",
      "Covers alternative therapy",
      "Lifetime coverage"
    ],
    rating: 4.7,
    waitingPeriod: "15 days",
    ageLimit: "14 years",
    popular: true
  },
  {
    id: "embrace-wellness",
    provider: "Embrace",
    planName: "Wellness Rewards",
    monthlyPrice: { dog: "$40-80", cat: "$25-50" },
    deductible: ["$200", "$300", "$500", "$1000"],
    reimbursement: ["65%", "70%", "80%", "90%"],
    annualLimit: "$5,000 - $30,000",
    coverage: {
      accidents: true,
      illnesses: true,
      hereditary: true,
      behavioral: true,
      wellness: true,
      dental: true,
      prescription: true
    },
    features: [
      "Wellness rewards program",
      "Diminishing deductible",
      "Prescription coverage",
      "Dental coverage included"
    ],
    rating: 4.6,
    waitingPeriod: "14 days",
    ageLimit: "No upper age limit"
  },
  {
    id: "trupanion-basic",
    provider: "Trupanion",
    planName: "Medical Insurance",
    monthlyPrice: { dog: "$50-90", cat: "$30-60" },
    deductible: ["$0-1000 (per condition)"],
    reimbursement: ["90%"],
    annualLimit: "Unlimited",
    coverage: {
      accidents: true,
      illnesses: true,
      hereditary: true,
      behavioral: false,
      wellness: false,
      dental: false,
      prescription: true
    },
    features: [
      "Direct vet payments",
      "No payout limits",
      "Per-condition deductibles",
      "Coverage for life"
    ],
    rating: 4.5,
    waitingPeriod: "5 days",
    ageLimit: "No upper age limit"
  }
]

export default function PetInsuranceComparePage() {
  const [selectedPet, setSelectedPet] = useState<"dog" | "cat">("dog")
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null)

  const renderStars = (rating: number) => (
    <div className="flex items-center">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`w-4 h-4 ${
            star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
          }`}
        />
      ))}
      <span className="ml-2 text-sm text-gray-600">{rating}</span>
    </div>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-green-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Compare Pet
              <span className="block text-blue-300">Insurance Plans</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-green-100">
              Find the perfect insurance coverage for your furry family member
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Pet Type Selector */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center justify-center space-x-4">
              <span className="text-lg font-medium">Compare plans for:</span>
              <div className="flex space-x-2">
                <Button
                  variant={selectedPet === "dog" ? "default" : "outline"}
                  onClick={() => setSelectedPet("dog")}
                  className="flex items-center space-x-2"
                >
                  <Heart className="w-4 h-4" />
                  <span>Dogs</span>
                </Button>
                <Button
                  variant={selectedPet === "cat" ? "default" : "outline"}
                  onClick={() => setSelectedPet("cat")}
                  className="flex items-center space-x-2"
                >
                  <Heart className="w-4 h-4" />
                  <span>Cats</span>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Comparison Table */}
        <div className="grid gap-6 lg:grid-cols-2 xl:grid-cols-4">
          {insurancePlans.map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className={`h-full relative ${
                plan.recommended ? 'ring-2 ring-blue-500' : ''
              }`}>
                {plan.recommended && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-500 text-white">
                      <Award className="w-3 h-3 mr-1" />
                      Recommended
                    </Badge>
                  </div>
                )}
                {plan.popular && (
                  <div className="absolute -top-3 right-4">
                    <Badge className="bg-orange-500 text-white">
                      <TrendingUp className="w-3 h-3 mr-1" />
                      Popular
                    </Badge>
                  </div>
                )}
                
                <CardHeader className="text-center">
                  <CardTitle className="text-xl">{plan.provider}</CardTitle>
                  <CardDescription className="font-medium">{plan.planName}</CardDescription>
                  <div className="text-3xl font-bold text-blue-600">
                    {plan.monthlyPrice[selectedPet]}
                    <span className="text-sm font-normal text-gray-500">/month</span>
                  </div>
                  {renderStars(plan.rating)}
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Coverage Details */}
                  <div>
                    <h4 className="font-semibold text-sm mb-2">Coverage</h4>
                    <div className="space-y-1 text-sm">
                      {Object.entries(plan.coverage).map(([key, covered]) => (
                        <div key={key} className="flex items-center justify-between">
                          <span className="capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                          {covered ? (
                            <Check className="w-4 h-4 text-green-500" />
                          ) : (
                            <X className="w-4 h-4 text-red-500" />
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Plan Details */}
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="font-medium">Deductible:</span>
                      <div className="text-gray-600">{plan.deductible.join(", ")}</div>
                    </div>
                    <div>
                      <span className="font-medium">Reimbursement:</span>
                      <div className="text-gray-600">{plan.reimbursement.join(", ")}</div>
                    </div>
                    <div>
                      <span className="font-medium">Annual Limit:</span>
                      <div className="text-gray-600">{plan.annualLimit}</div>
                    </div>
                    <div>
                      <span className="font-medium">Waiting Period:</span>
                      <div className="text-gray-600">{plan.waitingPeriod}</div>
                    </div>
                  </div>
                  
                  {/* Features */}
                  <div>
                    <h4 className="font-semibold text-sm mb-2">Key Features</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {plan.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start">
                          <Check className="w-3 h-3 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <Button className="w-full mt-4">
                    Get Quote
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Important Information */}
        <div className="mt-12 grid md:grid-cols-2 gap-6">
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertTitle>Important Considerations</AlertTitle>
            <AlertDescription>
              <ul className="mt-2 space-y-1 text-sm">
                <li>• Pre-existing conditions are typically not covered</li>
                <li>• Waiting periods apply for illness coverage</li>
                <li>• Premiums may increase with age</li>
                <li>• Read policy terms carefully before purchasing</li>
              </ul>
            </AlertDescription>
          </Alert>
          
          <Alert>
            <DollarSign className="h-4 w-4" />
            <AlertTitle>Money-Saving Tips</AlertTitle>
            <AlertDescription>
              <ul className="mt-2 space-y-1 text-sm">
                <li>• Enroll pets while young and healthy</li>
                <li>• Consider higher deductibles for lower premiums</li>
                <li>• Look for multi-pet discounts</li>
                <li>• Compare annual vs. monthly payment options</li>
              </ul>
            </AlertDescription>
          </Alert>
        </div>

        {/* FAQ Section */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Frequently Asked Questions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">When should I get pet insurance?</h4>
                <p className="text-gray-600 text-sm">
                  The best time to get pet insurance is when your pet is young and healthy. 
                  This ensures maximum coverage and lower premiums.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">What's typically not covered?</h4>
                <p className="text-gray-600 text-sm">
                  Pre-existing conditions, cosmetic procedures, breeding costs, and routine 
                  preventive care (unless you have a wellness plan) are usually excluded.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">How do claims work?</h4>
                <p className="text-gray-600 text-sm">
                  Most insurers require you to pay the vet bill upfront, then submit a claim 
                  for reimbursement. Some offer direct payment to veterinarians.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
