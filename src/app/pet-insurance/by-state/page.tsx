"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  MapPin,
  Search,
  Shield,
  DollarSign,
  TrendingUp,
  Users,
  Building,
  Phone,
  ExternalLink,
  Info
} from "lucide-react"
import { motion } from "framer-motion"

interface StateInsuranceInfo {
  state: string
  abbreviation: string
  averageCost: {
    dog: string
    cat: string
  }
  topProviders: string[]
  regulations: string[]
  vetCosts: {
    routine: string
    emergency: string
  }
  coverage: string
  specialNotes?: string
}

const stateData: StateInsuranceInfo[] = [
  {
    state: "California",
    abbreviation: "CA",
    averageCost: { dog: "$45-85", cat: "$25-55" },
    topProviders: ["Healthy Paws", "Petplan", "Embrace", "Trupanion"],
    regulations: ["No breed restrictions allowed", "30-day free look period", "Clear disclosure requirements"],
    vetCosts: { routine: "$200-400", emergency: "$1,500-5,000" },
    coverage: "Comprehensive coverage available",
    specialNotes: "High vet costs make insurance particularly valuable"
  },
  {
    state: "Texas",
    abbreviation: "TX",
    averageCost: { dog: "$35-70", cat: "$20-45" },
    topProviders: ["ASPCA", "Petplan", "Healthy Paws", "Figo"],
    regulations: ["Standard insurance regulations", "Consumer protection laws"],
    vetCosts: { routine: "$150-350", emergency: "$1,000-4,000" },
    coverage: "Good coverage options available"
  },
  {
    state: "Florida",
    abbreviation: "FL",
    averageCost: { dog: "$40-75", cat: "$22-50" },
    topProviders: ["Embrace", "Petplan", "Healthy Paws", "Spot"],
    regulations: ["Hurricane coverage considerations", "Standard pet insurance laws"],
    vetCosts: { routine: "$180-380", emergency: "$1,200-4,500" },
    coverage: "Weather-related injury coverage important"
  },
  {
    state: "New York",
    abbreviation: "NY",
    averageCost: { dog: "$50-90", cat: "$30-60" },
    topProviders: ["Petplan", "Healthy Paws", "Embrace", "Trupanion"],
    regulations: ["Strict consumer protection", "Mandatory coverage disclosures"],
    vetCosts: { routine: "$250-450", emergency: "$2,000-6,000" },
    coverage: "Premium coverage options available",
    specialNotes: "Higher costs in NYC area"
  },
  {
    state: "Illinois",
    abbreviation: "IL",
    averageCost: { dog: "$38-72", cat: "$23-48" },
    topProviders: ["ASPCA", "Petplan", "Figo", "Healthy Paws"],
    regulations: ["Standard regulations", "Consumer rights protection"],
    vetCosts: { routine: "$170-370", emergency: "$1,100-4,200" },
    coverage: "Good mid-range options"
  },
  {
    state: "Pennsylvania",
    abbreviation: "PA",
    averageCost: { dog: "$36-68", cat: "$21-46" },
    topProviders: ["Embrace", "Petplan", "Healthy Paws", "ASPCA"],
    regulations: ["Standard pet insurance laws", "Clear policy terms required"],
    vetCosts: { routine: "$160-360", emergency: "$1,000-4,000" },
    coverage: "Comprehensive options available"
  }
]

export default function PetInsuranceByStatePage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedState, setSelectedState] = useState<StateInsuranceInfo | null>(null)

  const filteredStates = stateData.filter(state =>
    state.state.toLowerCase().includes(searchTerm.toLowerCase()) ||
    state.abbreviation.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-green-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Pet Insurance
              <span className="block text-blue-300">by State</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-green-100">
              Find state-specific insurance information, costs, and regulations
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Search Section */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="relative max-w-md mx-auto">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search by state name or abbreviation..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* State Selection Grid */}
        {!selectedState && (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {filteredStates.map((state, index) => (
              <motion.div
                key={state.state}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card 
                  className="cursor-pointer hover:shadow-lg transition-shadow"
                  onClick={() => setSelectedState(state)}
                >
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <MapPin className="w-5 h-5 mr-2 text-blue-600" />
                      {state.state}
                    </CardTitle>
                    <CardDescription>Click to view detailed information</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Avg. Dog Insurance:</span>
                        <span className="font-medium">{state.averageCost.dog}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Avg. Cat Insurance:</span>
                        <span className="font-medium">{state.averageCost.cat}</span>
                      </div>
                      <div className="pt-2">
                        <span className="text-xs text-gray-500">
                          {state.topProviders.length} top providers available
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}

        {/* Detailed State Information */}
        {selectedState && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="mb-6">
              <Button 
                variant="outline" 
                onClick={() => setSelectedState(null)}
                className="mb-4"
              >
                ← Back to State List
              </Button>
            </div>

            <div className="grid lg:grid-cols-2 gap-8">
              {/* State Overview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-2xl">
                    <MapPin className="w-6 h-6 mr-2 text-blue-600" />
                    {selectedState.state} ({selectedState.abbreviation})
                  </CardTitle>
                  <CardDescription>Pet Insurance Overview</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Average Costs */}
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center">
                      <DollarSign className="w-4 h-4 mr-2 text-green-600" />
                      Average Monthly Costs
                    </h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <div className="text-sm text-gray-600">Dogs</div>
                        <div className="text-xl font-bold text-blue-600">
                          {selectedState.averageCost.dog}
                        </div>
                      </div>
                      <div className="bg-purple-50 p-3 rounded-lg">
                        <div className="text-sm text-gray-600">Cats</div>
                        <div className="text-xl font-bold text-purple-600">
                          {selectedState.averageCost.cat}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Veterinary Costs */}
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center">
                      <Building className="w-4 h-4 mr-2 text-red-600" />
                      Typical Veterinary Costs
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Routine Visit:</span>
                        <span className="font-medium">{selectedState.vetCosts.routine}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Emergency Visit:</span>
                        <span className="font-medium">{selectedState.vetCosts.emergency}</span>
                      </div>
                    </div>
                  </div>

                  {/* Coverage Information */}
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Shield className="w-4 h-4 mr-2 text-green-600" />
                      Coverage Availability
                    </h4>
                    <p className="text-sm text-gray-600">{selectedState.coverage}</p>
                  </div>

                  {selectedState.specialNotes && (
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Special Notes</AlertTitle>
                      <AlertDescription>{selectedState.specialNotes}</AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>

              {/* Providers and Regulations */}
              <div className="space-y-6">
                {/* Top Providers */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
                      Top Insurance Providers
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {selectedState.topProviders.map((provider, index) => (
                        <div key={provider} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center">
                            <Badge className="mr-3">#{index + 1}</Badge>
                            <span className="font-medium">{provider}</span>
                          </div>
                          <Button size="sm" variant="outline">
                            Get Quote
                            <ExternalLink className="w-3 h-3 ml-1" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* State Regulations */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Shield className="w-5 h-5 mr-2 text-green-600" />
                      State Regulations
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {selectedState.regulations.map((regulation, index) => (
                        <li key={index} className="flex items-start">
                          <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                          <span className="text-sm text-gray-600">{regulation}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>

                {/* Contact Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Phone className="w-5 h-5 mr-2 text-blue-600" />
                      Need Help?
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <Button className="w-full">
                        Compare Plans in {selectedState.state}
                      </Button>
                      <Button variant="outline" className="w-full">
                        Contact Local Agent
                      </Button>
                      <Button variant="outline" className="w-full">
                        State Insurance Department
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </motion.div>
        )}

        {/* General Information */}
        {!selectedState && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Understanding Pet Insurance by State</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-semibold mb-2 flex items-center">
                    <DollarSign className="w-4 h-4 mr-2 text-green-600" />
                    Cost Variations
                  </h4>
                  <p className="text-sm text-gray-600">
                    Pet insurance costs vary by state due to veterinary costs, regulations, and market competition.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2 flex items-center">
                    <Shield className="w-4 h-4 mr-2 text-blue-600" />
                    State Regulations
                  </h4>
                  <p className="text-sm text-gray-600">
                    Each state has different insurance regulations that may affect coverage options and consumer protections.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2 flex items-center">
                    <Users className="w-4 h-4 mr-2 text-purple-600" />
                    Provider Availability
                  </h4>
                  <p className="text-sm text-gray-600">
                    Not all insurance providers operate in every state. Check availability in your area.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
