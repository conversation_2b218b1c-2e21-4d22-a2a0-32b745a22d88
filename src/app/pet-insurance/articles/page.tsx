"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Search,
  Clock,
  User,
  BookOpen,
  TrendingUp,
  Shield,
  DollarSign,
  Heart,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Tag
} from "lucide-react"
import { motion } from "framer-motion"

interface Article {
  id: string
  title: string
  excerpt: string
  content: string
  author: string
  publishDate: string
  readTime: string
  category: string
  tags: string[]
  featured?: boolean
  trending?: boolean
}

const articles: Article[] = [
  {
    id: "1",
    title: "Pet Insurance 101: Everything You Need to Know",
    excerpt: "A comprehensive guide to understanding pet insurance, what it covers, and how to choose the right plan for your furry friend.",
    content: "Pet insurance can be a lifesaver when unexpected veterinary bills arise...",
    author: "<PERSON>. <PERSON>",
    publishDate: "2024-01-15",
    readTime: "8 min read",
    category: "Basics",
    tags: ["beginner", "coverage", "basics"],
    featured: true
  },
  {
    id: "2",
    title: "Pre-existing Conditions: What Pet Insurance Won't Cover",
    excerpt: "Understanding pre-existing conditions and how they affect your pet insurance coverage and claims.",
    content: "One of the most important aspects of pet insurance to understand...",
    author: "Dr. Michael Chen",
    publishDate: "2024-01-10",
    readTime: "6 min read",
    category: "Coverage",
    tags: ["pre-existing", "exclusions", "claims"],
    trending: true
  },
  {
    id: "3",
    title: "Is Pet Insurance Worth It? A Cost-Benefit Analysis",
    excerpt: "Breaking down the costs and benefits of pet insurance to help you make an informed decision.",
    content: "Many pet owners wonder whether pet insurance is worth the monthly premium...",
    author: "Jennifer Martinez",
    publishDate: "2024-01-08",
    readTime: "10 min read",
    category: "Financial",
    tags: ["cost", "benefits", "analysis"],
    featured: true
  },
  {
    id: "4",
    title: "How to File a Pet Insurance Claim: Step-by-Step Guide",
    excerpt: "Learn the process of filing a pet insurance claim and tips to ensure quick reimbursement.",
    content: "Filing a pet insurance claim doesn't have to be complicated...",
    author: "Dr. Lisa Thompson",
    publishDate: "2024-01-05",
    readTime: "5 min read",
    category: "Claims",
    tags: ["claims", "reimbursement", "process"]
  },
  {
    id: "5",
    title: "Wellness Plans vs. Pet Insurance: What's the Difference?",
    excerpt: "Understanding the key differences between wellness plans and traditional pet insurance coverage.",
    content: "Many pet owners are confused about the difference between wellness plans...",
    author: "Dr. Robert Kim",
    publishDate: "2024-01-03",
    readTime: "7 min read",
    category: "Types",
    tags: ["wellness", "comparison", "coverage"]
  },
  {
    id: "6",
    title: "Pet Insurance for Senior Pets: Special Considerations",
    excerpt: "What you need to know about insuring older pets and age-related coverage limitations.",
    content: "Insuring senior pets comes with unique challenges and considerations...",
    author: "Dr. Amanda Foster",
    publishDate: "2023-12-28",
    readTime: "9 min read",
    category: "Senior Pets",
    tags: ["senior", "age", "limitations"],
    trending: true
  }
]

const categories = ["All", "Basics", "Coverage", "Financial", "Claims", "Types", "Senior Pets"]

export default function PetInsuranceArticlesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null)

  const filteredArticles = articles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === "All" || article.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const featuredArticles = articles.filter(article => article.featured)
  const trendingArticles = articles.filter(article => article.trending)

  if (selectedArticle) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <div className="container mx-auto px-4 py-8">
          <Button 
            variant="outline" 
            onClick={() => setSelectedArticle(null)}
            className="mb-6"
          >
            ← Back to Articles
          </Button>
          
          <article className="max-w-4xl mx-auto">
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2 mb-4">
                  <Badge>{selectedArticle.category}</Badge>
                  {selectedArticle.featured && <Badge variant="outline">Featured</Badge>}
                  {selectedArticle.trending && <Badge className="bg-orange-100 text-orange-800">Trending</Badge>}
                </div>
                <CardTitle className="text-3xl mb-4">{selectedArticle.title}</CardTitle>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <User className="w-4 h-4 mr-1" />
                    {selectedArticle.author}
                  </div>
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {new Date(selectedArticle.publishDate).toLocaleDateString()}
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {selectedArticle.readTime}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  <p className="text-lg text-gray-700 mb-6">{selectedArticle.excerpt}</p>
                  <div className="text-gray-600 leading-relaxed">
                    {selectedArticle.content}
                    <br /><br />
                    <p>This is a sample article content. In a real application, this would contain the full article text with proper formatting, images, and detailed information about pet insurance topics.</p>
                  </div>
                </div>
                
                <div className="mt-8 pt-6 border-t">
                  <h4 className="font-semibold mb-3">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedArticle.tags.map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        <Tag className="w-3 h-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </article>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-green-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Pet Insurance
              <span className="block text-blue-300">Articles & Guides</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-green-100">
              Expert insights and comprehensive guides to help you navigate pet insurance
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Search and Filter */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                {categories.map(category => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Featured Articles */}
        {selectedCategory === "All" && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6 flex items-center">
              <TrendingUp className="w-6 h-6 mr-2 text-blue-600" />
              Featured Articles
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              {featuredArticles.map((article, index) => (
                <motion.div
                  key={article.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer" onClick={() => setSelectedArticle(article)}>
                    <CardHeader>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge>{article.category}</Badge>
                        <Badge variant="outline">Featured</Badge>
                      </div>
                      <CardTitle className="text-xl">{article.title}</CardTitle>
                      <CardDescription>{article.excerpt}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center">
                            <User className="w-4 h-4 mr-1" />
                            {article.author}
                          </div>
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            {article.readTime}
                          </div>
                        </div>
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {new Date(article.publishDate).toLocaleDateString()}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* All Articles */}
        <div>
          <h2 className="text-2xl font-bold mb-6 flex items-center">
            <BookOpen className="w-6 h-6 mr-2 text-green-600" />
            {selectedCategory === "All" ? "All Articles" : `${selectedCategory} Articles`}
          </h2>
          
          <div className="grid lg:grid-cols-3 gap-6">
            {filteredArticles.map((article, index) => (
              <motion.div
                key={article.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer" onClick={() => setSelectedArticle(article)}>
                  <CardHeader>
                    <div className="flex items-center gap-2 mb-2">
                      <Badge>{article.category}</Badge>
                      {article.featured && <Badge variant="outline">Featured</Badge>}
                      {article.trending && <Badge className="bg-orange-100 text-orange-800">Trending</Badge>}
                    </div>
                    <CardTitle className="text-lg">{article.title}</CardTitle>
                    <CardDescription className="line-clamp-3">{article.excerpt}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-1" />
                          {article.author}
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {article.readTime}
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-1">
                        {article.tags.slice(0, 3).map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      
                      <div className="text-xs text-gray-500">
                        {new Date(article.publishDate).toLocaleDateString()}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {filteredArticles.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No articles found</h3>
            <p className="text-gray-500">Try adjusting your search criteria</p>
          </div>
        )}

        {/* Newsletter Signup */}
        <Card className="mt-12">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Heart className="w-5 h-5 mr-2 text-red-600" />
              Stay Updated
            </CardTitle>
            <CardDescription>
              Get the latest pet insurance articles and tips delivered to your inbox
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 max-w-md">
              <Input placeholder="Enter your email" type="email" />
              <Button>Subscribe</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
