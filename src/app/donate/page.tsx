"use client"

import React from "react"
import { useState } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Heart, 
  DollarSign, 
  Shield, 
  Users, 
  Stethoscope,
  Home,
  Car,
  Utensils,
  Gift,
  CreditCard,
  Check,
  ArrowRight
} from "lucide-react"
import Link from "next/link"
import { toast } from "react-hot-toast"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { donationSchema, type DonationInput } from "@/lib/validations"

const DONATION_AMOUNTS = [25, 50, 100, 250, 500, 1000]

const DONATION_IMPACTS = [
  {
    amount: 25,
    icon: Utensils,
    title: "Feed a Pet",
    description: "Provides food for a pet for one week",
    color: "bg-green-100 text-green-600"
  },
  {
    amount: 50,
    icon: Stethoscope,
    title: "Basic Vet Care",
    description: "Covers basic medical examination and vaccines",
    color: "bg-blue-100 text-blue-600"
  },
  {
    amount: 100,
    icon: Home,
    title: "Shelter a Pet",
    description: "Provides shelter and care for a pet for one month",
    color: "bg-purple-100 text-purple-600"
  },
  {
    amount: 250,
    icon: Car,
    title: "Emergency Transport",
    description: "Covers emergency transport and urgent medical care",
    color: "bg-red-100 text-red-600"
  },
  {
    amount: 500,
    icon: Heart,
    title: "Complete Care Package",
    description: "Full medical care, spay/neuter, and microchipping",
    color: "bg-pink-100 text-pink-600"
  },
  {
    amount: 1000,
    icon: Users,
    title: "Save Multiple Lives",
    description: "Comprehensive care for multiple pets in need",
    color: "bg-orange-100 text-orange-600"
  }
]

const DonatePage: React.FC = () => {
  const { data: session } = useSession()
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null)
  const [customAmount, setCustomAmount] = useState("")
  const [loading, setLoading] = useState(false)
  const [donationFrequency, setDonationFrequency] = useState<"one-time" | "monthly">("one-time")

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<DonationInput>({
    resolver: zodResolver(donationSchema),
    defaultValues: {
      isAnonymous: false,
      isMemorial: false,
      isRecurring: false,
    },
  })

  const watchedValues = watch()

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount)
    setCustomAmount("")
    setValue("amount", amount)
  }

  const onSubmit = async (data: DonationInput) => {
    setLoading(true)
    try {
      // Here you would handle the donation submission
      await new Promise(resolve => setTimeout(resolve, 1500)) // Simulated API call
      toast.success("Thank you for your donation!")
    } catch (error) {
      toast.error("Failed to process donation. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      {/* Hero Section */}
      <div className="relative bg-blue-600 text-white py-20">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-black/50 to-black/25"></div>
          <img
            src="/images/home background.jpg"
            alt="Donation Background"
            className="w-full h-full object-cover"
          />
        </div>
        <div className="relative container mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">Make a Difference Today</h1>
          <p className="text-xl max-w-2xl mx-auto mb-8">
            Your donation helps us provide shelter, medical care, and love to animals in need.
            Every contribution, no matter the size, creates a lasting impact.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Badge variant="secondary" className="px-4 py-2 text-lg">
              <Shield className="w-5 h-5 mr-2" />
              Secure Donation
            </Badge>
            <Badge variant="secondary" className="px-4 py-2 text-lg">
              <Check className="w-5 h-5 mr-2" />
              Tax Deductible
            </Badge>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Donation Form */}
          <div>
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl">Choose Your Impact</CardTitle>
                <CardDescription>Select your donation frequency and amount</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4 mb-6">
                  <Button
                    variant={donationFrequency === "one-time" ? "default" : "outline"}
                    onClick={() => setDonationFrequency("one-time")}
                    className="flex-1"
                  >
                    One-time
                  </Button>
                  <Button
                    variant={donationFrequency === "monthly" ? "default" : "outline"}
                    onClick={() => setDonationFrequency("monthly")}
                    className="flex-1"
                  >
                    Monthly
                  </Button>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                  {DONATION_AMOUNTS.map((amount) => (
                    <Button
                      key={amount}
                      variant={selectedAmount === amount ? "default" : "outline"}
                      onClick={() => handleAmountSelect(amount)}
                      className="w-full h-16 text-lg"
                    >
                      ${amount}
                    </Button>
                  ))}
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium mb-2">Custom Amount</label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
                    <Input
                      type="number"
                      placeholder="Enter amount"
                      className="pl-10"
                      value={customAmount}
                      onChange={(e) => {
                        setCustomAmount(e.target.value)
                        setSelectedAmount(null)
                        setValue("amount", parseFloat(e.target.value))
                      }}
                    />
                  </div>
                </div>

                <Button
                  className="w-full h-12 text-lg mb-4"
                  disabled={loading}
                  onClick={handleSubmit(onSubmit)}
                >
                  {loading ? "Processing..." : `Donate ${donationFrequency === "monthly" ? "Monthly" : "Now"}`}
                  <ArrowRight className="ml-2" />
                </Button>

                <div className="flex items-center justify-center space-x-2 mt-4">
                  <Shield className="h-4 w-4 text-gray-500" />
                  <p className="text-sm text-gray-500">
                    Protected by Stripe. Your transaction is secure.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Impact Cards */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold mb-6">Your Impact</h2>
            <div className="grid gap-6">
              {DONATION_IMPACTS.map((impact) => (
                <Card key={impact.amount} className="overflow-hidden transform hover:scale-102 transition-transform">
                  <div className="flex items-start p-6">
                    <div className={`p-3 rounded-full mr-4 ${impact.color}`}>
                      <impact.icon className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-1">{impact.title}</h3>
                      <p className="text-gray-600">{impact.description}</p>
                      <Badge variant="secondary" className="mt-2">
                        ${impact.amount} donation
                      </Badge>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-16 pt-16 border-t">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="h-12 w-12 rounded-full bg-blue-100 text-blue-600 mx-auto mb-4 flex items-center justify-center">
                    <Shield className="h-6 w-6" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Secure Donations</h3>
                  <p className="text-gray-600">Your financial information is encrypted and secure</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="h-12 w-12 rounded-full bg-green-100 text-green-600 mx-auto mb-4 flex items-center justify-center">
                    <Check className="h-6 w-6" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Tax Deductible</h3>
                  <p className="text-gray-600">All donations are tax-deductible to the full extent</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <div className="h-12 w-12 rounded-full bg-purple-100 text-purple-600 mx-auto mb-4 flex items-center justify-center">
                    <Heart className="h-6 w-6" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Direct Impact</h3>
                  <p className="text-gray-600">Your donation directly helps animals in need</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DonatePage
