"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { toast } from "react-hot-toast"
import { 
  Phone, 
  Mail, 
  MessageSquare, 
  Send,
  Heart,
  Stethoscope,
  GraduationCap,
  Award,
  Clock,
  CheckCircle,
  Star
} from "lucide-react"
import { motion } from "framer-motion"
import Link from "next/link"

const expertContactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  petType: z.string().min(1, "Please select a pet type"),
  expertType: z.string().min(1, "Please select an expert type"),
  urgency: z.string().min(1, "Please select urgency level"),
  subject: z.string().min(1, "Subject is required"),
  question: z.string().min(20, "Question must be at least 20 characters"),
  preferredContact: z.string().min(1, "Please select preferred contact method"),
})

type ExpertContactFormData = z.infer<typeof expertContactSchema>

const petTypes = [
  { value: "dog", label: "Dog" },
  { value: "cat", label: "Cat" },
  { value: "bird", label: "Bird" },
  { value: "rabbit", label: "Rabbit" },
  { value: "reptile", label: "Reptile" },
  { value: "other", label: "Other" }
]

const expertTypes = [
  { value: "veterinarian", label: "Veterinarian", description: "Medical health questions" },
  { value: "behaviorist", label: "Animal Behaviorist", description: "Behavior and training issues" },
  { value: "nutritionist", label: "Pet Nutritionist", description: "Diet and nutrition advice" },
  { value: "trainer", label: "Professional Trainer", description: "Training and obedience" },
  { value: "groomer", label: "Professional Groomer", description: "Grooming and care tips" },
  { value: "general", label: "General Pet Care Expert", description: "General care questions" }
]

const urgencyLevels = [
  { value: "low", label: "Low - General question", color: "bg-green-100 text-green-800" },
  { value: "medium", label: "Medium - Need advice soon", color: "bg-yellow-100 text-yellow-800" },
  { value: "high", label: "High - Urgent concern", color: "bg-orange-100 text-orange-800" },
  { value: "emergency", label: "Emergency - Immediate help needed", color: "bg-red-100 text-red-800" }
]

const contactMethods = [
  { value: "email", label: "Email Response" },
  { value: "phone", label: "Phone Call" },
  { value: "video", label: "Video Consultation" },
  { value: "any", label: "Any Method" }
]

const experts = [
  {
    name: "Dr. Sarah Johnson",
    title: "Veterinarian",
    specialties: ["Internal Medicine", "Surgery", "Emergency Care"],
    experience: "15+ years",
    rating: 4.9,
    image: "/images/experts/dr-sarah.jpg"
  },
  {
    name: "Mike Rodriguez",
    title: "Animal Behaviorist",
    specialties: ["Dog Training", "Behavior Modification", "Aggression"],
    experience: "12+ years",
    rating: 4.8,
    image: "/images/experts/mike.jpg"
  },
  {
    name: "Dr. Emily Chen",
    title: "Pet Nutritionist",
    specialties: ["Diet Planning", "Weight Management", "Special Diets"],
    experience: "10+ years",
    rating: 4.9,
    image: "/images/experts/dr-emily.jpg"
  }
]

export default function ContactExpertPage() {
  const [loading, setLoading] = useState(false)
  
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<ExpertContactFormData>({
    resolver: zodResolver(expertContactSchema),
  })

  const selectedUrgency = watch("urgency")
  const selectedExpertType = watch("expertType")

  const onSubmit = async (data: ExpertContactFormData) => {
    setLoading(true)
    try {
      const response = await fetch("/api/contact-expert", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        toast.success("Your question has been sent to our experts! We'll get back to you soon.")
        reset()
      } else {
        const error = await response.json()
        toast.error(error.message || "Failed to send question")
      }
    } catch (error) {
      console.error("Error sending question:", error)
      toast.error("Something went wrong. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-green-50 to-white py-12">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Contact Pet Care Experts
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Get professional advice from certified veterinarians, behaviorists, and pet care specialists. 
            Our experts are here to help with all your pet care questions.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Expert Contact Form */}
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-2"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Ask an Expert
                </CardTitle>
                <p className="text-gray-600">
                  Fill out the form below and our experts will respond within 24 hours.
                </p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* Personal Information */}
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Your Name *</Label>
                      <Input
                        id="name"
                        {...register("name")}
                        error={errors.name?.message}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        {...register("email")}
                        error={errors.email?.message}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number (Optional)</Label>
                    <Input
                      id="phone"
                      type="tel"
                      {...register("phone")}
                      placeholder="For phone consultations"
                    />
                  </div>

                  {/* Question Details */}
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="petType">Pet Type *</Label>
                      <Select onValueChange={(value) => setValue("petType", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select pet type" />
                        </SelectTrigger>
                        <SelectContent>
                          {petTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.petType && (
                        <p className="text-sm text-red-600">{errors.petType.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="expertType">Expert Type *</Label>
                      <Select onValueChange={(value) => setValue("expertType", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select expert type" />
                        </SelectTrigger>
                        <SelectContent>
                          {expertTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              <div>
                                <div className="font-medium">{type.label}</div>
                                <div className="text-sm text-gray-500">{type.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.expertType && (
                        <p className="text-sm text-red-600">{errors.expertType.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="urgency">Urgency Level *</Label>
                      <Select onValueChange={(value) => setValue("urgency", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select urgency" />
                        </SelectTrigger>
                        <SelectContent>
                          {urgencyLevels.map((level) => (
                            <SelectItem key={level.value} value={level.value}>
                              {level.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.urgency && (
                        <p className="text-sm text-red-600">{errors.urgency.message}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="preferredContact">Preferred Contact Method *</Label>
                      <Select onValueChange={(value) => setValue("preferredContact", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select contact method" />
                        </SelectTrigger>
                        <SelectContent>
                          {contactMethods.map((method) => (
                            <SelectItem key={method.value} value={method.value}>
                              {method.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.preferredContact && (
                        <p className="text-sm text-red-600">{errors.preferredContact.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subject">Subject *</Label>
                    <Input
                      id="subject"
                      {...register("subject")}
                      error={errors.subject?.message}
                      placeholder="Brief description of your question"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="question">Your Question *</Label>
                    <Textarea
                      id="question"
                      {...register("question")}
                      rows={6}
                      placeholder="Please provide as much detail as possible about your question or concern..."
                    />
                    {errors.question && (
                      <p className="text-sm text-red-600">{errors.question.message}</p>
                    )}
                  </div>

                  {/* Urgency Warning */}
                  {selectedUrgency === "emergency" && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="flex items-start space-x-2">
                        <Stethoscope className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                        <div className="text-sm text-red-800">
                          <p className="font-medium mb-1">Emergency Situation Detected</p>
                          <p>For immediate veterinary emergencies, please contact your local emergency vet clinic or call our emergency hotline at (555) 911-PETS.</p>
                        </div>
                      </div>
                    </div>
                  )}

                  <Button
                    type="submit"
                    disabled={loading}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    {loading ? "Sending..." : "Send Question to Expert"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>

          {/* Expert Information */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Response Time */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Response Times
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">General Questions</span>
                  <Badge variant="secondary">24 hours</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Urgent Concerns</span>
                  <Badge className="bg-orange-100 text-orange-800">4-6 hours</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Emergencies</span>
                  <Badge className="bg-red-100 text-red-800">Immediate</Badge>
                </div>
              </CardContent>
            </Card>

            {/* Featured Experts */}
            <Card>
              <CardHeader>
                <CardTitle>Our Expert Team</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {experts.map((expert, index) => (
                  <div key={index} className="border rounded-lg p-3">
                    <div className="flex items-start space-x-3">
                      <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                        <GraduationCap className="h-6 w-6 text-gray-600" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{expert.name}</h4>
                        <p className="text-sm text-gray-600">{expert.title}</p>
                        <div className="flex items-center space-x-1 mt-1">
                          <Star className="h-3 w-3 text-yellow-400 fill-current" />
                          <span className="text-xs text-gray-500">{expert.rating}</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-500">{expert.experience}</span>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {expert.specialties.slice(0, 2).map((specialty, i) => (
                            <Badge key={i} variant="outline" className="text-xs">
                              {specialty}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Need Immediate Help?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button asChild variant="outline" className="w-full justify-start">
                  <a href="tel:+15559117387">
                    <Phone className="h-4 w-4 mr-2" />
                    Emergency Hotline
                  </a>
                </Button>
                <Button asChild variant="outline" className="w-full justify-start">
                  <Link href="/schedule-consultation">
                    <Stethoscope className="h-4 w-4 mr-2" />
                    Schedule Consultation
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full justify-start">
                  <Link href="/pet-care">
                    <Heart className="h-4 w-4 mr-2" />
                    Pet Care Guide
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
