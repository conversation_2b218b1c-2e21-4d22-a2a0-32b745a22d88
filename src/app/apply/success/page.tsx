"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  CheckCircle, 
  Heart, 
  Calendar, 
  MessageSquare, 
  ArrowRight,
  Download,
  Mail,
  Phone
} from "lucide-react"
import { motion } from "framer-motion"

export default function ApplicationSuccessPage() {
  const searchParams = useSearchParams()
  const [applicationId, setApplicationId] = useState<string | null>(null)
  const [petName, setPetName] = useState<string | null>(null)

  useEffect(() => {
    setApplicationId(searchParams.get("applicationId"))
    setPetName(searchParams.get("petName"))
  }, [searchParams])

  return (
    <div className="min-h-screen bg-gradient-to-b from-green-50 to-white py-12">
      <div className="max-w-4xl mx-auto px-4">
        {/* Success Animation */}
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, type: "spring" }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center justify-center w-24 h-24 bg-green-100 rounded-full mb-6">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Application Submitted Successfully! 🎉
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Thank you for your interest in adopting {petName || "one of our pets"}. 
            Your application has been received and is being reviewed by our team.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Application Details */}
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="border-green-200">
              <CardHeader>
                <CardTitle className="flex items-center text-green-700">
                  <Heart className="h-5 w-5 mr-2" />
                  Application Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {applicationId && (
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600">Application ID</p>
                    <p className="font-mono text-lg font-semibold text-green-700">
                      #{applicationId}
                    </p>
                  </div>
                )}
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Status</span>
                    <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                      Under Review
                    </span>
                  </div>
                  <div className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Submitted</span>
                    <span className="text-gray-900">{new Date().toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <span className="text-gray-600">Expected Response</span>
                    <span className="text-gray-900">3-5 business days</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Next Steps */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>What Happens Next?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-blue-600 font-semibold text-sm">1</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Application Review</h4>
                      <p className="text-sm text-gray-600">Our team will review your application within 3-5 business days.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-blue-600 font-semibold text-sm">2</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Reference Check</h4>
                      <p className="text-sm text-gray-600">We'll contact your references to learn more about you.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-blue-600 font-semibold text-sm">3</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Meet & Greet</h4>
                      <p className="text-sm text-gray-600">Schedule a visit to meet your potential new family member.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-green-600 font-semibold text-sm">4</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Adoption Day!</h4>
                      <p className="text-sm text-gray-600">Complete the adoption and welcome your new pet home.</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Action Buttons */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="mt-12"
        >
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-0">
            <CardContent className="p-8">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  While You Wait...
                </h2>
                <p className="text-gray-600">
                  Here are some helpful resources and actions you can take
                </p>
              </div>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Button asChild className="h-auto p-4 flex-col space-y-2">
                  <Link href="/dashboard/applications">
                    <Heart className="h-6 w-6" />
                    <span className="text-sm">Track Application</span>
                  </Link>
                </Button>
                
                <Button asChild variant="outline" className="h-auto p-4 flex-col space-y-2">
                  <Link href="/schedule-visit">
                    <Calendar className="h-6 w-6" />
                    <span className="text-sm">Schedule Visit</span>
                  </Link>
                </Button>
                
                <Button asChild variant="outline" className="h-auto p-4 flex-col space-y-2">
                  <Link href="/contact-shelter">
                    <MessageSquare className="h-6 w-6" />
                    <span className="text-sm">Contact Shelter</span>
                  </Link>
                </Button>
                
                <Button asChild variant="outline" className="h-auto p-4 flex-col space-y-2">
                  <Link href="/pet-care">
                    <Download className="h-6 w-6" />
                    <span className="text-sm">Pet Care Guide</span>
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Contact Information */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-8"
        >
          <Card>
            <CardHeader>
              <CardTitle>Need Help or Have Questions?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Phone className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Call Us</p>
                    <p className="text-gray-600">(*************</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <Mail className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Email Us</p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 pt-6 border-t border-gray-200 text-center">
                <p className="text-gray-600 mb-4">
                  You'll receive email updates about your application status. 
                  Check your spam folder if you don't see our emails.
                </p>
                <Link href="/dashboard">
                  <Button variant="outline">
                    Go to Dashboard
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
