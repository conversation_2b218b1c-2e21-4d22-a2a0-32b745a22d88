"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON>er } from "next/navigation"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, ArrowRight, Check, User, Home, Heart, FileText, CreditCard, Send } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { toast } from "react-hot-toast"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { applicationSchema, type ApplicationInput } from "@/lib/validations"

interface Pet {
  id: string
  name: string
  species: string
  breed: string
  age: number
  photos: Array<{ url: string; isPrimary: boolean }>
  organization: {
    name: string
    city: string
    state: string
  }
}

const STEPS = [
  { id: 1, title: "Personal Info", icon: User, description: "Tell us about yourself" },
  { id: 2, title: "Housing", icon: Home, description: "Your living situation" },
  { id: 3, title: "Experience", icon: Heart, description: "Pet experience & preferences" },
  { id: 4, title: "References", icon: FileText, description: "Personal & veterinary references" },
  { id: 5, title: "Payment", icon: CreditCard, description: "Application fee ($50)" },
  { id: 6, title: "Review", icon: Check, description: "Review and submit" },
]

export default function AdoptionApplicationPage() {
  const params = useParams()
  const router = useRouter()
  const { data: session, status } = useSession()
  const [pet, setPet] = useState<Pet | null>(null)
  const [currentStep, setCurrentStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [paymentCompleted, setPaymentCompleted] = useState(false)
  const [paymentLoading, setPaymentLoading] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    trigger,
  } = useForm<ApplicationInput>({
    resolver: zodResolver(applicationSchema),
    defaultValues: {
      petId: params.petId as string,
      personalReferences: [
        { name: "", phone: "", relationship: "" },
        { name: "", phone: "", relationship: "" },
      ],
      veterinaryReference: {
        clinicName: "",
        phone: "",
        lastVisit: "",
      },
    },
  })

  const watchedValues = watch()

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push(`/auth/signin?callbackUrl=/apply/${params.petId}`)
      return
    }

    const fetchPet = async () => {
      try {
        const response = await fetch(`/api/pets/${params.petId}`)
        if (response.ok) {
          const data = await response.json()
          setPet(data.pet)
        } else {
          toast.error("Pet not found")
          router.push("/pets")
        }
      } catch (error) {
        console.error("Error fetching pet:", error)
        toast.error("Failed to load pet details")
      }
    }

    const checkPaymentStatus = async () => {
      try {
        const response = await fetch(`/api/payments/application-fee?petId=${params.petId}`)
        if (response.ok) {
          const data = await response.json()
          setPaymentCompleted(data.hasPaid)
        }
      } catch (error) {
        console.error("Error checking payment status:", error)
      }
    }

    if (params.petId) {
      fetchPet()
      checkPaymentStatus()
    }
  }, [params.petId, status, router])

  const nextStep = async () => {
    // Special handling for payment step
    if (currentStep === 5) {
      if (!paymentCompleted) {
        toast.error("Please complete the payment before proceeding.")
        return
      }
      setCurrentStep(prev => Math.min(prev + 1, STEPS.length))
      return
    }
    
    const fieldsToValidate = getFieldsForStep(currentStep)
    const isValid = await trigger(fieldsToValidate)
    
    if (isValid) {
      setCurrentStep(prev => Math.min(prev + 1, STEPS.length))
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const getFieldsForStep = (step: number): (keyof ApplicationInput)[] => {
    switch (step) {
      case 1:
        return ["applicantName", "applicantEmail", "applicantPhone"]
      case 2:
        return ["housingType", "ownOrRent", "hasYard", "householdSize"]
      case 3:
        return ["petExperience", "workSchedule", "exercisePlans"]
      case 4:
        return ["personalReferences"]
      case 5:
        return [] // Payment step - no form validation needed
      default:
        return []
    }
  }

  const handlePayment = async () => {
    setPaymentLoading(true)
    try {
      const response = await fetch("/api/payments/application-fee", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          petId: params.petId,
          paymentMethod: "stripe"
        }),
      })

      if (response.ok) {
        const data = await response.json()
        setPaymentCompleted(true)
        toast.success("Payment successful! You can now proceed to review your application.")
        setCurrentStep(prev => prev + 1)
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || "Payment failed. Please try again.")
      }
    } catch (error) {
      toast.error("Payment failed. Please try again.")
    } finally {
      setPaymentLoading(false)
    }
  }

  const onSubmit = async (data: ApplicationInput) => {
    if (!paymentCompleted) {
      toast.error("Payment must be completed before submitting the application.")
      return
    }
    
    setLoading(true)
    try {
      const response = await fetch("/api/applications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        const result = await response.json()
        toast.success("Application submitted successfully!")
        // Redirect to success page with application details
        router.push(`/apply/success?applicationId=${result.applicationId || 'APP-' + Date.now()}&petName=${encodeURIComponent(pet?.name || 'your pet')}`)
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || "Failed to submit application")
      }
    } catch (error) {
      toast.error("Something went wrong. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  if (status === "loading") {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!pet) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Pet Not Found</h1>
        <p className="text-gray-600 mb-4">The pet you're trying to apply for doesn't exist.</p>
        <Link href="/pets">
          <Button>Browse Other Pets</Button>
        </Link>
      </div>
    )
  }

  const formatAge = (ageInMonths: number) => {
    if (ageInMonths < 12) {
      return `${ageInMonths} month${ageInMonths !== 1 ? 's' : ''} old`
    } else {
      const years = Math.floor(ageInMonths / 12)
      const months = ageInMonths % 12
      if (months === 0) {
        return `${years} year${years !== 1 ? 's' : ''} old`
      } else {
        return `${years}y ${months}m old`
      }
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Button */}
      <Link href={`/pets/${pet.id}`} className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-6">
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to {pet.name}'s Profile
      </Link>

      {/* Pet Summary */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="relative w-20 h-20 rounded-lg overflow-hidden">
              {pet.photos.length > 0 ? (
                <Image
                  src={pet.photos.find(p => p.isPrimary)?.url || pet.photos[0].url}
                  alt={pet.name}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-2xl">🐾</span>
                </div>
              )}
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Adoption Application for {pet.name}
              </h1>
              <p className="text-gray-600">
                {pet.breed} • {formatAge(pet.age)} • {pet.organization.city}, {pet.organization.state}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {STEPS.map((step, index) => {
            const Icon = step.icon
            const isActive = currentStep === step.id
            const isCompleted = currentStep > step.id
            
            return (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  isCompleted 
                    ? 'bg-green-500 border-green-500 text-white' 
                    : isActive 
                    ? 'bg-blue-500 border-blue-500 text-white' 
                    : 'bg-white border-gray-300 text-gray-400'
                }`}>
                  {isCompleted ? (
                    <Check className="h-5 w-5" />
                  ) : (
                    <Icon className="h-5 w-5" />
                  )}
                </div>
                <div className="ml-3 hidden sm:block">
                  <p className={`text-sm font-medium ${
                    isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </p>
                  <p className="text-xs text-gray-500">{step.description}</p>
                </div>
                {index < STEPS.length - 1 && (
                  <div className={`hidden sm:block w-16 h-0.5 ml-4 ${
                    isCompleted ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            )
          })}
        </div>
      </div>

      {/* Application Form */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <Card>
          <CardHeader>
            <CardTitle>{STEPS[currentStep - 1].title}</CardTitle>
            <CardDescription>{STEPS[currentStep - 1].description}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name *
                    </label>
                    <Input
                      {...register("applicantName")}
                      placeholder="Enter your full name"
                    />
                    {errors.applicantName && (
                      <p className="text-red-500 text-sm mt-1">{errors.applicantName.message}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address *
                    </label>
                    <Input
                      type="email"
                      {...register("applicantEmail")}
                      placeholder="Enter your email"
                    />
                    {errors.applicantEmail && (
                      <p className="text-red-500 text-sm mt-1">{errors.applicantEmail.message}</p>
                    )}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number *
                  </label>
                  <Input
                    type="tel"
                    {...register("applicantPhone")}
                    placeholder="Enter your phone number"
                  />
                  {errors.applicantPhone && (
                    <p className="text-red-500 text-sm mt-1">{errors.applicantPhone.message}</p>
                  )}
                </div>
              </div>
            )}

            {/* Step 2: Housing Information */}
            {currentStep === 2 && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Housing Type *
                    </label>
                    <select
                      {...register("housingType")}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Select housing type</option>
                      <option value="house">House</option>
                      <option value="apartment">Apartment</option>
                      <option value="condo">Condo</option>
                      <option value="townhouse">Townhouse</option>
                      <option value="mobile_home">Mobile Home</option>
                      <option value="other">Other</option>
                    </select>
                    {errors.housingType && (
                      <p className="text-red-500 text-sm mt-1">{errors.housingType.message}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Do you own or rent? *
                    </label>
                    <select
                      {...register("ownOrRent")}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Select option</option>
                      <option value="own">Own</option>
                      <option value="rent">Rent</option>
                      <option value="live_with_family">Live with family</option>
                    </select>
                    {errors.ownOrRent && (
                      <p className="text-red-500 text-sm mt-1">{errors.ownOrRent.message}</p>
                    )}
                  </div>
                </div>

                {watchedValues.ownOrRent === "rent" && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Landlord Contact Information
                    </label>
                    <Input
                      {...register("landlordContact")}
                      placeholder="Landlord name and phone number"
                    />
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Household Size *
                    </label>
                    <Input
                      type="number"
                      min="1"
                      {...register("householdSize", { valueAsNumber: true })}
                      placeholder="Number of people"
                    />
                    {errors.householdSize && (
                      <p className="text-red-500 text-sm mt-1">{errors.householdSize.message}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Do you have a yard?
                    </label>
                    <select
                      {...register("hasYard", {
                        setValueAs: (value) => value === "true"
                      })}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Select option</option>
                      <option value="true">Yes</option>
                      <option value="false">No</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Is your yard fenced?
                    </label>
                    <select
                      {...register("yardFenced", {
                        setValueAs: (value) => value === "true"
                      })}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      disabled={!watchedValues.hasYard}
                    >
                      <option value="">Select option</option>
                      <option value="true">Yes</option>
                      <option value="false">No</option>
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Do you have children?
                    </label>
                    <select
                      {...register("hasChildren", {
                        setValueAs: (value) => value === "true"
                      })}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Select option</option>
                      <option value="true">Yes</option>
                      <option value="false">No</option>
                    </select>
                  </div>
                  {watchedValues.hasChildren && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Children's Ages
                      </label>
                      <Input
                        {...register("childrenAges")}
                        placeholder="e.g., 5, 8, 12"
                      />
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Do you have other pets?
                    </label>
                    <select
                      {...register("hasOtherPets", {
                        setValueAs: (value) => value === "true"
                      })}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Select option</option>
                      <option value="true">Yes</option>
                      <option value="false">No</option>
                    </select>
                  </div>
                  {watchedValues.hasOtherPets && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Tell us about your other pets
                      </label>
                      <textarea
                        {...register("otherPetsDetails")}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        rows={3}
                        placeholder="Species, breeds, ages, temperaments..."
                      />
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Step 3: Experience & Lifestyle */}
            {currentStep === 3 && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Pet Experience *
                  </label>
                  <textarea
                    {...register("petExperience")}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    rows={4}
                    placeholder="Tell us about your experience with pets. Have you owned pets before? What types?"
                  />
                  {errors.petExperience && (
                    <p className="text-red-500 text-sm mt-1">{errors.petExperience.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Previous Pets
                  </label>
                  <textarea
                    {...register("previousPets")}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    rows={3}
                    placeholder="Tell us about pets you've had in the past. What happened to them?"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Current Veterinarian
                    </label>
                    <Input
                      {...register("currentVeterinarian")}
                      placeholder="Veterinarian or clinic name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Vet Contact Information
                    </label>
                    <Input
                      {...register("vetContact")}
                      placeholder="Phone number or address"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Work Schedule *
                  </label>
                  <textarea
                    {...register("workSchedule")}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    rows={3}
                    placeholder="Describe your typical work schedule. How many hours per day will the pet be alone?"
                  />
                  {errors.workSchedule && (
                    <p className="text-red-500 text-sm mt-1">{errors.workSchedule.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Travel Frequency
                  </label>
                  <Input
                    {...register("travelFrequency")}
                    placeholder="How often do you travel? What arrangements would you make for your pet?"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Exercise Plans *
                  </label>
                  <textarea
                    {...register("exercisePlans")}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    rows={3}
                    placeholder="How do you plan to exercise and provide mental stimulation for your pet?"
                  />
                  {errors.exercisePlans && (
                    <p className="text-red-500 text-sm mt-1">{errors.exercisePlans.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Training Plans
                  </label>
                  <textarea
                    {...register("trainingPlans")}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    rows={3}
                    placeholder="What are your plans for training and socialization?"
                  />
                </div>
              </div>
            )}

            {/* Step 4: References */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Personal References</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Please provide at least 2 personal references (not family members).
                  </p>

                  {[0, 1].map((index) => (
                    <div key={index} className="border rounded-lg p-4 mb-4">
                      <h4 className="font-medium mb-3">Reference {index + 1}</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Name *
                          </label>
                          <Input
                            {...register(`personalReferences.${index}.name` as const)}
                            placeholder="Full name"
                          />
                          {errors.personalReferences?.[index]?.name && (
                            <p className="text-red-500 text-sm mt-1">
                              {errors.personalReferences[index]?.name?.message}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Phone Number *
                          </label>
                          <Input
                            type="tel"
                            {...register(`personalReferences.${index}.phone` as const)}
                            placeholder="Phone number"
                          />
                          {errors.personalReferences?.[index]?.phone && (
                            <p className="text-red-500 text-sm mt-1">
                              {errors.personalReferences[index]?.phone?.message}
                            </p>
                          )}
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Relationship *
                          </label>
                          <Input
                            {...register(`personalReferences.${index}.relationship` as const)}
                            placeholder="Friend, coworker, etc."
                          />
                          {errors.personalReferences?.[index]?.relationship && (
                            <p className="text-red-500 text-sm mt-1">
                              {errors.personalReferences[index]?.relationship?.message}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-4">Veterinary Reference</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    If you have had pets before, please provide your veterinarian's information.
                  </p>

                  <div className="border rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Clinic Name
                        </label>
                        <Input
                          {...register("veterinaryReference.clinicName")}
                          placeholder="Veterinary clinic name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone Number
                        </label>
                        <Input
                          type="tel"
                          {...register("veterinaryReference.phone")}
                          placeholder="Clinic phone number"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Last Visit
                        </label>
                        <Input
                          {...register("veterinaryReference.lastVisit")}
                          placeholder="Approximate date of last visit"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 5: Payment */}
            {currentStep === 5 && (
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-blue-800 mb-2">
                    Application Fee Required
                  </h3>
                  <p className="text-blue-700 mb-4">
                    A non-refundable application fee of <strong>$50</strong> is required to process your adoption application. 
                    This fee helps cover the costs of application review, background checks, and administrative processing.
                  </p>
                  
                  {paymentCompleted ? (
                    <div className="flex items-center space-x-2 text-green-700">
                      <Check className="h-5 w-5" />
                      <span className="font-medium">Payment completed successfully!</span>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <span className="font-medium">Application Fee</span>
                          <span className="text-lg font-bold">$50.00</span>
                        </div>
                        <div className="text-sm text-gray-600 mb-4">
                          <p>• Covers application processing and review</p>
                          <p>• Background check and reference verification</p>
                          <p>• Administrative costs</p>
                          <p className="text-red-600 font-medium mt-2">* Non-refundable</p>
                        </div>
                      </div>
                      
                      <Button
                        type="button"
                        onClick={handlePayment}
                        disabled={paymentLoading}
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        size="lg"
                      >
                        {paymentLoading ? (
                          "Processing Payment..."
                        ) : (
                          <span>
                            <CreditCard className="h-5 w-5 mr-2" />
                            Pay $50 Application Fee
                          </span>
                        )}
                      </Button>
                      
                      <p className="text-xs text-gray-500 text-center">
                        Secure payment processing. Your payment information is encrypted and secure.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Step 6: Review */}
            {currentStep === 6 && (
              <div className="space-y-6">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-yellow-800 mb-2">
                    Review Your Application
                  </h3>
                  <p className="text-yellow-700">
                    Please review all information below before submitting your application.
                  </p>
                </div>

                {/* Personal Information Summary */}
                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-3">Personal Information</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Name:</span> {watchedValues.applicantName}
                    </div>
                    <div>
                      <span className="font-medium">Email:</span> {watchedValues.applicantEmail}
                    </div>
                    <div>
                      <span className="font-medium">Phone:</span> {watchedValues.applicantPhone}
                    </div>
                  </div>
                </div>

                {/* Housing Information Summary */}
                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-3">Housing Information</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Housing Type:</span> {watchedValues.housingType}
                    </div>
                    <div>
                      <span className="font-medium">Own/Rent:</span> {watchedValues.ownOrRent}
                    </div>
                    <div>
                      <span className="font-medium">Household Size:</span> {watchedValues.householdSize}
                    </div>
                    <div>
                      <span className="font-medium">Has Yard:</span> {watchedValues.hasYard ? 'Yes' : 'No'}
                    </div>
                  </div>
                </div>

                {/* Payment Confirmation */}
                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-3">Payment Information</h4>
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="font-medium">Application Fee:</span>
                      <span className="ml-2">$50.00</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {paymentCompleted ? (
                        <span>
                          <Check className="h-5 w-5 text-green-600" />
                          <span className="text-green-600 font-medium">Paid</span>
                        </span>
                      ) : (
                        <span>
                          <span className="text-red-600 font-medium">Payment Required</span>
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Agreement Checkboxes */}
                <div className="space-y-4">
                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      {...register("agreesToTerms")}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                    />
                    <label className="ml-2 block text-sm text-gray-900">
                      I agree to the{" "}
                      <Link href="/terms" className="text-blue-600 hover:text-blue-500">
                        Terms and Conditions
                      </Link>{" "}
                      and understand that providing false information may result in denial of my application.
                    </label>
                  </div>
                  {errors.agreesToTerms && (
                    <p className="text-red-500 text-sm">{errors.agreesToTerms.message}</p>
                  )}

                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      {...register("agreesToHomeVisit")}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                    />
                    <label className="ml-2 block text-sm text-gray-900">
                      I agree to a home visit as part of the adoption process and understand that this is required for approval.
                    </label>
                  </div>
                  {errors.agreesToHomeVisit && (
                    <p className="text-red-500 text-sm">{errors.agreesToHomeVisit.message}</p>
                  )}
                </div>
              </div>
            )}
          </CardContent>

          {/* Navigation Buttons */}
          <div className="flex justify-between items-center p-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 1}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <div className="text-sm text-gray-500">
              Step {currentStep} of {STEPS.length}
            </div>

            {currentStep < STEPS.length ? (
              <Button
                type="button"
                onClick={nextStep}
                className="flex items-center"
              >
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button
                type="submit"
                disabled={loading}
                className="flex items-center"
              >
                {loading ? (
                  "Submitting..."
                ) : (
                  <span>
                    Submit Application
                    <Send className="h-4 w-4 ml-2" />
                  </span>
                )}
              </Button>
            )}
          </div>
        </Card>
      </form>
    </div>
  )
}
