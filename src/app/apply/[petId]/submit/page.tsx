"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON>er } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  PawPrint, 
  Check, 
  MessageSquare, 
  Calendar,
  ArrowLeft 
} from "lucide-react"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { toast } from "react-hot-toast"

interface Pet {
  id: string
  name: string
  species: string
  breed: string
  photos: Array<{
    id: string
    url: string
    isPrimary: boolean
  }>
}

interface Application {
  id: string
  status: string
  submittedAt: string
  pet: Pet
}

export default function ApplicationSubmitted() {
  const params = useParams()
  const router = useRouter()
  const [application, setApplication] = useState<Application | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchApplication = async () => {
      try {
        const response = await fetch(`/api/applications/${params.petId}`)
        if (response.ok) {
          const data = await response.json()
          setApplication(data.application)
        } else {
          toast.error("Failed to fetch application details")
        }
      } catch (error) {
        console.error("Error fetching application:", error)
        toast.error("Something went wrong")
      } finally {
        setLoading(false)
      }
    }

    if (params.petId) {
      fetchApplication()
    }
  }, [params.petId])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-pink-50 to-white py-12">
        <div className="max-w-3xl mx-auto px-4">
          <Card>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-50 to-white py-12">
      <div className="max-w-3xl mx-auto px-4">
        <Card>
          <CardContent className="p-6">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-green-600" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Application Submitted Successfully
              </h1>
              <p className="text-gray-600">
                Thank you for applying to adopt {application?.pet.name}! We'll review your application and get back to you soon.
              </p>
            </div>

            {application && (
              <div className="space-y-6">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    {application.pet.photos.find(p => p.isPrimary)?.url ? (
                      <img
                        src={application.pet.photos.find(p => p.isPrimary)?.url}
                        alt={application.pet.name}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                        <PawPrint className="h-6 w-6 text-gray-400" />
                      </div>
                    )}
                    <div>
                      <h3 className="font-semibold text-gray-900">{application.pet.name}</h3>
                      <p className="text-sm text-gray-600">
                        {application.pet.breed} • {application.pet.species}
                      </p>
                    </div>
                  </div>
                  <Badge variant="outline">Application ID: {application.id.slice(-8)}</Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <h4 className="font-medium text-gray-900 mb-2">Next Steps</h4>
                      <ul className="space-y-3 text-sm text-gray-600">
                        <li className="flex items-start">
                          <Check className="h-4 w-4 text-green-600 mt-1 mr-2 flex-shrink-0" />
                          <span>We'll review your application within 1-2 business days</span>
                        </li>
                        <li className="flex items-start">
                          <Check className="h-4 w-4 text-green-600 mt-1 mr-2 flex-shrink-0" />
                          <span>You'll receive updates via email about your application status</span>
                        </li>
                        <li className="flex items-start">
                          <Check className="h-4 w-4 text-green-600 mt-1 mr-2 flex-shrink-0" />
                          <span>If approved, we'll schedule a meet & greet with {application.pet.name}</span>
                        </li>
                      </ul>
                    </CardContent>
                  </Card>

                  <div className="space-y-3">
                    <Button variant="outline" className="w-full" asChild>
                      <Link href="/dashboard/applications">
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        View All Applications
                      </Link>
                    </Button>
                    <Button variant="outline" className="w-full" asChild>
                      <Link href="/contact-shelter">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Contact Shelter
                      </Link>
                    </Button>
                    <Button variant="outline" className="w-full" asChild>
                      <Link href="/schedule-visit">
                        <Calendar className="h-4 w-4 mr-2" />
                        Schedule Visit
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
