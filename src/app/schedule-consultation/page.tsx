"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
// import { Calendar } from "@/components/ui/calendar"
// import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { toast } from "react-hot-toast"
import { 
  Calendar as CalendarIcon, 
  Clock, 
  Video, 
  Phone, 
  MapPin,
  CheckCircle,
  User,
  Stethoscope,
  GraduationCap,
  Star,
  DollarSign
} from "lucide-react"
import { motion } from "framer-motion"
// import { format } from "date-fns"
import { cn } from "@/lib/utils"

const consultationSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(1, "Phone number is required"),
  petName: z.string().optional(),
  petType: z.string().min(1, "Please select a pet type"),
  consultationType: z.string().min(1, "Please select consultation type"),
  expertType: z.string().min(1, "Please select expert type"),
  date: z.string().min(1, "Please select a date"),
  timeSlot: z.string().min(1, "Please select a time slot"),
  concerns: z.string().min(10, "Please describe your concerns (minimum 10 characters)"),
  previousVet: z.string().optional(),
})

type ConsultationFormData = z.infer<typeof consultationSchema>

const petTypes = [
  { value: "dog", label: "Dog" },
  { value: "cat", label: "Cat" },
  { value: "bird", label: "Bird" },
  { value: "rabbit", label: "Rabbit" },
  { value: "reptile", label: "Reptile" },
  { value: "other", label: "Other" }
]

const consultationTypes = [
  { 
    value: "video", 
    label: "Video Consultation", 
    icon: Video, 
    price: "$75",
    duration: "30 min",
    description: "Face-to-face consultation via video call"
  },
  { 
    value: "phone", 
    label: "Phone Consultation", 
    icon: Phone, 
    price: "$50",
    duration: "20 min",
    description: "Voice consultation over the phone"
  },
  { 
    value: "in-person", 
    label: "In-Person Visit", 
    icon: MapPin, 
    price: "$100",
    duration: "45 min",
    description: "Visit our facility for hands-on consultation"
  }
]

const expertTypes = [
  { value: "veterinarian", label: "Veterinarian", specialties: ["Health", "Medical", "Surgery"] },
  { value: "behaviorist", label: "Animal Behaviorist", specialties: ["Training", "Behavior", "Aggression"] },
  { value: "nutritionist", label: "Pet Nutritionist", specialties: ["Diet", "Weight", "Allergies"] },
  { value: "trainer", label: "Professional Trainer", specialties: ["Obedience", "Tricks", "Socialization"] }
]

const timeSlots = [
  "9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM",
  "1:00 PM", "1:30 PM", "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM",
  "4:00 PM", "4:30 PM", "5:00 PM"
]

const experts = [
  {
    name: "Dr. Sarah Johnson",
    title: "Senior Veterinarian",
    rating: 4.9,
    reviews: 127,
    specialties: ["Internal Medicine", "Surgery", "Emergency Care"],
    availability: "Mon-Fri"
  },
  {
    name: "Mike Rodriguez",
    title: "Certified Animal Behaviorist",
    rating: 4.8,
    reviews: 89,
    specialties: ["Dog Training", "Behavior Modification", "Aggression"],
    availability: "Tue-Sat"
  },
  {
    name: "Dr. Emily Chen",
    title: "Pet Nutritionist",
    rating: 4.9,
    reviews: 156,
    specialties: ["Diet Planning", "Weight Management", "Special Diets"],
    availability: "Mon-Thu"
  }
]

export default function ScheduleConsultationPage() {
  const [loading, setLoading] = useState(false)
  
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<ConsultationFormData>({
    resolver: zodResolver(consultationSchema),
  })

  const selectedConsultationType = watch("consultationType")
  const selectedExpertType = watch("expertType")

  const onSubmit = async (data: ConsultationFormData) => {
    setLoading(true)
    try {
      const response = await fetch("/api/schedule-consultation", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        toast.success("Consultation scheduled successfully! You'll receive a confirmation email shortly.")
        reset()
      } else {
        const error = await response.json()
        toast.error(error.message || "Failed to schedule consultation")
      }
    } catch (error) {
      console.error("Error scheduling consultation:", error)
      toast.error("Something went wrong. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const selectedConsultationTypeData = consultationTypes.find(
    type => type.value === selectedConsultationType
  )

  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-50 to-white py-12">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Schedule Expert Consultation
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Book a personalized consultation with our certified pet care experts. 
            Get professional advice tailored to your pet's specific needs.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Consultation Form */}
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-2"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CalendarIcon className="h-5 w-5 mr-2" />
                  Book Your Consultation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* Personal Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-900">Personal Information</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Your Name *</Label>
                        <Input
                          id="name"
                          {...register("name")}
                          error={errors.name?.message}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address *</Label>
                        <Input
                          id="email"
                          type="email"
                          {...register("email")}
                          error={errors.email?.message}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number *</Label>
                      <Input
                        id="phone"
                        type="tel"
                        {...register("phone")}
                        error={errors.phone?.message}
                      />
                    </div>
                  </div>

                  {/* Pet Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-900">Pet Information</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="petName">Pet Name (Optional)</Label>
                        <Input
                          id="petName"
                          {...register("petName")}
                          placeholder="Your pet's name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="petType">Pet Type *</Label>
                        <Select onValueChange={(value) => setValue("petType", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select pet type" />
                          </SelectTrigger>
                          <SelectContent>
                            {petTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.petType && (
                          <p className="text-sm text-red-600">{errors.petType.message}</p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Consultation Type */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-900">Consultation Type</h3>
                    <div className="grid gap-4">
                      {consultationTypes.map((type) => (
                        <div
                          key={type.value}
                          className={cn(
                            "border rounded-lg p-4 cursor-pointer transition-all",
                            selectedConsultationType === type.value
                              ? "border-purple-500 bg-purple-50"
                              : "border-gray-200 hover:border-gray-300"
                          )}
                          onClick={() => setValue("consultationType", type.value)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <type.icon className="h-5 w-5 text-purple-600" />
                              <div>
                                <h4 className="font-medium text-gray-900">{type.label}</h4>
                                <p className="text-sm text-gray-600">{type.description}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold text-purple-600">{type.price}</div>
                              <div className="text-sm text-gray-500">{type.duration}</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    {errors.consultationType && (
                      <p className="text-sm text-red-600">{errors.consultationType.message}</p>
                    )}
                  </div>

                  {/* Expert Type */}
                  <div className="space-y-2">
                    <Label htmlFor="expertType">Expert Type *</Label>
                    <Select onValueChange={(value) => setValue("expertType", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select expert type" />
                      </SelectTrigger>
                      <SelectContent>
                        {expertTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            <div>
                              <div className="font-medium">{type.label}</div>
                              <div className="text-sm text-gray-500">
                                {type.specialties.join(", ")}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.expertType && (
                      <p className="text-sm text-red-600">{errors.expertType.message}</p>
                    )}
                  </div>

                  {/* Date and Time */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-900">Date & Time</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="date">Select Date *</Label>
                        <Input
                          id="date"
                          type="date"
                          {...register("date", {
                            required: "Please select a date",
                            validate: (value) => {
                              const selectedDate = new Date(value)
                              const today = new Date()
                              today.setHours(0, 0, 0, 0)
                              return selectedDate >= today || "Please select a future date"
                            }
                          })}
                          min={new Date().toISOString().split('T')[0]}
                          className="w-full"
                        />
                        {errors.date && (
                          <p className="text-sm text-red-600">{errors.date.message}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="timeSlot">Time Slot *</Label>
                        <Select onValueChange={(value) => setValue("timeSlot", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select time" />
                          </SelectTrigger>
                          <SelectContent>
                            {timeSlots.map((time) => (
                              <SelectItem key={time} value={time}>
                                {time}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.timeSlot && (
                          <p className="text-sm text-red-600">{errors.timeSlot.message}</p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Concerns */}
                  <div className="space-y-2">
                    <Label htmlFor="concerns">Describe Your Concerns *</Label>
                    <Textarea
                      id="concerns"
                      {...register("concerns")}
                      rows={4}
                      placeholder="Please describe what you'd like to discuss during the consultation..."
                    />
                    {errors.concerns && (
                      <p className="text-sm text-red-600">{errors.concerns.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="previousVet">Previous Veterinarian (Optional)</Label>
                    <Input
                      id="previousVet"
                      {...register("previousVet")}
                      placeholder="Name of your current/previous vet"
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={loading}
                    className="w-full bg-purple-600 hover:bg-purple-700"
                  >
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    {loading ? "Scheduling..." : "Schedule Consultation"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>

          {/* Sidebar Information */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Selected Consultation Summary */}
            {selectedConsultationTypeData && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                    Selected Service
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <selectedConsultationTypeData.icon className="h-6 w-6 text-purple-600" />
                      <div>
                        <h4 className="font-medium">{selectedConsultationTypeData.label}</h4>
                        <p className="text-sm text-gray-600">{selectedConsultationTypeData.description}</p>
                      </div>
                    </div>
                    <div className="flex justify-between items-center pt-2 border-t">
                      <span className="text-gray-600">Price:</span>
                      <span className="font-semibold text-purple-600">{selectedConsultationTypeData.price}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Duration:</span>
                      <span className="font-medium">{selectedConsultationTypeData.duration}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Available Experts */}
            <Card>
              <CardHeader>
                <CardTitle>Available Experts</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {experts.map((expert, index) => (
                  <div key={index} className="border rounded-lg p-3">
                    <div className="flex items-start space-x-3">
                      <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                        <GraduationCap className="h-6 w-6 text-purple-600" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{expert.name}</h4>
                        <p className="text-sm text-gray-600">{expert.title}</p>
                        <div className="flex items-center space-x-1 mt-1">
                          <Star className="h-3 w-3 text-yellow-400 fill-current" />
                          <span className="text-xs text-gray-500">{expert.rating}</span>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-500">{expert.reviews} reviews</span>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">Available: {expert.availability}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* What to Expect */}
            <Card>
              <CardHeader>
                <CardTitle>What to Expect</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-purple-600 font-semibold text-xs">1</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Confirmation</h4>
                    <p className="text-sm text-gray-600">You'll receive email confirmation with meeting details</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-purple-600 font-semibold text-xs">2</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Preparation</h4>
                    <p className="text-sm text-gray-600">Prepare questions and any relevant pet information</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-purple-600 font-semibold text-xs">3</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Consultation</h4>
                    <p className="text-sm text-gray-600">Expert advice tailored to your pet's needs</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-purple-600 font-semibold text-xs">4</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Follow-up</h4>
                    <p className="text-sm text-gray-600">Receive summary and recommendations via email</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
