"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock,
  MessageCircle,
  Send,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "react-hot-toast"

const contactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Valid email is required"),
  phone: z.string().optional(),
  subject: z.string().min(1, "Subject is required"),
  message: z.string().min(10, "Message must be at least 10 characters"),
  inquiryType: z.string().min(1, "Please select an inquiry type"),
})

type ContactInput = z.infer<typeof contactSchema>

const INQUIRY_TYPES = [
  { value: "adoption", label: "Adoption Inquiry" },
  { value: "volunteer", label: "Volunteer Opportunities" },
  { value: "foster", label: "Foster Program" },
  { value: "donation", label: "Donations" },
  { value: "lost-pet", label: "Lost Pet" },
  { value: "surrender", label: "Pet Surrender" },
  { value: "general", label: "General Question" },
  { value: "media", label: "Media Inquiry" },
]

const CONTACT_INFO = [
  {
    icon: MapPin,
    title: "Visit Us",
    details: [
      "123 Pet Adoption Lane",
      "Compassion City, CC 12345",
      "United States"
    ],
    color: "bg-blue-100 text-blue-600"
  },
  {
    icon: Phone,
    title: "Call Us",
    details: [
      "Main: (555) 123-PETS",
      "Emergency: (555) 911-HELP",
      "Volunteer: (555) 456-HELP"
    ],
    color: "bg-green-100 text-green-600"
  },
  {
    icon: Mail,
    title: "Email Us",
    details: [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ],
    color: "bg-purple-100 text-purple-600"
  },
  {
    icon: Clock,
    title: "Hours",
    details: [
      "Mon-Fri: 9:00 AM - 6:00 PM",
      "Saturday: 10:00 AM - 4:00 PM",
      "Sunday: 12:00 PM - 4:00 PM"
    ],
    color: "bg-orange-100 text-orange-600"
  }
]

const FAQ_ITEMS = [
  {
    question: "How do I adopt a pet?",
    answer: "Start by browsing our available pets online, then submit an adoption application. Our team will review your application and contact you to schedule a meet-and-greet."
  },
  {
    question: "What are the adoption fees?",
    answer: "Adoption fees vary by animal type and age. Dogs typically range from $150-$300, cats from $100-$200. All pets are spayed/neutered, vaccinated, and microchipped."
  },
  {
    question: "Can I volunteer if I'm under 18?",
    answer: "Yes! Volunteers aged 16-17 can participate with parental consent. We have various age-appropriate volunteer opportunities available."
  },
  {
    question: "Do you accept pet surrenders?",
    answer: "We accept surrenders on a case-by-case basis depending on our capacity. Please call us to discuss your situation and explore all options."
  },
  {
    question: "How can I donate?",
    answer: "You can donate online through our website, mail a check, or drop off supplies during our business hours. We also accept vehicle donations."
  }
]

export default function ContactPage() {
  const [loading, setLoading] = useState(false)
  const [submitted, setSubmitted] = useState(false)

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ContactInput>({
    resolver: zodResolver(contactSchema),
  })

  const onSubmit = async (data: ContactInput) => {
    setLoading(true)
    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        setSubmitted(true)
        reset()
        toast.success("Message sent successfully! We'll get back to you soon.")
      } else {
        toast.error("Failed to send message. Please try again.")
      }
    } catch (error) {
      toast.error("Something went wrong. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-gradient-to-br from-blue-50 to-purple-100 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Contact Us
            </h1>
            <p className="text-xl text-gray-600">
              We're here to help! Reach out with any questions about adoption, volunteering, or our services.
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Contact Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageCircle className="h-5 w-5 mr-2" />
                  Send us a Message
                </CardTitle>
                <CardDescription>
                  Fill out the form below and we'll get back to you as soon as possible.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {submitted ? (
                  <div className="text-center py-8">
                    <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      Message Sent Successfully!
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Thank you for contacting us. We'll respond within 24 hours.
                    </p>
                    <Button onClick={() => setSubmitted(false)}>
                      Send Another Message
                    </Button>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Name *
                        </label>
                        <Input
                          {...register("name")}
                          placeholder="Your full name"
                        />
                        {errors.name && (
                          <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                        )}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Email *
                        </label>
                        <Input
                          type="email"
                          {...register("email")}
                          placeholder="<EMAIL>"
                        />
                        {errors.email && (
                          <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
                        )}
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone (Optional)
                        </label>
                        <Input
                          type="tel"
                          {...register("phone")}
                          placeholder="(*************"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Inquiry Type *
                        </label>
                        <select
                          {...register("inquiryType")}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="">Select inquiry type</option>
                          {INQUIRY_TYPES.map((type) => (
                            <option key={type.value} value={type.value}>
                              {type.label}
                            </option>
                          ))}
                        </select>
                        {errors.inquiryType && (
                          <p className="text-red-500 text-sm mt-1">{errors.inquiryType.message}</p>
                        )}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Subject *
                      </label>
                      <Input
                        {...register("subject")}
                        placeholder="Brief description of your inquiry"
                      />
                      {errors.subject && (
                        <p className="text-red-500 text-sm mt-1">{errors.subject.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Message *
                      </label>
                      <textarea
                        {...register("message")}
                        className="w-full p-3 border border-gray-300 rounded-md"
                        rows={6}
                        placeholder="Please provide details about your inquiry..."
                      />
                      {errors.message && (
                        <p className="text-red-500 text-sm mt-1">{errors.message.message}</p>
                      )}
                    </div>

                    <Button
                      type="submit"
                      disabled={loading}
                      className="w-full bg-blue-600 hover:bg-blue-700"
                      size="lg"
                    >
                      {loading ? (
                        "Sending..."
                      ) : (
                        <span>
                          <Send className="h-4 w-4 mr-2" />
                          Send Message
                        </span>
                      )}
                    </Button>
                  </form>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Contact Information */}
          <div className="space-y-6">
            {CONTACT_INFO.map((info, index) => {
              const Icon = info.icon
              return (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg ${info.color} mr-3`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <CardTitle className="text-lg">{info.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {info.details.map((detail, idx) => (
                      <p key={idx} className="text-gray-600">
                        {detail}
                      </p>
                    ))}
                  </CardContent>
                </Card>
              )
            })}

            {/* Emergency Notice */}
            <Card className="border-red-200 bg-red-50">
              <CardHeader>
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                  <CardTitle className="text-red-800">Emergency?</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-red-700 text-sm">
                  If you've found an injured animal or have an emergency, 
                  please call our emergency line at (555) 911-HELP immediately.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* FAQ Section */}
        <section className="mt-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600">
              Quick answers to common questions
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            {FAQ_ITEMS.map((faq, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-lg">{faq.question}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Map Section */}
        <section className="mt-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Visit Our Facility
            </h2>
            <p className="text-xl text-gray-600">
              Come meet our amazing animals in person
            </p>
          </div>

          <Card>
            <CardContent className="p-0">
              <div className="aspect-video bg-gray-200 flex items-center justify-center">
                <div className="text-center">
                  <MapPin className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Interactive map would be displayed here</p>
                  <p className="text-sm text-gray-500 mt-2">
                    123 Pet Adoption Lane, Compassion City, CC 12345
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}
