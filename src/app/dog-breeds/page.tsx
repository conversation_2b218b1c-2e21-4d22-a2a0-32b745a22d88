"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  Search,
  Dog,
  Heart,
  Star,
  Users,
  Home,
  Activity,
  Shield,
  Filter
} from "lucide-react"
import { motion } from "framer-motion"

interface DogBreed {
  id: string
  name: string
  size: "Small" | "Medium" | "Large" | "Extra Large"
  energyLevel: number
  goodWithKids: number
  goodWithPets: number
  groomingNeeds: number
  trainability: number
  description: string
  characteristics: string[]
  lifespan: string
  weight: string
  origin: string
  image?: string
}

const dogBreeds: DogBreed[] = [
  {
    id: "golden-retriever",
    name: "Golden Retriever",
    size: "Large",
    energyLevel: 4,
    goodWithKids: 5,
    goodWithPets: 4,
    groomingNeeds: 3,
    trainability: 5,
    description: "Friendly, intelligent, and devoted. Golden Retrievers are among America's most popular dog breeds.",
    characteristics: ["Friendly", "Intelligent", "Devoted", "Active", "Patient"],
    lifespan: "10-12 years",
    weight: "55-75 lbs",
    origin: "Scotland"
  },
  {
    id: "labrador-retriever",
    name: "Labrador Retriever",
    size: "Large",
    energyLevel: 5,
    goodWithKids: 5,
    goodWithPets: 4,
    groomingNeeds: 2,
    trainability: 5,
    description: "Labs are friendly, outgoing, and active companions who have more than enough affection to go around.",
    characteristics: ["Outgoing", "Even Tempered", "Gentle", "Agile", "Kind"],
    lifespan: "10-12 years",
    weight: "55-80 lbs",
    origin: "Canada"
  },
  {
    id: "german-shepherd",
    name: "German Shepherd",
    size: "Large",
    energyLevel: 4,
    goodWithKids: 4,
    goodWithPets: 3,
    groomingNeeds: 3,
    trainability: 5,
    description: "Confident, courageous, and smart working dogs that are extremely versatile.",
    characteristics: ["Confident", "Courageous", "Smart", "Loyal", "Versatile"],
    lifespan: "9-13 years",
    weight: "50-90 lbs",
    origin: "Germany"
  },
  {
    id: "french-bulldog",
    name: "French Bulldog",
    size: "Small",
    energyLevel: 2,
    goodWithKids: 4,
    goodWithPets: 3,
    groomingNeeds: 2,
    trainability: 3,
    description: "Playful, adaptable, and smart. French Bulldogs get along well with other animals and enjoy making new friends.",
    characteristics: ["Playful", "Adaptable", "Smart", "Affectionate", "Alert"],
    lifespan: "10-12 years",
    weight: "16-28 lbs",
    origin: "France"
  },
  {
    id: "bulldog",
    name: "Bulldog",
    size: "Medium",
    energyLevel: 2,
    goodWithKids: 4,
    goodWithPets: 3,
    groomingNeeds: 2,
    trainability: 2,
    description: "Docile, willful, and friendly. Bulldogs are gentle and predictable, excellent with children.",
    characteristics: ["Docile", "Willful", "Friendly", "Gentle", "Predictable"],
    lifespan: "8-10 years",
    weight: "40-50 lbs",
    origin: "England"
  },
  {
    id: "poodle",
    name: "Poodle",
    size: "Medium",
    energyLevel: 4,
    goodWithKids: 4,
    goodWithPets: 4,
    groomingNeeds: 5,
    trainability: 5,
    description: "Intelligent, active, and elegant dogs with wonderful personalities. They come in three sizes.",
    characteristics: ["Intelligent", "Active", "Alert", "Trainable", "Instinctual"],
    lifespan: "12-15 years",
    weight: "45-70 lbs",
    origin: "Germany"
  },
  {
    id: "beagle",
    name: "Beagle",
    size: "Medium",
    energyLevel: 4,
    goodWithKids: 5,
    goodWithPets: 4,
    groomingNeeds: 2,
    trainability: 3,
    description: "Loving, lovable, and happy. Beagles are excellent hunting dogs and loyal companions.",
    characteristics: ["Loving", "Lovable", "Happy", "Curious", "Friendly"],
    lifespan: "12-15 years",
    weight: "20-30 lbs",
    origin: "England"
  },
  {
    id: "rottweiler",
    name: "Rottweiler",
    size: "Large",
    energyLevel: 3,
    goodWithKids: 3,
    goodWithPets: 2,
    groomingNeeds: 2,
    trainability: 4,
    description: "Loyal, loving, and confident guardians. Rottweilers are calm, confident, and courageous.",
    characteristics: ["Loyal", "Loving", "Confident", "Calm", "Courageous"],
    lifespan: "8-10 years",
    weight: "80-135 lbs",
    origin: "Germany"
  }
]

export default function DogBreedsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedSize, setSelectedSize] = useState("all")
  const [sortBy, setSortBy] = useState("name")

  const filteredBreeds = dogBreeds
    .filter(breed => 
      breed.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
      (selectedSize === "all" || breed.size === selectedSize)
    )
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name)
        case "size":
          const sizeOrder = { "Small": 1, "Medium": 2, "Large": 3, "Extra Large": 4 }
          return sizeOrder[a.size] - sizeOrder[b.size]
        case "energy":
          return b.energyLevel - a.energyLevel
        default:
          return 0
      }
    })

  const renderStars = (rating: number) => (
    <div className="flex">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`w-4 h-4 ${
            star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
          }`}
        />
      ))}
    </div>
  )

  const getSizeColor = (size: string) => {
    switch (size) {
      case "Small": return "bg-green-100 text-green-800"
      case "Medium": return "bg-blue-100 text-blue-800"
      case "Large": return "bg-orange-100 text-orange-800"
      case "Extra Large": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Dog Breed
              <span className="block text-blue-300">Guide</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-purple-100">
              Discover the perfect dog breed for your lifestyle and family
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search dog breeds..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="flex gap-2">
                <select
                  value={selectedSize}
                  onChange={(e) => setSelectedSize(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Sizes</option>
                  <option value="Small">Small</option>
                  <option value="Medium">Medium</option>
                  <option value="Large">Large</option>
                  <option value="Extra Large">Extra Large</option>
                </select>
                
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="name">Sort by Name</option>
                  <option value="size">Sort by Size</option>
                  <option value="energy">Sort by Energy Level</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Breed Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredBreeds.map((breed, index) => (
            <motion.div
              key={breed.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-200">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-xl mb-2">{breed.name}</CardTitle>
                      <div className="flex gap-2 mb-2">
                        <Badge className={getSizeColor(breed.size)}>
                          {breed.size}
                        </Badge>
                        <Badge variant="outline">
                          {breed.origin}
                        </Badge>
                      </div>
                    </div>
                    <Dog className="w-8 h-8 text-blue-600" />
                  </div>
                  <CardDescription className="text-sm">
                    {breed.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent>
                  <div className="space-y-4">
                    {/* Characteristics */}
                    <div>
                      <h4 className="font-medium text-sm text-gray-700 mb-2">Characteristics</h4>
                      <div className="flex flex-wrap gap-1">
                        {breed.characteristics.slice(0, 3).map((char) => (
                          <Badge key={char} variant="secondary" className="text-xs">
                            {char}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    {/* Ratings */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Energy Level</span>
                        {renderStars(breed.energyLevel)}
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Good with Kids</span>
                        {renderStars(breed.goodWithKids)}
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Trainability</span>
                        {renderStars(breed.trainability)}
                      </div>
                    </div>
                    
                    {/* Quick Facts */}
                    <div className="pt-4 border-t text-sm text-gray-600">
                      <div className="flex justify-between">
                        <span>Weight:</span>
                        <span>{breed.weight}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Lifespan:</span>
                        <span>{breed.lifespan}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {filteredBreeds.length === 0 && (
          <div className="text-center py-12">
            <Dog className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No breeds found</h3>
            <p className="text-gray-500">Try adjusting your search criteria</p>
          </div>
        )}

        {/* Info Section */}
        <Card className="mt-12">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Heart className="w-5 h-5 mr-2 text-red-500" />
              Finding the Right Breed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Consider Your Lifestyle</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Activity level and exercise needs</li>
                  <li>• Living space (apartment vs. house)</li>
                  <li>• Time available for training and grooming</li>
                  <li>• Experience with dogs</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Family Considerations</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Children in the household</li>
                  <li>• Other pets</li>
                  <li>• Allergies to consider</li>
                  <li>• Long-term commitment (10+ years)</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
