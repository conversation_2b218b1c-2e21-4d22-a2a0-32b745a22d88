"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter, useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  CheckCircle, 
  Heart, 
  FileText, 
  CreditCard, 
  Calendar,
  User,
  Phone,
  Mail,
  MapPin,
  ArrowLeft,
  Download,
  AlertCircle
} from "lucide-react"
import Link from "next/link"
import { toast } from "react-hot-toast"

interface Pet {
  id: string
  name: string
  species: string
  breed: string
  age: number
  photos: Array<{ url: string; isPrimary: boolean }>
  organization: {
    name: string
    address: string
    city: string
    state: string
    phone: string
    email: string
  }
}

interface AdoptionData {
  applicationId: string
  adoptionFee: number
  status: string
}

export default function FinalizeAdoptionPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const petId = searchParams.get("petId")
  const applicationId = searchParams.get("applicationId")

  const [pet, setPet] = useState<Pet | null>(null)
  const [adoptionData, setAdoptionData] = useState<AdoptionData | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [step, setStep] = useState(1)

  const [formData, setFormData] = useState({
    emergencyContact: {
      name: "",
      phone: "",
      relationship: ""
    },
    veterinarian: {
      name: "",
      phone: "",
      address: ""
    },
    agreement: false,
    paymentMethod: "card"
  })

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
      return
    }

    if (status === "authenticated" && petId && applicationId) {
      fetchAdoptionData()
    }
  }, [status, petId, applicationId, router])

  const fetchAdoptionData = async () => {
    try {
      const [petResponse, adoptionResponse] = await Promise.all([
        fetch(`/api/pets/${petId}`),
        fetch(`/api/applications/${applicationId}`)
      ])

      if (petResponse.ok && adoptionResponse.ok) {
        const petData = await petResponse.json()
        const adoptionData = await adoptionResponse.json()
        
        setPet(petData.pet)
        setAdoptionData(adoptionData)
      } else {
        toast.error("Failed to load adoption information")
        router.push("/dashboard")
      }
    } catch (error) {
      console.error("Error fetching adoption data:", error)
      toast.error("Failed to load adoption information")
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (section: string, field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [field]: value
      }
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      const response = await fetch("/api/adoptions/finalize", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          applicationId,
          petId,
          ...formData
        }),
      })

      if (response.ok) {
        toast.success("Adoption finalized successfully! 🎉")
        router.push("/dashboard/adoptions")
      } else {
        const error = await response.json()
        toast.error(error.error || "Failed to finalize adoption")
      }
    } catch (error) {
      console.error("Error finalizing adoption:", error)
      toast.error("Failed to finalize adoption")
    } finally {
      setSubmitting(false)
    }
  }

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!pet || !adoptionData) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="text-center py-8">
            <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Adoption Not Found</h2>
            <p className="text-gray-600 mb-4">
              We couldn't find the adoption information you're looking for.
            </p>
            <Link href="/dashboard">
              <Button>Return to Dashboard</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/dashboard" className="inline-flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Finalize Adoption
          </h1>
          <p className="text-gray-700 dark:text-gray-300">
            Complete the final steps to adopt {pet.name}
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            {[1, 2, 3].map((stepNumber) => (
              <div key={stepNumber} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step >= stepNumber 
                    ? "bg-blue-600 text-white" 
                    : "bg-gray-200 text-gray-600"
                }`}>
                  {stepNumber}
                </div>
                {stepNumber < 3 && (
                  <div className={`w-16 h-1 ${
                    step > stepNumber ? "bg-blue-600" : "bg-gray-200"
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-center mt-2">
            <span className="text-sm text-gray-600">
              Step {step} of 3: {
                step === 1 ? "Review Details" :
                step === 2 ? "Additional Information" :
                "Payment & Finalization"
              }
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Pet Information */}
          <div className="lg:col-span-1">
            <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-gray-900 dark:text-gray-100">Your New Pet</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-4">
                  <div className="w-32 h-32 bg-gray-200 dark:bg-gray-700 rounded-lg mx-auto mb-3 flex items-center justify-center">
                    {pet.photos.length > 0 ? (
                      <img
                        src={pet.photos.find(p => p.isPrimary)?.url || pet.photos[0].url}
                        alt={pet.name}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <span className="text-4xl">🐾</span>
                    )}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{pet.name}</h3>
                  <p className="text-gray-700 dark:text-gray-300">{pet.breed} • {pet.species}</p>
                  <p className="text-gray-700 dark:text-gray-300">{pet.age} years old</p>
                </div>
                
                <div className="space-y-3 text-sm">
                  <div className="flex items-start">
                    <MapPin className="h-4 w-4 text-gray-500 dark:text-gray-400 mr-2 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">{pet.organization.name}</p>
                      <p className="text-gray-700 dark:text-gray-300">
                        {pet.organization.address}<br />
                        {pet.organization.city}, {pet.organization.state}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                    <span className="text-gray-700 dark:text-gray-300">{pet.organization.phone}</span>
                  </div>
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                    <span className="text-gray-700 dark:text-gray-300">{pet.organization.email}</span>
                  </div>
                </div>

                {/* Adoption Fee */}
                <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-900 dark:text-gray-100">Adoption Fee:</span>
                    <span className="text-xl font-bold text-blue-600 dark:text-blue-400">
                      ${adoptionData.adoptionFee}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-2">
            {step === 1 && (
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-gray-100">Review Adoption Details</CardTitle>
                  <CardDescription className="text-gray-700 dark:text-gray-300">
                    Please review all the information before proceeding
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                      <div className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                        <div>
                          <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">Application Approved!</h4>
                          <p className="text-sm text-green-700 dark:text-green-300">
                            Your adoption application has been approved. You can now proceed to finalize the adoption.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Adopter Information</h4>
                        <div className="space-y-2 text-sm">
                          <p><span className="font-medium">Name:</span> {session?.user?.name}</p>
                          <p><span className="font-medium">Email:</span> {session?.user?.email}</p>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Application Details</h4>
                        <div className="space-y-2 text-sm">
                          <p><span className="font-medium">Application ID:</span> {applicationId}</p>
                          <p><span className="font-medium">Status:</span> {adoptionData.status}</p>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button onClick={() => setStep(2)} size="lg">
                        Continue to Next Step
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {step === 2 && (
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-gray-100">Additional Information</CardTitle>
                  <CardDescription className="text-gray-700 dark:text-gray-300">
                    Please provide emergency contact and veterinarian information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form className="space-y-6">
                    {/* Emergency Contact */}
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">Emergency Contact</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="emergencyName" className="text-gray-900 dark:text-gray-100">Full Name *</Label>
                          <Input
                            id="emergencyName"
                            value={formData.emergencyContact.name}
                            onChange={(e) => handleInputChange("emergencyContact", "name", e.target.value)}
                            required
                            className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <Label htmlFor="emergencyPhone" className="text-gray-900 dark:text-gray-100">Phone Number *</Label>
                          <Input
                            id="emergencyPhone"
                            type="tel"
                            value={formData.emergencyContact.phone}
                            onChange={(e) => handleInputChange("emergencyContact", "phone", e.target.value)}
                            required
                            className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div className="md:col-span-2">
                          <Label htmlFor="emergencyRelationship" className="text-gray-900 dark:text-gray-100">Relationship *</Label>
                          <Input
                            id="emergencyRelationship"
                            value={formData.emergencyContact.relationship}
                            onChange={(e) => handleInputChange("emergencyContact", "relationship", e.target.value)}
                            placeholder="e.g., Spouse, Parent, Friend"
                            required
                            className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Veterinarian Information */}
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">Veterinarian Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="vetName" className="text-gray-900 dark:text-gray-100">Veterinarian/Clinic Name *</Label>
                          <Input
                            id="vetName"
                            value={formData.veterinarian.name}
                            onChange={(e) => handleInputChange("veterinarian", "name", e.target.value)}
                            required
                            className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div>
                          <Label htmlFor="vetPhone" className="text-gray-900 dark:text-gray-100">Phone Number *</Label>
                          <Input
                            id="vetPhone"
                            type="tel"
                            value={formData.veterinarian.phone}
                            onChange={(e) => handleInputChange("veterinarian", "phone", e.target.value)}
                            required
                            className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                        <div className="md:col-span-2">
                          <Label htmlFor="vetAddress" className="text-gray-900 dark:text-gray-100">Address *</Label>
                          <Textarea
                            id="vetAddress"
                            value={formData.veterinarian.address}
                            onChange={(e) => handleInputChange("veterinarian", "address", e.target.value)}
                            required
                            rows={3}
                            className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-between">
                      <Button variant="outline" onClick={() => setStep(1)}>
                        Previous Step
                      </Button>
                      <Button onClick={() => setStep(3)}>
                        Continue to Payment
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            {step === 3 && (
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-gray-100">Payment & Finalization</CardTitle>
                  <CardDescription className="text-gray-700 dark:text-gray-300">
                    Complete your adoption by agreeing to terms and processing payment
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Adoption Agreement */}
                    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                      <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Adoption Agreement</h4>
                      <div className="text-sm text-blue-700 dark:text-blue-300 space-y-2">
                        <p>By proceeding with this adoption, you agree to:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>Provide proper care, food, water, and shelter for the pet</li>
                          <li>Ensure regular veterinary care and vaccinations</li>
                          <li>Not abandon, abuse, or neglect the pet</li>
                          <li>Contact the organization if you can no longer care for the pet</li>
                          <li>Allow follow-up visits as required by the organization</li>
                        </ul>
                      </div>
                      <div className="mt-4">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.agreement}
                            onChange={(e) => setFormData(prev => ({ ...prev, agreement: e.target.checked }))}
                            className="mr-2"
                            required
                          />
                          <span className="text-sm text-blue-800 dark:text-blue-200">
                            I agree to the adoption terms and conditions
                          </span>
                        </label>
                      </div>
                    </div>

                    {/* Payment Information */}
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">Payment Information</h4>
                      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <span className="text-lg font-medium text-gray-900 dark:text-gray-100">Adoption Fee:</span>
                          <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            ${adoptionData.adoptionFee}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Payment will be processed securely. You will receive a receipt via email.
                        </p>
                      </div>
                    </div>

                    <div className="flex justify-between">
                      <Button variant="outline" onClick={() => setStep(2)}>
                        Previous Step
                      </Button>
                      <Button 
                        type="submit" 
                        disabled={submitting || !formData.agreement}
                        size="lg"
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        {submitting ? (
                          <span>
                            <CheckCircle className="h-4 w-4 mr-2 animate-spin" />
                            Processing...
                          </span>
                        ) : (
                          <span>
                            <Heart className="h-4 w-4 mr-2" />
                            Finalize Adoption (${adoptionData.adoptionFee})
                          </span>
                        )}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
