"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { toast } from "react-hot-toast"
import { 
  Download, 
  FileText, 
  CheckCircle, 
  Star,
  Clock,
  Users,
  Shield,
  Heart,
  BookOpen,
  Stethoscope,
  Mail
} from "lucide-react"
import { motion } from "framer-motion"
import Link from "next/link"

const downloadSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  selectedGuides: z.array(z.string()).min(1, "Please select at least one guide"),
  newsletter: z.boolean().optional(),
})

type DownloadFormData = z.infer<typeof downloadSchema>

const pdfGuides = [
  {
    id: "complete-pet-care",
    title: "Complete Pet Care Guide",
    description: "Comprehensive 50-page guide covering all aspects of pet care from adoption to senior care",
    pages: 50,
    size: "2.3 MB",
    category: "General Care",
    rating: 4.9,
    downloads: 15420,
    topics: ["Basic Care", "Health", "Nutrition", "Training", "Emergency Care"],
    icon: Heart,
    color: "bg-pink-100 text-pink-600"
  },
  {
    id: "new-pet-checklist",
    title: "New Pet Owner Checklist",
    description: "Essential checklist and timeline for the first 30 days with your new pet",
    pages: 12,
    size: "850 KB",
    category: "Getting Started",
    rating: 4.8,
    downloads: 23150,
    topics: ["Preparation", "First Week", "Supplies", "Vet Visits"],
    icon: CheckCircle,
    color: "bg-green-100 text-green-600"
  },
  {
    id: "emergency-handbook",
    title: "Pet Emergency Handbook",
    description: "Critical first aid procedures and emergency contact templates",
    pages: 24,
    size: "1.1 MB",
    category: "Health & Safety",
    rating: 4.9,
    downloads: 18750,
    topics: ["First Aid", "Emergency Contacts", "Poison Control", "CPR"],
    icon: Stethoscope,
    color: "bg-red-100 text-red-600"
  },
  {
    id: "training-basics",
    title: "Pet Training Fundamentals",
    description: "Step-by-step training guide for basic commands and behavior modification",
    pages: 35,
    size: "1.8 MB",
    category: "Training",
    rating: 4.7,
    downloads: 12890,
    topics: ["Basic Commands", "House Training", "Socialization", "Problem Solving"],
    icon: Users,
    color: "bg-blue-100 text-blue-600"
  },
  {
    id: "nutrition-guide",
    title: "Pet Nutrition & Diet Guide",
    description: "Complete nutrition guide with feeding schedules and dietary recommendations",
    pages: 28,
    size: "1.5 MB",
    category: "Nutrition",
    rating: 4.8,
    downloads: 16340,
    topics: ["Feeding Schedules", "Nutritional Needs", "Special Diets", "Treats"],
    icon: Shield,
    color: "bg-orange-100 text-orange-600"
  },
  {
    id: "senior-pet-care",
    title: "Senior Pet Care Manual",
    description: "Specialized care guide for aging pets with health and comfort tips",
    pages: 32,
    size: "1.7 MB",
    category: "Senior Care",
    rating: 4.9,
    downloads: 9870,
    topics: ["Age-Related Changes", "Comfort Care", "Medical Needs", "Quality of Life"],
    icon: BookOpen,
    color: "bg-purple-100 text-purple-600"
  }
]

const benefits = [
  {
    icon: Download,
    title: "Instant Access",
    description: "Download immediately after submitting the form"
  },
  {
    icon: FileText,
    title: "High Quality",
    description: "Professional PDFs with clear formatting and images"
  },
  {
    icon: Users,
    title: "Expert Reviewed",
    description: "All guides reviewed by certified veterinarians"
  },
  {
    icon: Shield,
    title: "Always Updated",
    description: "Receive updated versions when available"
  }
]

export default function DownloadPDFPage() {
  const [loading, setLoading] = useState(false)
  const [selectedGuides, setSelectedGuides] = useState<string[]>([])
  
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<DownloadFormData>({
    resolver: zodResolver(downloadSchema),
  })

  const handleGuideSelection = (guideId: string, checked: boolean) => {
    let newSelection: string[]
    if (checked) {
      newSelection = [...selectedGuides, guideId]
    } else {
      newSelection = selectedGuides.filter(id => id !== guideId)
    }
    setSelectedGuides(newSelection)
    setValue("selectedGuides", newSelection)
  }

  const selectAllGuides = () => {
    const allGuideIds = pdfGuides.map(guide => guide.id)
    setSelectedGuides(allGuideIds)
    setValue("selectedGuides", allGuideIds)
  }

  const clearSelection = () => {
    setSelectedGuides([])
    setValue("selectedGuides", [])
  }

  const onSubmit = async (data: DownloadFormData) => {
    setLoading(true)
    try {
      const response = await fetch("/api/download-pdf", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        const result = await response.json()
        toast.success("Download links sent to your email!")
        
        // Simulate download links
        data.selectedGuides.forEach((guideId, index) => {
          const guide = pdfGuides.find(g => g.id === guideId)
          if (guide) {
            setTimeout(() => {
              // In a real app, this would be actual PDF download links
              const link = document.createElement('a')
              link.href = `/pdfs/${guideId}.pdf`
              link.download = `${guide.title}.pdf`
              link.click()
            }, index * 500)
          }
        })
      } else {
        const error = await response.json()
        toast.error(error.message || "Failed to process download request")
      }
    } catch (error) {
      console.error("Error processing download:", error)
      toast.error("Something went wrong. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const totalSize = selectedGuides.reduce((total, guideId) => {
    const guide = pdfGuides.find(g => g.id === guideId)
    if (guide) {
      const sizeInMB = parseFloat(guide.size.replace(/[^\d.]/g, ''))
      return total + (guide.size.includes('KB') ? sizeInMB / 1000 : sizeInMB)
    }
    return total
  }, 0)

  return (
    <div className="min-h-screen bg-gradient-to-b from-green-50 to-white py-12">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Download Pet Care Guides
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Access our comprehensive collection of expert-written pet care guides. 
            Download instantly and keep them handy for reference anytime.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* PDF Selection */}
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-2"
          >
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Select Guides to Download
                  </CardTitle>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={selectAllGuides}>
                      Select All
                    </Button>
                    <Button variant="outline" size="sm" onClick={clearSelection}>
                      Clear
                    </Button>
                  </div>
                </div>
                <p className="text-gray-600">
                  Choose from our collection of professional pet care guides
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {pdfGuides.map((guide) => (
                    <motion.div
                      key={guide.id}
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.05 * pdfGuides.indexOf(guide) }}
                      className={`border rounded-lg p-4 transition-all ${
                        selectedGuides.includes(guide.id)
                          ? "border-green-500 bg-green-50"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="flex items-start space-x-4">
                        <Checkbox
                          checked={selectedGuides.includes(guide.id)}
                          onCheckedChange={(checked) => 
                            handleGuideSelection(guide.id, checked as boolean)
                          }
                          className="mt-1"
                        />
                        <div className={`w-12 h-12 ${guide.color} rounded-lg flex items-center justify-center flex-shrink-0`}>
                          <guide.icon className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <div>
                              <h3 className="font-semibold text-gray-900 mb-1">
                                {guide.title}
                              </h3>
                              <p className="text-sm text-gray-600 mb-2">
                                {guide.description}
                              </p>
                            </div>
                            <Badge variant="secondary">{guide.category}</Badge>
                          </div>
                          
                          <div className="flex items-center space-x-4 text-xs text-gray-500 mb-2">
                            <span className="flex items-center">
                              <FileText className="h-3 w-3 mr-1" />
                              {guide.pages} pages
                            </span>
                            <span>{guide.size}</span>
                            <span className="flex items-center">
                              <Star className="h-3 w-3 mr-1 text-yellow-400 fill-current" />
                              {guide.rating}
                            </span>
                            <span className="flex items-center">
                              <Download className="h-3 w-3 mr-1" />
                              {guide.downloads.toLocaleString()} downloads
                            </span>
                          </div>
                          
                          <div className="flex flex-wrap gap-1">
                            {guide.topics.slice(0, 4).map((topic, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {topic}
                              </Badge>
                            ))}
                            {guide.topics.length > 4 && (
                              <Badge variant="outline" className="text-xs">
                                +{guide.topics.length - 4} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Download Form */}
            {selectedGuides.length > 0 && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                className="mt-6"
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Download Information</CardTitle>
                    <p className="text-gray-600">
                      Enter your details to receive download links
                    </p>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                      <div className="grid md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="name">Full Name *</Label>
                          <Input
                            id="name"
                            {...register("name")}
                            error={errors.name?.message}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email">Email Address *</Label>
                          <Input
                            id="email"
                            type="email"
                            {...register("email")}
                            error={errors.email?.message}
                          />
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="newsletter"
                          {...register("newsletter")}
                        />
                        <Label htmlFor="newsletter" className="text-sm">
                          Subscribe to our newsletter for pet care tips and updates
                        </Label>
                      </div>

                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-2">
                          Selected Guides ({selectedGuides.length})
                        </h4>
                        <div className="space-y-1 text-sm text-gray-600">
                          {selectedGuides.map(guideId => {
                            const guide = pdfGuides.find(g => g.id === guideId)
                            return guide ? (
                              <div key={guideId} className="flex justify-between">
                                <span>{guide.title}</span>
                                <span>{guide.size}</span>
                              </div>
                            ) : null
                          })}
                        </div>
                        <div className="border-t border-blue-200 mt-2 pt-2 flex justify-between font-medium">
                          <span>Total Size:</span>
                          <span>{totalSize.toFixed(1)} MB</span>
                        </div>
                      </div>

                      <Button
                        type="submit"
                        disabled={loading}
                        className="w-full bg-green-600 hover:bg-green-700"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        {loading ? "Processing..." : `Download ${selectedGuides.length} Guide${selectedGuides.length > 1 ? 's' : ''}`}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </motion.div>

          {/* Sidebar */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Benefits */}
            <Card>
              <CardHeader>
                <CardTitle>Why Download Our Guides?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <benefit.icon className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{benefit.title}</h4>
                      <p className="text-sm text-gray-600">{benefit.description}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Download Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Downloads</span>
                  <span className="font-semibold">96,420+</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Average Rating</span>
                  <span className="font-semibold flex items-center">
                    4.8 <Star className="h-3 w-3 ml-1 text-yellow-400 fill-current" />
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Last Updated</span>
                  <span className="font-semibold">This Month</span>
                </div>
              </CardContent>
            </Card>

            {/* Contact */}
            <Card>
              <CardHeader>
                <CardTitle>Need Help?</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-sm text-gray-600">
                  Having trouble downloading or have questions about our guides?
                </p>
                <Button asChild variant="outline" className="w-full">
                  <Link href="/contact-expert">
                    <Mail className="h-4 w-4 mr-2" />
                    Contact Support
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
