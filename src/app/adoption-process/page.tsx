"use client"

import React, { useState, useEffect } from "react"
import { motion, useScroll, useTransform, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Heart,
  ClipboardCheck,
  Home,
  UserCheck,
  Calendar,
  DollarSign,
  FilePen,
  Clock,
  ArrowRight,
  CheckCircle2,
  Search,
  Users,
  Sparkles,
  Star,
  PawPrint,
  Gift
} from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

const ADOPTION_STEPS = [
  {
    id: 1,
    title: "Browse Pets",
    shortTitle: "Browse",
    description: "Discover your perfect companion from our loving pets waiting for homes. Each profile tells a unique story!",
    detailedDescription: "Browse through our gallery of adorable pets, each with their own personality and story. Use our smart filters to find pets that match your lifestyle, living situation, and preferences.",
    icon: Search,
    animationIcon: "🔍",
    hoverAnimation: "bounce",
    color: "from-blue-400 to-cyan-400",
    bgColor: "bg-blue-50",
    action: {
      text: "Find Your Match",
      href: "/pets"
    },
    tips: [
      "Consider your living space and lifestyle",
      "Think about activity level preferences",
      "Consider any allergies in your family"
    ],
    timeframe: "Take your time",
    encouragement: "Every pet has a story waiting to unfold with you! 🐾"
  },
  {
    id: 2,
    title: "Apply",
    shortTitle: "Apply",
    description: "Share your story with us! Our simple application helps us understand your perfect pet match.",
    detailedDescription: "Complete our thoughtful application form designed to ensure the best match between you and your future pet. We ask about your experience, lifestyle, and what you're looking for in a companion.",
    icon: FilePen,
    animationIcon: "📝",
    hoverAnimation: "wiggle",
    color: "from-purple-400 to-pink-400",
    bgColor: "bg-purple-50",
    action: {
      text: "Start Application",
      href: "/apply"
    },
    tips: [
      "Be honest about your experience level",
      "Describe your ideal pet companion",
      "Include all family members in planning"
    ],
    timeframe: "15-20 minutes",
    encouragement: "You're one step closer to finding your furry soulmate! 💜"
  },
  {
    id: 3,
    title: "Meet & Greet",
    shortTitle: "Meet",
    description: "The magical moment arrives! Meet your potential new family member in a comfortable, stress-free environment.",
    detailedDescription: "Schedule a visit to our facility or arrange a meet-and-greet. This special time allows you and the pet to get to know each other and see if there's that special connection.",
    icon: Heart,
    animationIcon: "❤️",
    hoverAnimation: "pulse",
    color: "from-pink-400 to-red-400",
    bgColor: "bg-pink-50",
    action: {
      text: "Schedule Visit",
      href: "/schedule-visit"
    },
    tips: [
      "Bring the whole family if possible",
      "Ask about the pet's personality and habits",
      "Take your time - there's no rush"
    ],
    timeframe: "30-60 minutes",
    encouragement: "Love at first sight happens here! 😍"
  },
  {
    id: 4,
    title: "Welcome Home",
    shortTitle: "Adopt",
    description: "Congratulations! Complete the adoption and welcome your new family member to their forever home.",
    detailedDescription: "Finalize the adoption paperwork, pay the adoption fee, and receive everything you need for a smooth transition. Your new pet comes with medical records, care instructions, and our ongoing support.",
    icon: Gift,
    animationIcon: "🎉",
    hoverAnimation: "celebrate",
    color: "from-green-400 to-emerald-400",
    bgColor: "bg-green-50",
    action: {
      text: "Complete Adoption",
      href: "/finalize-adoption"
    },
    tips: [
      "Prepare your home before pickup",
      "Stock up on food and supplies",
      "Plan for a quiet first few days"
    ],
    timeframe: "30 minutes",
    encouragement: "Welcome to the most rewarding journey of your life! 🏠💕"
  }
]

const WHAT_INCLUDED = [
  { icon: "🏥", title: "Health Check", description: "Complete veterinary examination" },
  { icon: "💉", title: "Vaccinations", description: "Up-to-date immunizations" },
  { icon: "🔬", title: "Spay/Neuter", description: "Professional sterilization" },
  { icon: "🏷️", title: "Microchip", description: "Permanent identification" },
  { icon: "🛡️", title: "Health Insurance", description: "30-day coverage included" },
  { icon: "📋", title: "Medical Records", description: "Complete health history" }
]

const AdoptionProcessPage = () => {
  const [activeStep, setActiveStep] = useState<number | null>(null)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [isLoaded, setIsLoaded] = useState(false)
  const { scrollYProgress } = useScroll()
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "30%"])

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  const handleStepClick = (stepId: number) => {
    setActiveStep(activeStep === stepId ? null : stepId)
  }

  const markStepComplete = (stepId: number) => {
    if (!completedSteps.includes(stepId)) {
      setCompletedSteps([...completedSteps, stepId])
    }
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-orange-50 via-pink-50 to-purple-50 relative overflow-hidden" role="main" aria-label="Adoption Process">
      {/* Animated background elements */}
      {isLoaded && (
        <div className="absolute inset-0 opacity-10">
          <motion.div
            style={{ y: backgroundY }}
            className="absolute inset-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 2 }}
          >
            {/* Floating paw prints */}
            <FloatingPawPrints />
          </motion.div>
        </div>
      )}

      {/* Hero Section */}
      <section className="relative py-20 px-4" aria-labelledby="hero-heading">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg" role="banner">
              <PawPrint className="h-5 w-5 text-purple-500" aria-hidden="true" />
              <span className="text-sm font-medium text-gray-700">Your Journey to Love</span>
            </div>

            <h1 id="hero-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight">
              Adoption
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-600 block">
                Process
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Four simple steps to finding your perfect companion. We'll guide you every step of the way with love, care, and excitement!
            </p>

            <motion.div
              animate={{ y: [0, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="inline-block"
              aria-hidden="true"
            >
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Interactive Steps */}
      <section className="relative py-16 px-4" aria-labelledby="steps-heading">
        <div className="container mx-auto">
          <h2 id="steps-heading" className="sr-only">Adoption Process Steps</h2>

          {/* Progress Tracking */}
          <div className="max-w-4xl mx-auto mb-16">
            {/* Progress Header */}
            <div className="text-center mb-8">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6 }}
                className="inline-flex items-center gap-3 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg"
              >
                <Star className="h-5 w-5 text-yellow-500" />
                <span className="text-sm font-medium text-gray-700">
                  Progress: {completedSteps.length} of {ADOPTION_STEPS.length} steps completed
                </span>
                <div className="flex gap-1">
                  {[...Array(ADOPTION_STEPS.length)].map((_, i) => (
                    <div
                      key={i}
                      className={cn(
                        "w-2 h-2 rounded-full transition-all duration-300",
                        i < completedSteps.length ? "bg-yellow-400" : "bg-gray-300"
                      )}
                    />
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Progress Bar - Responsive */}
            <div className="flex items-center justify-between relative px-4 sm:px-0">
              <div className="absolute top-1/2 left-4 right-4 sm:left-0 sm:right-0 h-1 bg-gray-200 rounded-full -translate-y-1/2" />
              <motion.div
                className="absolute top-1/2 left-4 sm:left-0 h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full -translate-y-1/2"
                initial={{ width: 0 }}
                animate={{ width: `${(completedSteps.length / ADOPTION_STEPS.length) * 100}%` }}
                transition={{ duration: 1, ease: "easeInOut" }}
              />
              {ADOPTION_STEPS.map((step) => (
                <div key={step.id} className="relative z-10">
                  <motion.div
                    className={cn(
                      "w-10 h-10 sm:w-12 sm:h-12 rounded-full border-3 sm:border-4 flex items-center justify-center cursor-pointer transition-all duration-300 touch-manipulation",
                      completedSteps.includes(step.id)
                        ? "bg-gradient-to-r from-purple-500 to-pink-500 border-white shadow-lg"
                        : activeStep === step.id
                        ? "bg-white border-purple-500 shadow-lg"
                        : "bg-white border-gray-300 hover:border-purple-400 active:border-purple-500"
                    )}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleStepClick(step.id)}
                    role="button"
                    tabIndex={0}
                    aria-label={`Step ${step.id}: ${step.title}`}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault()
                        handleStepClick(step.id)
                      }
                    }}
                  >
                    {completedSteps.includes(step.id) ? (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", damping: 15, stiffness: 300 }}
                      >
                        <CheckCircle2 className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                      </motion.div>
                    ) : (
                      <span className="text-lg sm:text-2xl" aria-hidden="true">{step.animationIcon}</span>
                    )}
                  </motion.div>
                  <div className="absolute top-12 sm:top-16 left-1/2 -translate-x-1/2 text-center">
                    <p className="text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap">{step.shortTitle}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Encouraging Message */}
            <AnimatePresence mode="wait">
              {completedSteps.length > 0 && (
                <motion.div
                  key={completedSteps.length}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  className="text-center mt-8"
                >
                  <div className="inline-flex items-center gap-2 bg-gradient-to-r from-green-100 to-emerald-100 rounded-full px-6 py-3 shadow-lg">
                    <Sparkles className="h-5 w-5 text-green-600" />
                    <span className="text-sm font-medium text-green-700">
                      {completedSteps.length === ADOPTION_STEPS.length
                        ? "🎉 Congratulations! You're ready to welcome your new family member!"
                        : `Great progress! You're ${Math.round((completedSteps.length / ADOPTION_STEPS.length) * 100)}% of the way there! 🌟`
                      }
                    </span>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Step Cards - Responsive Grid */}
          <div className="max-w-6xl mx-auto">
            {/* Mobile: Single column with horizontal scroll hint */}
            <div className="block sm:hidden">
              <div className="flex gap-4 overflow-x-auto pb-4 snap-x snap-mandatory scrollbar-hide">
                {ADOPTION_STEPS.map((step, index) => (
                  <div key={step.id} className="flex-none w-80 snap-center">
                    <StepCard
                      step={step}
                      index={index}
                      isActive={activeStep === step.id}
                      isCompleted={completedSteps.includes(step.id)}
                      onClick={() => handleStepClick(step.id)}
                      onComplete={() => markStepComplete(step.id)}
                    />
                  </div>
                ))}
              </div>
              <div className="text-center mt-4">
                <p className="text-sm text-gray-500">← Swipe to explore all steps →</p>
              </div>
            </div>

            {/* Tablet and Desktop: Grid layout */}
            <div className="hidden sm:grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {ADOPTION_STEPS.map((step, index) => (
                <StepCard
                  key={step.id}
                  step={step}
                  index={index}
                  isActive={activeStep === step.id}
                  isCompleted={completedSteps.includes(step.id)}
                  onClick={() => handleStepClick(step.id)}
                  onComplete={() => markStepComplete(step.id)}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Support Section */}
      <section className="relative py-16 px-4" aria-labelledby="support-heading">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center mb-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 id="support-heading" className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                We're Here to Help! 🤗
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Have questions? Our friendly adoption team is ready to guide you through every step.
              </p>
            </motion.div>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full bg-gradient-to-br from-blue-50 to-cyan-50 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h3>
                  <p className="text-gray-600 mb-6">
                    Find answers to common questions about our adoption process, requirements, and policies.
                  </p>
                  <Link href="/faq">
                    <Button variant="outline" size="lg" className="w-full border-blue-500 text-blue-600 hover:bg-blue-50">
                      View FAQ
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="h-full bg-gradient-to-br from-purple-50 to-pink-50 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Heart className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Personal Guidance</h3>
                  <p className="text-gray-600 mb-6">
                    Speak directly with our adoption counselors for personalized assistance and support.
                  </p>
                  <Link href="/contact">
                    <Button size="lg" className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white">
                      Contact Us
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* What's Included Section */}
      <section className="relative py-16 px-4 bg-gradient-to-r from-green-50 to-emerald-50" aria-labelledby="included-heading">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 id="included-heading" className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                What's Included with Every Adoption 🎁
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Your adoption fee covers comprehensive care to ensure your new pet is healthy, happy, and ready for their forever home.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {WHAT_INCLUDED.map((item, index) => (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="h-full bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                    <CardContent className="p-6 text-center">
                      <div className="text-4xl mb-4" aria-hidden="true">{item.icon}</div>
                      <h3 className="text-lg font-bold text-gray-900 mb-2">{item.title}</h3>
                      <p className="text-gray-600 text-sm">{item.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              viewport={{ once: true }}
              className="text-center mt-12"
            >
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Begin Your Journey? 🌟</h3>
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  Every pet deserves a loving home, and every family deserves the joy of a furry companion.
                  Start your adoption journey today!
                </p>
                <Link href="/pets">
                  <Button size="lg" className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                    Find Your Perfect Pet
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </main>
  )
}

// Step Card Component
interface StepCardProps {
  step: typeof ADOPTION_STEPS[0]
  index: number
  isActive: boolean
  isCompleted: boolean
  onClick: () => void
  onComplete: () => void
}

function StepCard({ step, index, isActive, isCompleted, onClick, onComplete }: StepCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  const getAnimationClass = () => {
    if (!isHovered) return ""

    switch (step.hoverAnimation) {
      case "bounce":
        return "animate-bounce"
      case "wiggle":
        return "animate-pulse"
      case "pulse":
        return "animate-ping"
      case "celebrate":
        return "animate-spin"
      default:
        return "animate-bounce"
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="relative"
      role="listitem"
    >
      <Card
        className={cn(
          "h-full cursor-pointer transition-all duration-300 overflow-hidden",
          step.bgColor,
          isActive ? "ring-2 ring-purple-500 shadow-xl scale-105" : "hover:shadow-lg hover:scale-102",
          isCompleted && "ring-2 ring-green-500"
        )}
        onClick={onClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        role="button"
        tabIndex={0}
        aria-label={`Step ${step.id}: ${step.title} - ${step.description}`}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            onClick()
          }
        }}
      >
        {/* Glow effect */}
        <div className={cn(
          "absolute inset-0 opacity-0 transition-opacity duration-300",
          isActive && "opacity-20",
          `bg-gradient-to-br ${step.color}`
        )} />

        <CardHeader className="relative z-10 text-center pb-4">
          <div className="mx-auto mb-4 relative">
            <div className={cn(
              "w-16 h-16 rounded-full flex items-center justify-center text-white shadow-lg",
              `bg-gradient-to-br ${step.color}`,
              isCompleted && "bg-gradient-to-br from-green-400 to-emerald-500"
            )}>
              {isCompleted ? (
                <CheckCircle2 className="w-8 h-8" />
              ) : (
                <step.icon className="w-8 h-8" />
              )}
            </div>

            {/* Animated emoji overlay */}
            <div className={cn(
              "absolute -top-2 -right-2 text-2xl transition-all duration-300",
              getAnimationClass()
            )}>
              {step.animationIcon}
            </div>
          </div>

          <CardTitle className="text-lg font-bold text-gray-900 mb-2">
            {step.title}
          </CardTitle>

          {step.timeframe && (
            <Badge variant="secondary" className="text-xs">
              <Clock className="w-3 h-3 mr-1" />
              {step.timeframe}
            </Badge>
          )}
        </CardHeader>

        <CardContent className="relative z-10 pt-0">
          <p className="text-gray-600 text-sm leading-relaxed mb-4">
            {step.description}
          </p>

          {/* Expandable details */}
          <AnimatePresence>
            {isActive && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <div className="border-t border-gray-200 pt-4 space-y-3">
                  <p className="text-sm text-gray-700 font-medium">
                    {step.detailedDescription}
                  </p>

                  {step.tips && (
                    <div>
                      <h4 className="text-sm font-semibold text-gray-800 mb-2">💡 Helpful Tips:</h4>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {step.tips.map((tip, tipIndex) => (
                          <li key={tipIndex} className="flex items-start gap-2">
                            <span className="text-purple-500 mt-0.5">•</span>
                            <span>{tip}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-3 text-center">
                    <p className="text-sm text-purple-700 font-medium">
                      {step.encouragement}
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {step.action && (
            <div className="mt-4">
              <Link href={step.action.href}>
                <Button
                  className={cn(
                    "w-full text-white shadow-lg hover:shadow-xl transition-all duration-300",
                    `bg-gradient-to-r ${step.color} hover:scale-105`
                  )}
                  onClick={(e) => {
                    e.stopPropagation()
                    onComplete()
                  }}
                >
                  {step.action.text}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

// Floating Paw Prints Component (Client-only to avoid hydration mismatch)
function FloatingPawPrints() {
  const [pawPrints, setPawPrints] = useState<Array<{
    id: number
    x: number
    y: number
    delay: number
    duration: number
  }>>([])

  useEffect(() => {
    // Generate consistent positions only on client
    const prints = Array.from({ length: 15 }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      delay: Math.random() * 3,
      duration: 4 + Math.random() * 2
    }))
    setPawPrints(prints)
  }, [])

  return (
    <>
      {pawPrints.map((print) => (
        <motion.div
          key={print.id}
          className="absolute text-4xl"
          initial={{
            x: print.x,
            y: print.y,
            opacity: 0
          }}
          animate={{
            y: [print.y, print.y - 30, print.y],
            opacity: [0, 0.3, 0],
            rotate: [0, 360]
          }}
          transition={{
            duration: print.duration,
            repeat: Infinity,
            delay: print.delay
          }}
        >
          🐾
        </motion.div>
      ))}
    </>
  )
}

export default AdoptionProcessPage
