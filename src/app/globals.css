@import "tailwindcss";

@layer base {
  * {
    @apply border-gray-200 dark:border-gray-700;
  }

  body {
    @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Ensure proper contrast and readability */
  .bg-gray-50 {
    @apply bg-gray-50 dark:bg-gray-800;
  }

  .text-gray-600 {
    @apply text-gray-700 dark:text-gray-300;
  }

  .text-gray-900 {
    @apply text-gray-900 dark:text-gray-100;
  }

  /* Fix white/bright backgrounds that cause visibility issues */
  .bg-white {
    @apply bg-white dark:bg-gray-900;
  }

  /* Ensure cards have proper contrast */
  .card-background {
    @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700;
  }

  /* Fix opacity issues */
  .bg-white\/95 {
    @apply bg-white/95 dark:bg-gray-900/95;
  }

  .bg-white\/90 {
    @apply bg-white/90 dark:bg-gray-900/90;
  }
}

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Enhanced background image display */
  .bg-image-enhanced {
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  /* Mobile optimization for background images */
  @media (max-width: 768px) {
    .bg-image-enhanced {
      background-attachment: scroll;
    }
  }

  /* Improved backdrop blur for better readability */
  .backdrop-blur-enhanced {
    backdrop-filter: blur(8px) saturate(180%);
    -webkit-backdrop-filter: blur(8px) saturate(180%);
  }

  /* Hide scrollbar for horizontal scroll */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Touch-friendly interactions */
  .touch-manipulation {
    touch-action: manipulation;
  }
}
