"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  ArrowLeft, 
  Search, 
  Heart, 
  Eye, 
  Trash2,
  MapPin,
  Calendar,
  Filter
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { toast } from "react-hot-toast"

interface Favorite {
  id: string
  createdAt: string
  pet: {
    id: string
    name: string
    species: string
    breed: string
    age: number
    size: string
    gender: string
    color: string
    description: string
    adoptionFee: number
    goodWithKids: boolean
    goodWithDogs: boolean
    goodWithCats: boolean
    photos: Array<{ url: string; isPrimary: boolean }>
    organization: {
      name: string
      city: string
      state: string
    }
    _count: {
      applications: number
    }
  }
}

export default function FavoritesPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [favorites, setFavorites] = useState<Favorite[]>([])
  const [filteredFavorites, setFilteredFavorites] = useState<Favorite[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [speciesFilter, setSpeciesFilter] = useState("")

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
      return
    }

    if (status === "authenticated") {
      fetchFavorites()
    }
  }, [status, router])

  useEffect(() => {
    let filtered = favorites

    if (searchTerm) {
      filtered = filtered.filter(fav => 
        fav.pet.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fav.pet.breed.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fav.pet.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (speciesFilter) {
      filtered = filtered.filter(fav => fav.pet.species.toLowerCase() === speciesFilter.toLowerCase())
    }

    setFilteredFavorites(filtered)
  }, [favorites, searchTerm, speciesFilter])

  const fetchFavorites = async () => {
    try {
      const response = await fetch("/api/favorites")
      if (response.ok) {
        const data = await response.json()
        setFavorites(data.favorites || [])
      } else {
        toast.error("Failed to load favorites")
      }
    } catch (error) {
      console.error("Error fetching favorites:", error)
      toast.error("Failed to load favorites")
    } finally {
      setLoading(false)
    }
  }

  const removeFavorite = async (favoriteId: string, petName: string) => {
    try {
      const response = await fetch(`/api/favorites/${favoriteId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        setFavorites(prev => prev.filter(fav => fav.id !== favoriteId))
        toast.success(`Removed ${petName} from favorites`)
      } else {
        toast.error("Failed to remove favorite")
      }
    } catch (error) {
      console.error("Error removing favorite:", error)
      toast.error("Failed to remove favorite")
    }
  }

  const formatAge = (ageInMonths: number) => {
    if (ageInMonths < 12) {
      return `${ageInMonths} month${ageInMonths !== 1 ? 's' : ''} old`
    } else {
      const years = Math.floor(ageInMonths / 12)
      const months = ageInMonths % 12
      if (months === 0) {
        return `${years} year${years !== 1 ? 's' : ''} old`
      } else {
        return `${years}y ${months}m old`
      }
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (status === "loading" || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-80 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Link href="/dashboard" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Link>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">My Favorites</h1>
        <p className="text-gray-600">
          Pets you've saved for later consideration.
        </p>
      </div>

      {/* Search and Filter */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="Search by pet name, breed, or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-400" />
          <select
            value={speciesFilter}
            onChange={(e) => setSpeciesFilter(e.target.value)}
            className="p-2 border border-gray-300 rounded-md"
          >
            <option value="">All Species</option>
            <option value="dog">Dogs</option>
            <option value="cat">Cats</option>
            <option value="rabbit">Rabbits</option>
            <option value="bird">Birds</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>

      {/* Favorites Grid */}
      {filteredFavorites.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Heart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {favorites.length === 0 ? "No Favorites Yet" : "No Matching Favorites"}
            </h3>
            <p className="text-gray-600 mb-6">
              {favorites.length === 0 
                ? "Start browsing pets and save your favorites for easy access later."
                : "Try adjusting your search or filter criteria."
              }
            </p>
            {favorites.length === 0 && (
              <Link href="/pets">
                <Button>Browse Available Pets</Button>
              </Link>
            )}
          </CardContent>
        </Card>
      ) : (
        <div>
          {/* Results Count */}
          <div className="mb-6">
            <p className="text-gray-600">
              Showing {filteredFavorites.length} of {favorites.length} favorites
            </p>
          </div>

          {/* Favorites Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredFavorites.map((favorite) => (
              <Card key={favorite.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="relative aspect-square overflow-hidden">
                  {favorite.pet.photos.length > 0 ? (
                    <Image
                      src={favorite.pet.photos.find(p => p.isPrimary)?.url || favorite.pet.photos[0].url}
                      alt={favorite.pet.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-4xl">🐾</span>
                    </div>
                  )}
                  
                  {/* Remove Favorite Button */}
                  <button
                    onClick={() => removeFavorite(favorite.id, favorite.pet.name)}
                    className="absolute top-2 right-2 p-2 bg-white/80 rounded-full hover:bg-white transition-colors"
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </button>

                  {/* Quick Info Badges */}
                  <div className="absolute bottom-2 left-2 flex gap-1">
                    {favorite.pet.goodWithKids && (
                      <Badge className="bg-green-500 text-white text-xs">
                        Kids ✓
                      </Badge>
                    )}
                    {favorite.pet.goodWithDogs && (
                      <Badge className="bg-blue-500 text-white text-xs">
                        Dogs ✓
                      </Badge>
                    )}
                    {favorite.pet.goodWithCats && (
                      <Badge className="bg-purple-500 text-white text-xs">
                        Cats ✓
                      </Badge>
                    )}
                  </div>
                </div>

                <CardHeader className="pb-2">
                  <CardTitle className="text-xl">{favorite.pet.name}</CardTitle>
                  <CardDescription>
                    {favorite.pet.breed} • {formatAge(favorite.pet.age)} • {favorite.pet.gender}
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="space-y-3">
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-3 w-3 mr-1" />
                      {favorite.pet.organization.city}, {favorite.pet.organization.state}
                    </div>

                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-3 w-3 mr-1" />
                      Saved {formatDate(favorite.createdAt)}
                    </div>
                    
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {favorite.pet.description}
                    </p>

                    {favorite.pet.adoptionFee && (
                      <div className="text-lg font-semibold text-green-600">
                        ${favorite.pet.adoptionFee} adoption fee
                      </div>
                    )}

                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <Badge variant="outline">{favorite.pet.size} size</Badge>
                      <span>{favorite.pet._count.applications} applications</span>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-2">
                      <Link href={`/pets/${favorite.pet.id}`} className="flex-1">
                        <Button variant="outline" size="sm" className="w-full">
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      </Link>
                      <Link href={`/apply/${favorite.pet.id}`} className="flex-1">
                        <Button size="sm" className="w-full">
                          Apply
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
