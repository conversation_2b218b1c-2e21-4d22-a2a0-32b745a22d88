"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  ArrowLeft, 
  Search, 
  Filter, 
  Eye, 
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  MessageSquare
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { toast } from "react-hot-toast"

interface Application {
  id: string
  status: string
  submittedAt: string
  reviewedAt?: string
  approvedAt?: string
  staffNotes?: string
  rejectionReason?: string
  pet: {
    id: string
    name: string
    species: string
    breed: string
    age: number
    photos: Array<{ url: string; isPrimary: boolean }>
    organization: {
      name: string
      city: string
      state: string
    }
  }
  appointments: Array<{
    id: string
    type: string
    scheduledDate: string
    status: string
  }>
}

export default function ApplicationsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [applications, setApplications] = useState<Application[]>([])
  const [filteredApplications, setFilteredApplications] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("")

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
      return
    }

    if (status === "authenticated") {
      fetchApplications()
    }
  }, [status, router])

  useEffect(() => {
    let filtered = applications

    if (searchTerm) {
      filtered = filtered.filter(app => 
        app.pet.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.pet.breed.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.pet.species.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter) {
      filtered = filtered.filter(app => app.status === statusFilter)
    }

    setFilteredApplications(filtered)
  }, [applications, searchTerm, statusFilter])

  const fetchApplications = async () => {
    try {
      const response = await fetch("/api/applications")
      if (response.ok) {
        const data = await response.json()
        setApplications(data.applications || [])
      } else {
        toast.error("Failed to load applications")
      }
    } catch (error) {
      console.error("Error fetching applications:", error)
      toast.error("Failed to load applications")
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "rejected":
        return <XCircle className="h-4 w-4 text-red-500" />
      case "under_review":
      case "reference_check":
      case "home_visit_scheduled":
      case "home_visit_completed":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "waitlisted":
        return <AlertCircle className="h-4 w-4 text-orange-500" />
      default:
        return <FileText className="h-4 w-4 text-blue-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return "bg-green-100 text-green-800 border-green-200"
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200"
      case "under_review":
      case "reference_check":
      case "home_visit_scheduled":
      case "home_visit_completed":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "waitlisted":
        return "bg-orange-100 text-orange-800 border-orange-200"
      default:
        return "bg-blue-100 text-blue-800 border-blue-200"
    }
  }

  const getStatusDescription = (status: string) => {
    switch (status.toLowerCase()) {
      case "submitted":
        return "Your application has been submitted and is awaiting review."
      case "under_review":
        return "Our team is currently reviewing your application."
      case "reference_check":
        return "We are contacting your references to verify information."
      case "home_visit_scheduled":
        return "A home visit has been scheduled. Check your appointments."
      case "home_visit_completed":
        return "Home visit completed. Awaiting final decision."
      case "approved":
        return "Congratulations! Your application has been approved."
      case "rejected":
        return "Unfortunately, your application was not approved at this time."
      case "waitlisted":
        return "You've been added to the waitlist for this pet."
      case "withdrawn":
        return "This application has been withdrawn."
      default:
        return "Application status unknown."
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatAge = (ageInMonths: number) => {
    if (ageInMonths < 12) {
      return `${ageInMonths} month${ageInMonths !== 1 ? 's' : ''} old`
    } else {
      const years = Math.floor(ageInMonths / 12)
      const months = ageInMonths % 12
      if (months === 0) {
        return `${years} year${years !== 1 ? 's' : ''} old`
      } else {
        return `${years}y ${months}m old`
      }
    }
  }

  if (status === "loading" || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Link href="/dashboard" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Link>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">My Applications</h1>
        <p className="text-gray-600">
          Track the status of your adoption applications and upcoming appointments.
        </p>
      </div>

      {/* Search and Filter */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="Search by pet name, breed, or species..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-400" />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="p-2 border border-gray-300 rounded-md"
          >
            <option value="">All Statuses</option>
            <option value="SUBMITTED">Submitted</option>
            <option value="UNDER_REVIEW">Under Review</option>
            <option value="REFERENCE_CHECK">Reference Check</option>
            <option value="HOME_VISIT_SCHEDULED">Home Visit Scheduled</option>
            <option value="HOME_VISIT_COMPLETED">Home Visit Completed</option>
            <option value="APPROVED">Approved</option>
            <option value="REJECTED">Rejected</option>
            <option value="WAITLISTED">Waitlisted</option>
          </select>
        </div>
      </div>

      {/* Applications List */}
      {filteredApplications.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {applications.length === 0 ? "No Applications Yet" : "No Matching Applications"}
            </h3>
            <p className="text-gray-600 mb-6">
              {applications.length === 0 
                ? "You haven't submitted any adoption applications yet."
                : "Try adjusting your search or filter criteria."
              }
            </p>
            {applications.length === 0 && (
              <Link href="/pets">
                <Button>Browse Available Pets</Button>
              </Link>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {filteredApplications.map((application) => (
            <Card key={application.id} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="flex flex-col lg:flex-row">
                  {/* Pet Image */}
                  <div className="lg:w-48 h-48 lg:h-auto relative">
                    {application.pet?.photos?.length > 0 ? (
                      <Image
                        src={application.pet.photos.find(p => p.isPrimary)?.url || application.pet.photos[0].url}
                        alt={application.pet?.name || 'Pet'}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-4xl">🐾</span>
                      </div>
                    )}
                  </div>

                  {/* Application Details */}
                  <div className="flex-1 p-6">
                    <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-4">
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-1">
                          {application.pet?.name || 'Unknown Pet'}
                        </h3>
                        <p className="text-gray-600 mb-2">
                          {application.pet?.breed || 'Unknown'} • {application.pet?.age ? formatAge(application.pet.age) : 'Unknown age'} • {application.pet?.species || 'Unknown'}
                        </p>
                        <p className="text-sm text-gray-500">
                          {application.pet?.organization?.name || 'Unknown Organization'} • {application.pet?.organization?.city || 'Unknown'}, {application.pet?.organization?.state || 'Unknown'}
                        </p>
                      </div>
                      <div className="mt-4 lg:mt-0 lg:text-right">
                        <div className="flex items-center lg:justify-end mb-2">
                          {getStatusIcon(application.status)}
                          <Badge className={`ml-2 ${getStatusColor(application.status)}`}>
                            {application.status.replace(/_/g, ' ')}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-500">
                          Applied: {formatDate(application.submittedAt)}
                        </p>
                        {application.reviewedAt && (
                          <p className="text-sm text-gray-500">
                            Reviewed: {formatDate(application.reviewedAt)}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Status Description */}
                    <div className="bg-gray-50 rounded-lg p-4 mb-4">
                      <p className="text-sm text-gray-700">
                        {getStatusDescription(application.status)}
                      </p>
                      {application.staffNotes && (
                        <div className="mt-2 pt-2 border-t border-gray-200">
                          <p className="text-xs text-gray-600 font-medium">Staff Notes:</p>
                          <p className="text-sm text-gray-700">{application.staffNotes}</p>
                        </div>
                      )}
                      {application.rejectionReason && (
                        <div className="mt-2 pt-2 border-t border-gray-200">
                          <p className="text-xs text-gray-600 font-medium">Reason:</p>
                          <p className="text-sm text-gray-700">{application.rejectionReason}</p>
                        </div>
                      )}
                    </div>

                    {/* Appointments */}
                    {application.appointments && application.appointments.length > 0 && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Upcoming Appointments</h4>
                        <div className="space-y-2">
                          {application.appointments.map((appointment) => (
                            <div key={appointment.id} className="flex items-center text-sm">
                              <Calendar className="h-4 w-4 text-blue-500 mr-2" />
                              <span className="font-medium">{appointment.type.replace(/_/g, ' ')}</span>
                              <span className="mx-2">•</span>
                              <span>{formatDate(appointment.scheduledDate)}</span>
                              <Badge className="ml-2" variant="outline">
                                {appointment.status}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex flex-wrap gap-2">
                      <Link href={`/dashboard/applications/${application.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3 mr-1" />
                          View Details
                        </Button>
                      </Link>
                      {application.pet?.id && (
                        <Link href={`/pets/${application.pet.id}`}>
                          <Button variant="outline" size="sm">
                            View Pet
                          </Button>
                        </Link>
                      )}
                      {application.status === "APPROVED" && (
                        <Button size="sm" className="bg-green-600 hover:bg-green-700">
                          <Calendar className="h-3 w-3 mr-1" />
                          Schedule Pickup
                        </Button>
                      )}
                      <Button variant="outline" size="sm" asChild>
                        <Link href="/contact-shelter">
                          <MessageSquare className="h-3 w-3 mr-1" />
                          Contact Shelter
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
