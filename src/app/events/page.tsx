"use client"

import { useState } from "react"
import { Calendar } from "lucide-react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import Link from "next/link"

const UPCOMING_EVENTS = [
  {
    id: 1,
    title: "Pet Adoption Day",
    date: "2025-06-30",
    time: "10:00 AM - 4:00 PM",
    location: "Main Shelter",
    description: "Find your perfect companion! Join us for our monthly adoption event featuring dozens of lovely pets looking for their forever homes.",
    category: "Adoption",
    image: "/images/home background.jpg"
  },
  {
    id: 2,
    title: "Volunteer Orientation",
    date: "2025-07-05",
    time: "2:00 PM - 3:30 PM",
    location: "Training Room",
    description: "Learn about our volunteer opportunities and how you can make a difference in pets' lives.",
    category: "Volunteer",
    image: "/images/volunteer with us page .jpg"
  },
  {
    id: 3,
    title: "Pet Care Workshop",
    date: "2025-07-12",
    time: "1:00 PM - 3:00 PM",
    location: "Community Center",
    description: "Expert tips on pet care, nutrition, and basic training techniques.",
    category: "Education",
    image: "/images/pet care guide.jpg"
  }
]

export default function EventsPage() {
  const [filter, setFilter] = useState("all")

  return (
    <div className="container mx-auto py-12 px-4">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Upcoming Events</h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Join us for exciting events and make a difference in the lives of our furry friends.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {UPCOMING_EVENTS.map((event) => (
          <Card key={event.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="relative h-48 overflow-hidden">
              <img
                src={event.image}
                alt={event.title}
                className="w-full h-full object-cover"
              />
              <Badge 
                className="absolute top-4 right-4"
                variant="secondary"
              >
                {event.category}
              </Badge>
            </div>
            <CardHeader>
              <CardTitle>{event.title}</CardTitle>
              <CardDescription>
                <span className="flex items-center gap-2 text-gray-600">
                  <Calendar className="w-4 h-4" />
                  {new Date(event.date).toLocaleDateString()} at {event.time}
                </span>
                <span className="mt-1 block text-gray-600">📍 {event.location}</span>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">{event.description}</p>
              <Link href={`/events/${event.id}`}>
                <Button className="w-full">Learn More & Register</Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
