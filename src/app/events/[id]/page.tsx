"use client"

import { useParams } from "next/navigation"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Mail,
  Phone,
  ArrowLeft,
  Share2
} from "lucide-react"
import Link from "next/link"
import { toast } from "react-hot-toast"

export default function EventDetailsPage() {
  const params = useParams()
  const [isRegistering, setIsRegistering] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    attendees: 1,
    notes: ""
  })

  // In a real app, this would come from an API call
  const event = {
    id: params.id,
    title: "Pet Adoption Day",
    date: "2025-06-30",
    time: "10:00 AM - 4:00 PM",
    location: "Main Shelter - 123 Pet Street",
    description: "Join us for a special adoption event featuring dozens of loving pets looking for their forever homes. Our staff and volunteers will be available to help you find your perfect match.",
    category: "Adoption",
    image: "/images/home background.jpg",
    maxAttendees: 50,
    currentAttendees: 32,
    organizer: "Sarah Johnson",
    organizerRole: "Event Coordinator",
    highlights: [
      "Meet adoptable pets",
      "Professional pet photographers on site",
      "Free pet care workshops",
      "Adoption counselors available",
      "Refreshments provided",
      "Special adoption rates"
    ]
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsRegistering(true)
    
    try {
      // Here you would make an API call to register
      await new Promise(resolve => setTimeout(resolve, 1500)) // Simulated API call
      toast.success("Registration successful! Check your email for details.")
      setFormData({
        name: "",
        email: "",
        phone: "",
        attendees: 1,
        notes: ""
      })
    } catch (error) {
      toast.error("Failed to register. Please try again.")
    } finally {
      setIsRegistering(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white py-12">
      <div className="container mx-auto px-4">
        {/* Back Button */}
        <Link href="/events" className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-8">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Events
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Event Details */}
          <div className="lg:col-span-2">
            <div className="relative h-[400px] rounded-xl overflow-hidden mb-8">
              <img
                src={event.image}
                alt={event.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black/20"></div>
              <Badge 
                className="absolute top-6 right-6 text-lg px-6 py-2"
                variant="secondary"
              >
                {event.category}
              </Badge>
            </div>

            <h1 className="text-4xl font-bold text-gray-900 mb-6">{event.title}</h1>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              <Card>
                <CardContent className="flex items-center space-x-3 py-6">
                  <Calendar className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-500">Date</p>
                    <p className="font-medium">{new Date(event.date).toLocaleDateString()}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="flex items-center space-x-3 py-6">
                  <Clock className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-500">Time</p>
                    <p className="font-medium">{event.time}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="flex items-center space-x-3 py-6">
                  <MapPin className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-500">Location</p>
                    <p className="font-medium">{event.location}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="flex items-center space-x-3 py-6">
                  <Users className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-500">Capacity</p>
                    <p className="font-medium">{event.currentAttendees}/{event.maxAttendees} registered</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="prose max-w-none">
              <h2 className="text-2xl font-semibold mb-4">About This Event</h2>
              <p className="text-gray-600 mb-8">{event.description}</p>

              <h2 className="text-2xl font-semibold mb-4">Event Highlights</h2>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-8">
                {event.highlights.map((highlight, index) => (
                  <li key={index} className="flex items-center space-x-2">
                    <span className="h-2 w-2 rounded-full bg-blue-500"></span>
                    <span>{highlight}</span>
                  </li>
                ))}
              </ul>

              <div className="border-t pt-8 mt-8">
                <h2 className="text-2xl font-semibold mb-4">Event Organizer</h2>
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                    <Users className="h-6 w-6 text-blue-500" />
                  </div>
                  <div>
                    <p className="font-medium">{event.organizer}</p>
                    <p className="text-sm text-gray-500">{event.organizerRole}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Registration Form */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle>Register for Event</CardTitle>
                <CardDescription>Fill out the form below to secure your spot</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Name</label>
                    <Input
                      required
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Email</label>
                    <Input
                      type="email"
                      required
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Phone</label>
                    <Input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Number of Attendees</label>
                    <Input
                      type="number"
                      min="1"
                      max="5"
                      required
                      value={formData.attendees}
                      onChange={(e) => setFormData(prev => ({ ...prev, attendees: parseInt(e.target.value) }))}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Special Notes</label>
                    <Textarea
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      placeholder="Any dietary restrictions or special requirements?"
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isRegistering}
                  >
                    {isRegistering ? "Registering..." : "Register Now"}
                  </Button>

                  <div className="text-center">
                    <p className="text-sm text-gray-500">
                      {event.maxAttendees - event.currentAttendees} spots remaining
                    </p>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
