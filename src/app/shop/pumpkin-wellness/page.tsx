"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Heart,
  Shield,
  Calendar,
  DollarSign,
  CheckCircle,
  Star,
  Users,
  Award,
  Phone,
  Mail,
  Clock,
  Stethoscope,
  Pill,
  Activity,
  TrendingUp,
  Gift,
  ShoppingCart
} from "lucide-react"
import { motion } from "framer-motion"

interface WellnessPlan {
  id: string
  name: string
  price: {
    monthly: string
    annual: string
    savings?: string
  }
  ageGroup: string
  features: string[]
  benefits: string[]
  popular?: boolean
  recommended?: boolean
  color: string
}

const wellnessPlans: WellnessPlan[] = [
  {
    id: "puppy-kitten",
    name: "<PERSON><PERSON><PERSON> & <PERSON><PERSON> Plan",
    price: { monthly: "$29.99", annual: "$299.99", savings: "Save $60" },
    ageGroup: "0-12 months",
    features: [
      "Unlimited wellness exams",
      "Core vaccinations",
      "Spay/neuter procedure",
      "Microchipping",
      "Deworming treatments",
      "Flea & tick prevention",
      "24/7 vet chat support"
    ],
    benefits: [
      "Complete puppy/kitten care",
      "Early health monitoring",
      "Preventive treatments",
      "Expert guidance"
    ],
    popular: true,
    color: "blue"
  },
  {
    id: "adult",
    name: "Adult Pet Plan",
    price: { monthly: "$39.99", annual: "$399.99", savings: "Save $80" },
    ageGroup: "1-7 years",
    features: [
      "Annual wellness exam",
      "Core vaccinations",
      "Dental cleaning",
      "Blood work panel",
      "Heartworm testing",
      "Flea & tick prevention",
      "Nutrition consultation",
      "24/7 vet chat support"
    ],
    benefits: [
      "Comprehensive adult care",
      "Early disease detection",
      "Dental health maintenance",
      "Nutritional guidance"
    ],
    recommended: true,
    color: "green"
  },
  {
    id: "senior",
    name: "Senior Pet Plan",
    price: { monthly: "$49.99", annual: "$499.99", savings: "Save $100" },
    ageGroup: "7+ years",
    features: [
      "Bi-annual wellness exams",
      "Comprehensive blood work",
      "Senior health screening",
      "Dental care",
      "Joint health supplements",
      "Pain management consultation",
      "Specialized nutrition plan",
      "Priority appointment booking",
      "24/7 vet chat support"
    ],
    benefits: [
      "Enhanced senior monitoring",
      "Early intervention",
      "Comfort and mobility support",
      "Specialized care"
    ],
    color: "purple"
  }
]

export default function PumpkinWellnessPage() {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">("annual")

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-yellow-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-orange-600 to-yellow-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Pumpkin Wellness
              <span className="block text-orange-300">Club</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-yellow-100">
              Comprehensive preventive care plans to keep your pet healthy and happy
            </p>
            <div className="flex justify-center gap-4">
              <Button size="lg" className="bg-white text-orange-600 hover:bg-gray-100">
                View Plans
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600">
                Learn More
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Benefits Overview */}
        <div className="grid md:grid-cols-4 gap-6 mb-12">
          {[
            { icon: Heart, title: "Preventive Care", description: "Focus on keeping pets healthy, not just treating illness" },
            { icon: DollarSign, title: "Cost Savings", description: "Save up to 40% on routine veterinary care" },
            { icon: Calendar, title: "Convenient Scheduling", description: "Priority booking and flexible appointment times" },
            { icon: Stethoscope, title: "Expert Care", description: "Access to board-certified veterinarians" }
          ].map((benefit, index) => (
            <motion.div
              key={benefit.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="text-center h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <benefit.icon className="w-6 h-6 text-orange-600" />
                  </div>
                  <h3 className="font-semibold mb-2">{benefit.title}</h3>
                  <p className="text-sm text-gray-600">{benefit.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Billing Toggle */}
        <div className="flex justify-center mb-8">
          <div className="bg-gray-100 p-1 rounded-lg">
            <Button
              variant={billingCycle === "monthly" ? "default" : "ghost"}
              size="sm"
              onClick={() => setBillingCycle("monthly")}
            >
              Monthly
            </Button>
            <Button
              variant={billingCycle === "annual" ? "default" : "ghost"}
              size="sm"
              onClick={() => setBillingCycle("annual")}
            >
              Annual
              <Badge className="ml-2 bg-green-500 text-white">Save up to $100</Badge>
            </Button>
          </div>
        </div>

        {/* Wellness Plans */}
        <div className="grid lg:grid-cols-3 gap-8 mb-12">
          {wellnessPlans.map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className={`h-full relative ${
                plan.recommended ? 'ring-2 ring-green-500 scale-105' : ''
              } ${plan.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {plan.recommended && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-green-500 text-white">
                      <Award className="w-3 h-3 mr-1" />
                      Recommended
                    </Badge>
                  </div>
                )}
                {plan.popular && (
                  <div className="absolute -top-3 right-4">
                    <Badge className="bg-blue-500 text-white">
                      <Star className="w-3 h-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
                
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <CardDescription className="text-lg font-medium">{plan.ageGroup}</CardDescription>
                  <div className="text-4xl font-bold text-orange-600">
                    {plan.price[billingCycle]}
                    <span className="text-lg font-normal text-gray-500">
                      /{billingCycle === "monthly" ? "month" : "year"}
                    </span>
                  </div>
                  {billingCycle === "annual" && plan.price.savings && (
                    <Badge className="bg-green-100 text-green-800">
                      {plan.price.savings}
                    </Badge>
                  )}
                </CardHeader>
                
                <CardContent className="space-y-6">
                  {/* Features */}
                  <div>
                    <h4 className="font-semibold mb-3">What's Included</h4>
                    <div className="space-y-2">
                      {plan.features.map((feature, idx) => (
                        <div key={idx} className="flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Benefits */}
                  <div>
                    <h4 className="font-semibold mb-3">Key Benefits</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {plan.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-start">
                          <Heart className="w-3 h-3 text-orange-500 mr-2 mt-1 flex-shrink-0" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <Button 
                    className={`w-full ${
                      plan.recommended 
                        ? 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800' 
                        : plan.popular
                        ? 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800'
                        : 'bg-gradient-to-r from-orange-600 to-yellow-600 hover:from-orange-700 hover:to-yellow-700'
                    }`}
                    onClick={() => setSelectedPlan(plan.id)}
                  >
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    Choose {plan.name}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* How It Works */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-center">How Pumpkin Wellness Club Works</CardTitle>
            <CardDescription className="text-center">
              Simple, comprehensive wellness care for your pet
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-4 gap-6">
              {[
                {
                  step: "1",
                  title: "Choose Your Plan",
                  description: "Select the wellness plan that fits your pet's age and needs",
                  icon: Gift
                },
                {
                  step: "2",
                  title: "Schedule Care",
                  description: "Book appointments online or call our dedicated member line",
                  icon: Calendar
                },
                {
                  step: "3",
                  title: "Visit Your Vet",
                  description: "Receive comprehensive care at our network of trusted clinics",
                  icon: Stethoscope
                },
                {
                  step: "4",
                  title: "Stay Healthy",
                  description: "Follow personalized care plans and track your pet's health",
                  icon: Activity
                }
              ].map((step, index) => (
                <div key={step.step} className="text-center">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <step.icon className="w-8 h-8 text-orange-600" />
                  </div>
                  <div className="text-lg font-semibold mb-2">Step {step.step}</div>
                  <div className="font-medium mb-2">{step.title}</div>
                  <div className="text-sm text-gray-600">{step.description}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Additional Services */}
        <Tabs defaultValue="services" className="w-full mb-8">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="services">Additional Services</TabsTrigger>
            <TabsTrigger value="locations">Clinic Locations</TabsTrigger>
            <TabsTrigger value="support">Member Support</TabsTrigger>
          </TabsList>

          <TabsContent value="services" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Add-On Services</CardTitle>
                <CardDescription>
                  Enhance your wellness plan with additional services
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center">
                      <Pill className="w-4 h-4 mr-2 text-blue-600" />
                      Pharmacy Services
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Prescription medications</li>
                      <li>• Flea and tick prevention</li>
                      <li>• Heartworm prevention</li>
                      <li>• Nutritional supplements</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center">
                      <Activity className="w-4 h-4 mr-2 text-green-600" />
                      Specialized Care
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Behavioral consultations</li>
                      <li>• Nutritional counseling</li>
                      <li>• Grooming services</li>
                      <li>• Emergency care discounts</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="locations" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Find a Clinic Near You</CardTitle>
                <CardDescription>
                  Over 500 participating veterinary clinics nationwide
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid md:grid-cols-3 gap-4">
                    <div className="bg-orange-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-orange-600">500+</div>
                      <div className="text-sm text-gray-600">Participating Clinics</div>
                    </div>
                    <div className="bg-blue-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-blue-600">50</div>
                      <div className="text-sm text-gray-600">States Covered</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-green-600">24/7</div>
                      <div className="text-sm text-gray-600">Support Available</div>
                    </div>
                  </div>
                  <Button className="w-full">
                    Find Clinics Near Me
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="support" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Member Support</CardTitle>
                <CardDescription>
                  We're here to help you and your pet every step of the way
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center">
                      <Phone className="w-4 h-4 mr-2 text-blue-600" />
                      Contact Options
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <strong>Member Hotline:</strong> 1-800-PUMPKIN
                      </div>
                      <div>
                        <strong>Email Support:</strong> <EMAIL>
                      </div>
                      <div>
                        <strong>Live Chat:</strong> Available 24/7 on our website
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center">
                      <Clock className="w-4 h-4 mr-2 text-green-600" />
                      Support Hours
                    </h4>
                    <div className="space-y-1 text-sm text-gray-600">
                      <div>Monday - Friday: 6 AM - 10 PM EST</div>
                      <div>Saturday - Sunday: 8 AM - 8 PM EST</div>
                      <div>Emergency Support: 24/7</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Testimonials */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>What Pet Parents Say</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              {[
                {
                  name: "Jennifer L.",
                  pet: "Golden Retriever",
                  review: "The wellness plan has saved us hundreds of dollars and Max is healthier than ever!",
                  rating: 5
                },
                {
                  name: "David M.",
                  pet: "Persian Cat",
                  review: "Love the convenience and the quality of care. The vets are amazing with Luna.",
                  rating: 5
                },
                {
                  name: "Sarah K.",
                  pet: "Mixed Breed",
                  review: "Best decision we made for our senior dog. The comprehensive care is outstanding.",
                  rating: 5
                }
              ].map((testimonial, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-center mb-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-4 h-4 ${
                            star <= testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <p className="text-sm text-gray-600 mb-3">"{testimonial.review}"</p>
                    <div className="text-sm">
                      <div className="font-medium">{testimonial.name}</div>
                      <div className="text-gray-500">{testimonial.pet} parent</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Alert className="border-orange-200 bg-orange-50">
          <Heart className="h-4 w-4 text-orange-600" />
          <AlertTitle className="text-orange-800">Ready to Give Your Pet the Best Care?</AlertTitle>
          <AlertDescription className="text-orange-700">
            <div className="mt-2">
              <p className="mb-3">Join thousands of pet parents who trust Pumpkin Wellness Club for their pet's health.</p>
              <div className="flex gap-4">
                <Button className="bg-orange-600 hover:bg-orange-700">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Get Started Today
                </Button>
                <Button variant="outline" className="border-orange-600 text-orange-600">
                  <Phone className="w-4 h-4 mr-2" />
                  Call 1-800-PUMPKIN
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
