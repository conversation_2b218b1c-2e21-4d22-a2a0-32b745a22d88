"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  MapPin,
  Shield,
  Battery,
  Smartphone,
  Heart,
  Star,
  ShoppingCart,
  Zap,
  Activity,
  Bell,
  Wifi,
  Clock,
  Award,
  CheckCircle,
  Truck,
  CreditCard,
  Gift,
  Users
} from "lucide-react"
import { motion } from "framer-motion"

interface ByteTagProduct {
  id: string
  name: string
  price: string
  originalPrice?: string
  features: string[]
  batteryLife: string
  range: string
  waterproof: boolean
  popular?: boolean
  new?: boolean
  color: string
  image: string
}

const byteTagProducts: ByteTagProduct[] = [
  {
    id: "bytetag-pro",
    name: "ByteTag Pro",
    price: "$89.99",
    originalPrice: "$109.99",
    features: [
      "Real-time GPS tracking",
      "Activity monitoring",
      "Safe zone alerts",
      "Health insights",
      "2-year warranty"
    ],
    batteryLife: "Up to 7 days",
    range: "Unlimited (cellular)",
    waterproof: true,
    popular: true,
    color: "blue",
    image: "/images/bytetag-pro.jpg"
  },
  {
    id: "bytetag-lite",
    name: "ByteTag Lite",
    price: "$59.99",
    features: [
      "GPS tracking",
      "Basic activity monitoring",
      "Safe zone alerts",
      "1-year warranty"
    ],
    batteryLife: "Up to 5 days",
    range: "Unlimited (cellular)",
    waterproof: true,
    color: "green",
    image: "/images/bytetag-lite.jpg"
  },
  {
    id: "bytetag-max",
    name: "ByteTag Max",
    price: "$129.99",
    features: [
      "Advanced GPS tracking",
      "Comprehensive health monitoring",
      "Smart alerts",
      "Vet integration",
      "3-year warranty",
      "Premium app features"
    ],
    batteryLife: "Up to 10 days",
    range: "Unlimited (cellular)",
    waterproof: true,
    new: true,
    color: "purple",
    image: "/images/bytetag-max.jpg"
  }
]

const subscriptionPlans = [
  {
    name: "Basic Plan",
    price: "$4.99/month",
    features: [
      "Real-time location tracking",
      "Location history",
      "Safe zone alerts",
      "Basic customer support"
    ]
  },
  {
    name: "Premium Plan",
    price: "$9.99/month",
    features: [
      "Everything in Basic",
      "Health & activity insights",
      "Vet integration",
      "Advanced alerts",
      "Priority customer support",
      "Family sharing"
    ],
    popular: true
  },
  {
    name: "Family Plan",
    price: "$14.99/month",
    features: [
      "Everything in Premium",
      "Up to 5 pets",
      "Multi-user access",
      "Advanced analytics",
      "24/7 phone support"
    ]
  }
]

export default function ShopByteTagPage() {
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null)
  const [selectedPlan, setSelectedPlan] = useState<string>("Premium Plan")

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              ByteTag
              <span className="block text-blue-300">Smart Pet Tracking</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-purple-100">
              Never lose your pet again with advanced GPS tracking and health monitoring
            </p>
            <div className="flex justify-center gap-4">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                Shop Now
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                Learn More
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Key Features */}
        <div className="grid md:grid-cols-4 gap-6 mb-12">
          {[
            { icon: MapPin, title: "Real-Time GPS", description: "Track your pet's location anywhere in the world" },
            { icon: Activity, title: "Health Monitoring", description: "Monitor activity, sleep, and health metrics" },
            { icon: Bell, title: "Smart Alerts", description: "Get notified when your pet leaves safe zones" },
            { icon: Battery, title: "Long Battery Life", description: "Up to 10 days of continuous tracking" }
          ].map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="text-center h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold mb-2">{feature.title}</h3>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Product Showcase */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-center mb-8">Choose Your ByteTag</h2>
          <div className="grid lg:grid-cols-3 gap-8">
            {byteTagProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card className={`h-full relative ${
                  product.popular ? 'ring-2 ring-blue-500 scale-105' : ''
                } ${product.new ? 'ring-2 ring-purple-500' : ''}`}>
                  {product.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-blue-500 text-white">
                        <Star className="w-3 h-3 mr-1" />
                        Most Popular
                      </Badge>
                    </div>
                  )}
                  {product.new && (
                    <div className="absolute -top-3 right-4">
                      <Badge className="bg-purple-500 text-white">
                        <Zap className="w-3 h-3 mr-1" />
                        New
                      </Badge>
                    </div>
                  )}
                  
                  <CardHeader className="text-center">
                    <div className="w-32 h-32 bg-gray-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
                      <div className={`w-20 h-20 bg-${product.color}-500 rounded-full flex items-center justify-center`}>
                        <MapPin className="w-10 h-10 text-white" />
                      </div>
                    </div>
                    <CardTitle className="text-2xl">{product.name}</CardTitle>
                    <div className="text-3xl font-bold text-blue-600">
                      {product.price}
                      {product.originalPrice && (
                        <span className="text-lg font-normal text-gray-500 line-through ml-2">
                          {product.originalPrice}
                        </span>
                      )}
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {/* Features */}
                    <div>
                      <h4 className="font-semibold mb-3">Features</h4>
                      <div className="space-y-2">
                        {product.features.map((feature, idx) => (
                          <div key={idx} className="flex items-start">
                            <CheckCircle className="w-4 h-4 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Specs */}
                    <div className="border-t pt-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="font-medium">Battery Life</div>
                          <div className="text-gray-600">{product.batteryLife}</div>
                        </div>
                        <div>
                          <div className="font-medium">Range</div>
                          <div className="text-gray-600">{product.range}</div>
                        </div>
                        <div className="col-span-2">
                          <div className="flex items-center">
                            <Shield className="w-4 h-4 text-blue-500 mr-2" />
                            <span className="text-sm">
                              {product.waterproof ? "Waterproof" : "Water Resistant"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <Button 
                      className={`w-full ${
                        product.popular 
                          ? 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800' 
                          : product.new
                          ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800'
                          : 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800'
                      }`}
                      onClick={() => setSelectedProduct(product.id)}
                    >
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      Add to Cart
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Subscription Plans */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-center">Choose Your Service Plan</CardTitle>
            <CardDescription className="text-center">
              Monthly subscription required for cellular connectivity and app features
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              {subscriptionPlans.map((plan, index) => (
                <Card key={plan.name} className={`${plan.popular ? 'ring-2 ring-blue-500' : ''}`}>
                  {plan.popular && (
                    <div className="bg-blue-500 text-white text-center py-2 text-sm font-medium">
                      Most Popular
                    </div>
                  )}
                  <CardHeader className="text-center">
                    <CardTitle className="text-lg">{plan.name}</CardTitle>
                    <div className="text-2xl font-bold text-blue-600">{plan.price}</div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {plan.features.map((feature, idx) => (
                        <div key={idx} className="flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                    <Button 
                      className={`w-full mt-4 ${
                        plan.popular ? 'bg-blue-600 hover:bg-blue-700' : ''
                      }`}
                      variant={plan.popular ? "default" : "outline"}
                    >
                      Select Plan
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* App Features */}
        <Tabs defaultValue="tracking" className="w-full mb-8">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="tracking">Tracking</TabsTrigger>
            <TabsTrigger value="health">Health</TabsTrigger>
            <TabsTrigger value="alerts">Alerts</TabsTrigger>
            <TabsTrigger value="sharing">Sharing</TabsTrigger>
          </TabsList>

          <TabsContent value="tracking" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="w-5 h-5 mr-2 text-blue-600" />
                  Advanced GPS Tracking
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Real-Time Location</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Live GPS tracking with 1-meter accuracy</li>
                      <li>• Location history and timeline</li>
                      <li>• Indoor and outdoor tracking</li>
                      <li>• Works worldwide with cellular coverage</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Smart Features</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Automatic location updates</li>
                      <li>• Battery-saving sleep mode</li>
                      <li>• Weather-resistant design</li>
                      <li>• Easy attachment to any collar</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="health" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="w-5 h-5 mr-2 text-green-600" />
                  Health & Activity Monitoring
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Activity Tracking</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Daily step counting</li>
                      <li>• Active vs. rest time</li>
                      <li>• Calorie burn estimation</li>
                      <li>• Exercise goal setting</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Health Insights</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Sleep pattern analysis</li>
                      <li>• Behavior change detection</li>
                      <li>• Vet report generation</li>
                      <li>• Health trend monitoring</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="alerts" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Bell className="w-5 h-5 mr-2 text-orange-600" />
                  Smart Alerts & Notifications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Safety Alerts</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Safe zone entry/exit notifications</li>
                      <li>• Escape alerts with location</li>
                      <li>• Low battery warnings</li>
                      <li>• Device offline notifications</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Health Alerts</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Unusual activity pattern alerts</li>
                      <li>• Extended inactivity warnings</li>
                      <li>• Vet appointment reminders</li>
                      <li>• Medication schedule alerts</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sharing" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="w-5 h-5 mr-2 text-purple-600" />
                  Family Sharing & Collaboration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Multi-User Access</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Share access with family members</li>
                      <li>• Role-based permissions</li>
                      <li>• Multiple device support</li>
                      <li>• Synchronized notifications</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Collaboration Features</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Shared care schedules</li>
                      <li>• Group messaging</li>
                      <li>• Vet information sharing</li>
                      <li>• Emergency contact system</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Customer Reviews */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>What Pet Parents Say</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              {[
                {
                  name: "Emily R.",
                  pet: "Labrador Mix",
                  review: "ByteTag saved my dog's life! He escaped during a thunderstorm and I found him within 30 minutes.",
                  rating: 5
                },
                {
                  name: "Michael T.",
                  pet: "Siamese Cat",
                  review: "Love the health monitoring features. It helped us catch an issue early and get proper treatment.",
                  rating: 5
                },
                {
                  name: "Lisa K.",
                  pet: "German Shepherd",
                  review: "The battery life is amazing and the app is so easy to use. Highly recommend!",
                  rating: 5
                }
              ].map((review, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-center mb-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-4 h-4 ${
                            star <= review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <p className="text-sm text-gray-600 mb-3">"{review.review}"</p>
                    <div className="text-sm">
                      <div className="font-medium">{review.name}</div>
                      <div className="text-gray-500">{review.pet} parent</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Purchase Information */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Purchase Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="flex items-center">
                <Truck className="w-8 h-8 text-blue-600 mr-4" />
                <div>
                  <h4 className="font-semibold">Free Shipping</h4>
                  <p className="text-sm text-gray-600">On orders over $50</p>
                </div>
              </div>
              <div className="flex items-center">
                <Shield className="w-8 h-8 text-green-600 mr-4" />
                <div>
                  <h4 className="font-semibold">30-Day Return</h4>
                  <p className="text-sm text-gray-600">Money-back guarantee</p>
                </div>
              </div>
              <div className="flex items-center">
                <Award className="w-8 h-8 text-purple-600 mr-4" />
                <div>
                  <h4 className="font-semibold">2-Year Warranty</h4>
                  <p className="text-sm text-gray-600">Full device protection</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Alert className="border-blue-200 bg-blue-50">
          <Heart className="h-4 w-4 text-blue-600" />
          <AlertTitle className="text-blue-800">Ready to Keep Your Pet Safe?</AlertTitle>
          <AlertDescription className="text-blue-700">
            <div className="mt-2">
              <p className="mb-3">Join over 100,000 pet parents who trust ByteTag to keep their pets safe and healthy.</p>
              <div className="flex gap-4">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Shop ByteTag Now
                </Button>
                <Button variant="outline" className="border-blue-600 text-blue-600">
                  <Smartphone className="w-4 h-4 mr-2" />
                  Download App
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
