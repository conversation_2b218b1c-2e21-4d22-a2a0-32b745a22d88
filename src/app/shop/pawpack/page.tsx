"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Gift,
  Heart,
  Star,
  ShoppingCart,
  Package,
  Truck,
  Calendar,
  DollarSign,
  Award,
  CheckCircle,
  Smile,
  Bone,
  Cat,
  Dog,
  Sparkles,
  Clock,
  Users,
  RefreshCw
} from "lucide-react"
import { motion } from "framer-motion"

interface PawPackPlan {
  id: string
  name: string
  price: {
    monthly: string
    quarterly: string
    annual: string
  }
  petType: "dog" | "cat" | "both"
  items: number
  features: string[]
  benefits: string[]
  popular?: boolean
  savings?: string
  color: string
}

const pawPackPlans: PawPackPlan[] = [
  {
    id: "puppy-starter",
    name: "<PERSON>uppy Starter Pack",
    price: { monthly: "$24.99", quarterly: "$69.99", annual: "$249.99" },
    petType: "dog",
    items: 4,
    features: [
      "4-5 premium toys & treats",
      "Training aids & guides",
      "Puppy-safe products only",
      "Size-appropriate items",
      "Educational materials"
    ],
    benefits: [
      "Perfect for new puppy parents",
      "Age-appropriate development",
      "Training support included",
      "Vet-approved products"
    ],
    popular: true,
    savings: "Save $50",
    color: "blue"
  },
  {
    id: "dog-deluxe",
    name: "Dog Deluxe Box",
    price: { monthly: "$34.99", quarterly: "$99.99", annual: "$349.99" },
    petType: "dog",
    items: 6,
    features: [
      "6-7 premium items",
      "Mix of toys, treats & accessories",
      "Size customization available",
      "Seasonal themed items",
      "Exclusive brand partnerships"
    ],
    benefits: [
      "Best value for dog owners",
      "Variety keeps dogs engaged",
      "High-quality brands",
      "Surprise elements"
    ],
    savings: "Save $70",
    color: "green"
  },
  {
    id: "cat-comfort",
    name: "Cat Comfort Collection",
    price: { monthly: "$29.99", quarterly: "$84.99", annual: "$299.99" },
    petType: "cat",
    items: 5,
    features: [
      "5-6 cat-specific items",
      "Interactive toys & puzzles",
      "Gourmet treats & catnip",
      "Comfort accessories",
      "Wellness products"
    ],
    benefits: [
      "Designed for feline preferences",
      "Mental stimulation focus",
      "Stress-relief items",
      "Indoor cat friendly"
    ],
    savings: "Save $60",
    color: "purple"
  },
  {
    id: "multi-pet",
    name: "Multi-Pet Family Box",
    price: { monthly: "$49.99", quarterly: "$144.99", annual: "$499.99" },
    petType: "both",
    items: 8,
    features: [
      "8-10 items for dogs & cats",
      "Customizable pet profiles",
      "Shared toys & separate treats",
      "Family-friendly activities",
      "Bulk savings included"
    ],
    benefits: [
      "Perfect for multi-pet homes",
      "Cost-effective solution",
      "Promotes pet bonding",
      "Reduces shipping costs"
    ],
    savings: "Save $100",
    color: "orange"
  }
]

const sampleItems = [
  {
    category: "Toys",
    items: [
      "Interactive puzzle toys",
      "Rope toys for tugging",
      "Squeaky plush animals",
      "Feather wands for cats",
      "Treat-dispensing balls"
    ]
  },
  {
    category: "Treats",
    items: [
      "Grain-free training treats",
      "Dental chews",
      "Freeze-dried meat",
      "Catnip-infused snacks",
      "Limited ingredient treats"
    ]
  },
  {
    category: "Accessories",
    items: [
      "Stylish bandanas",
      "Grooming tools",
      "Food & water bowls",
      "Comfort blankets",
      "ID tags & collars"
    ]
  },
  {
    category: "Wellness",
    items: [
      "Supplements & vitamins",
      "Calming aids",
      "Dental care products",
      "Skin & coat treatments",
      "Joint support chews"
    ]
  }
]

export default function PawPackPage() {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)
  const [billingCycle, setBillingCycle] = useState<"monthly" | "quarterly" | "annual">("annual")

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-pink-600 to-orange-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              PawPack
              <span className="block text-pink-300">Subscription Boxes</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-orange-100">
              Monthly surprises delivered to your door - toys, treats, and more!
            </p>
            <div className="flex justify-center gap-4">
              <Button size="lg" className="bg-white text-pink-600 hover:bg-gray-100">
                <Gift className="w-5 h-5 mr-2" />
                Start Subscription
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-pink-600">
                View Sample Box
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Why PawPack */}
        <div className="grid md:grid-cols-4 gap-6 mb-12">
          {[
            { icon: Gift, title: "Monthly Surprises", description: "Carefully curated items delivered every month" },
            { icon: Heart, title: "Pet-Approved", description: "Every item tested and loved by real pets" },
            { icon: Award, title: "Premium Brands", description: "Only the highest quality products included" },
            { icon: Smile, title: "Happiness Guaranteed", description: "100% satisfaction or your money back" }
          ].map((benefit, index) => (
            <motion.div
              key={benefit.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="text-center h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <benefit.icon className="w-6 h-6 text-pink-600" />
                  </div>
                  <h3 className="font-semibold mb-2">{benefit.title}</h3>
                  <p className="text-sm text-gray-600">{benefit.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Billing Toggle */}
        <div className="flex justify-center mb-8">
          <div className="bg-gray-100 p-1 rounded-lg">
            <Button
              variant={billingCycle === "monthly" ? "default" : "ghost"}
              size="sm"
              onClick={() => setBillingCycle("monthly")}
            >
              Monthly
            </Button>
            <Button
              variant={billingCycle === "quarterly" ? "default" : "ghost"}
              size="sm"
              onClick={() => setBillingCycle("quarterly")}
            >
              Quarterly
              <Badge className="ml-2 bg-blue-500 text-white">Save 15%</Badge>
            </Button>
            <Button
              variant={billingCycle === "annual" ? "default" : "ghost"}
              size="sm"
              onClick={() => setBillingCycle("annual")}
            >
              Annual
              <Badge className="ml-2 bg-green-500 text-white">Save 30%</Badge>
            </Button>
          </div>
        </div>

        {/* Subscription Plans */}
        <div className="grid lg:grid-cols-2 xl:grid-cols-4 gap-6 mb-12">
          {pawPackPlans.map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className={`h-full relative ${
                plan.popular ? 'ring-2 ring-pink-500 scale-105' : ''
              }`}>
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-pink-500 text-white">
                      <Star className="w-3 h-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
                
                <CardHeader className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-pink-100 to-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    {plan.petType === "dog" && <Dog className="w-8 h-8 text-pink-600" />}
                    {plan.petType === "cat" && <Cat className="w-8 h-8 text-purple-600" />}
                    {plan.petType === "both" && <Heart className="w-8 h-8 text-orange-600" />}
                  </div>
                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                  <CardDescription className="capitalize">
                    For {plan.petType === "both" ? "dogs & cats" : `${plan.petType}s`}
                  </CardDescription>
                  <div className="text-3xl font-bold text-pink-600">
                    {plan.price[billingCycle]}
                    <span className="text-lg font-normal text-gray-500">
                      /{billingCycle}
                    </span>
                  </div>
                  {billingCycle !== "monthly" && plan.savings && (
                    <Badge className="bg-green-100 text-green-800">
                      {plan.savings}
                    </Badge>
                  )}
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-800">{plan.items}+</div>
                    <div className="text-sm text-gray-600">Items per box</div>
                  </div>

                  {/* Features */}
                  <div>
                    <h4 className="font-semibold mb-2">What's Included</h4>
                    <div className="space-y-1">
                      {plan.features.map((feature, idx) => (
                        <div key={idx} className="flex items-start">
                          <CheckCircle className="w-3 h-3 text-green-500 mr-2 mt-1 flex-shrink-0" />
                          <span className="text-xs">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Benefits */}
                  <div>
                    <h4 className="font-semibold mb-2">Benefits</h4>
                    <ul className="text-xs text-gray-600 space-y-1">
                      {plan.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-start">
                          <Sparkles className="w-3 h-3 text-pink-500 mr-2 mt-0.5 flex-shrink-0" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <Button 
                    className={`w-full ${
                      plan.popular 
                        ? 'bg-gradient-to-r from-pink-600 to-pink-700 hover:from-pink-700 hover:to-pink-800' 
                        : 'bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800'
                    }`}
                    onClick={() => setSelectedPlan(plan.id)}
                  >
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    Subscribe Now
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* How It Works */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-center">How PawPack Works</CardTitle>
            <CardDescription className="text-center">
              Simple subscription service that brings joy to your pet every month
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-4 gap-6">
              {[
                {
                  step: "1",
                  title: "Choose Your Plan",
                  description: "Select the perfect subscription for your pet's size and preferences",
                  icon: Gift
                },
                {
                  step: "2",
                  title: "Customize Profile",
                  description: "Tell us about your pet's likes, dislikes, and any allergies",
                  icon: Heart
                },
                {
                  step: "3",
                  title: "Receive Monthly Box",
                  description: "Get a curated box of toys, treats, and accessories delivered",
                  icon: Package
                },
                {
                  step: "4",
                  title: "Enjoy & Repeat",
                  description: "Watch your pet enjoy their surprises and look forward to next month",
                  icon: RefreshCw
                }
              ].map((step, index) => (
                <div key={step.step} className="text-center">
                  <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <step.icon className="w-8 h-8 text-pink-600" />
                  </div>
                  <div className="text-lg font-semibold mb-2">Step {step.step}</div>
                  <div className="font-medium mb-2">{step.title}</div>
                  <div className="text-sm text-gray-600">{step.description}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Sample Items */}
        <Tabs defaultValue="toys" className="w-full mb-8">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="toys">Toys</TabsTrigger>
            <TabsTrigger value="treats">Treats</TabsTrigger>
            <TabsTrigger value="accessories">Accessories</TabsTrigger>
            <TabsTrigger value="wellness">Wellness</TabsTrigger>
          </TabsList>

          {sampleItems.map((category) => (
            <TabsContent key={category.category.toLowerCase()} value={category.category.toLowerCase()} className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Bone className="w-5 h-5 mr-2 text-pink-600" />
                    Sample {category.category}
                  </CardTitle>
                  <CardDescription>
                    Examples of {category.category.toLowerCase()} you might find in your PawPack
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {category.items.map((item, index) => (
                      <div key={index} className="bg-gradient-to-br from-pink-50 to-orange-50 p-4 rounded-lg">
                        <div className="flex items-center">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                          <span className="text-sm font-medium">{item}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        {/* Subscription Benefits */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Subscription Benefits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="flex items-center">
                <Truck className="w-8 h-8 text-blue-600 mr-4" />
                <div>
                  <h4 className="font-semibold">Free Shipping</h4>
                  <p className="text-sm text-gray-600">Always included</p>
                </div>
              </div>
              <div className="flex items-center">
                <Calendar className="w-8 h-8 text-green-600 mr-4" />
                <div>
                  <h4 className="font-semibold">Flexible Scheduling</h4>
                  <p className="text-sm text-gray-600">Skip or pause anytime</p>
                </div>
              </div>
              <div className="flex items-center">
                <Award className="w-8 h-8 text-purple-600 mr-4" />
                <div>
                  <h4 className="font-semibold">Satisfaction Guarantee</h4>
                  <p className="text-sm text-gray-600">100% money back</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customer Reviews */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Happy Customers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              {[
                {
                  name: "Jessica M.",
                  pet: "Golden Retriever",
                  review: "Max goes crazy every time the PawPack arrives! The quality is amazing and he loves every item.",
                  rating: 5
                },
                {
                  name: "Robert K.",
                  pet: "Maine Coon",
                  review: "Perfect for my picky cat. The variety keeps her interested and the treats are always a hit!",
                  rating: 5
                },
                {
                  name: "Amanda S.",
                  pet: "Multiple Pets",
                  review: "The multi-pet box is perfect for our household. Both our dog and cat get excited for delivery day!",
                  rating: 5
                }
              ].map((review, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-center mb-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-4 h-4 ${
                            star <= review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <p className="text-sm text-gray-600 mb-3">"{review.review}"</p>
                    <div className="text-sm">
                      <div className="font-medium">{review.name}</div>
                      <div className="text-gray-500">{review.pet} parent</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* FAQ */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Frequently Asked Questions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Can I customize my box?</h4>
                <p className="text-sm text-gray-600">
                  Yes! Create a detailed pet profile including size, age, preferences, and allergies. 
                  We'll customize each box accordingly.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Can I skip a month?</h4>
                <p className="text-sm text-gray-600">
                  Absolutely! You can skip, pause, or cancel your subscription anytime through your account dashboard.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">What if my pet doesn't like something?</h4>
                <p className="text-sm text-gray-600">
                  We offer a 100% satisfaction guarantee. Contact us and we'll make it right with a replacement or refund.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Do you ship internationally?</h4>
                <p className="text-sm text-gray-600">
                  Currently we ship within the US and Canada. International shipping is coming soon!
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Alert className="border-pink-200 bg-pink-50">
          <Gift className="h-4 w-4 text-pink-600" />
          <AlertTitle className="text-pink-800">Ready to Surprise Your Pet?</AlertTitle>
          <AlertDescription className="text-pink-700">
            <div className="mt-2">
              <p className="mb-3">Join over 50,000 happy pets who get excited for PawPack delivery day!</p>
              <div className="flex gap-4">
                <Button className="bg-pink-600 hover:bg-pink-700">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Start Subscription
                </Button>
                <Button variant="outline" className="border-pink-600 text-pink-600">
                  <Package className="w-4 h-4 mr-2" />
                  View Sample Box
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
