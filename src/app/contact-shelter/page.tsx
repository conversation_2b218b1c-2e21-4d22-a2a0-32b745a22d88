"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Mail, Phone, Clock, MapPin, Send, AlertTriangle } from "lucide-react"
import { toast } from "react-hot-toast"

const INQUIRY_TYPES = [
  { value: "adoption", label: "Adoption Inquiry" },
  { value: "medical", label: "Medical Emergency" },
  { value: "lost-found", label: "Lost & Found" },
  { value: "donation", label: "Donation Information" },
  { value: "volunteer", label: "Volunteering" },
  { value: "other", label: "Other" },
]

const CONTACT_INFO = [
  {
    icon: MapPin,
    title: "Visit Us",
    details: [
      "123 Pet Haven Lane",
      "Compassion City, ST 12345",
      "United States"
    ],
    color: "bg-blue-100 text-blue-600"
  },
  {
    icon: Phone,
    title: "Call Us",
    details: [
      "Main: (*************",
      "Emergency: (555) 911-PETS",
      "Fax: (*************"
    ],
    color: "bg-green-100 text-green-600"
  },
  {
    icon: Mail,
    title: "Email Us",
    details: [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ],
    color: "bg-purple-100 text-purple-600"
  },
  {
    icon: Clock,
    title: "Hours",
    details: [
      "Mon-Fri: 9:00 AM - 6:00 PM",
      "Sat: 10:00 AM - 4:00 PM",
      "Sun: 11:00 AM - 3:00 PM"
    ],
    color: "bg-orange-100 text-orange-600"
  }
]

export default function ContactShelterPage() {
  const [sending, setSending] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSending(true)

    try {
      // Simulated API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      toast.success("Message sent successfully! We'll get back to you soon.")
      // Reset form
    } catch (error) {
      toast.error("Failed to send message. Please try again.")
    } finally {
      setSending(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white py-12">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Contact Our Shelter
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            We're here to help! Whether you have questions about adoption, want to
            volunteer, or need assistance, our team is ready to assist you.
          </p>
        </div>

        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Send Us a Message</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Your Name</Label>
                        <Input id="name" placeholder="Full Name" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="inquiryType">Type of Inquiry</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select inquiry type" />
                        </SelectTrigger>
                        <SelectContent>
                          {INQUIRY_TYPES.map(type => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message">Your Message</Label>
                      <Textarea
                        id="message"
                        placeholder="How can we help you?"
                        rows={6}
                        required
                      />
                    </div>

                    {/* Emergency Notice */}
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3">
                      <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                      <p className="text-sm text-red-600">
                        For medical emergencies or urgent situations, please call our
                        emergency line at (555) 911-PETS immediately.
                      </p>
                    </div>

                    <div className="flex justify-end">
                      <Button
                        type="submit"
                        disabled={sending}
                        className="bg-primary hover:bg-primary/90"
                      >
                        {sending ? (
                          "Sending..."
                        ) : (
                          <span>
                            Send Message
                            <Send className="h-4 w-4 ml-2" />
                          </span>
                        )}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Contact Information */}
            <div className="space-y-6">
              {CONTACT_INFO.map((info) => (
                <Card key={info.title}>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className={`p-2 rounded-lg ${info.color}`}>
                        <info.icon className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="font-medium mb-2">{info.title}</h3>
                        {info.details.map((detail, index) => (
                          <p key={index} className="text-gray-600 text-sm">
                            {detail}
                          </p>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Map */}
              <Card>
                <CardContent className="p-6">
                  <div className="aspect-video rounded-lg overflow-hidden bg-gray-100">
                    {/* Add Google Maps or other map integration here */}
                    <div className="w-full h-full flex items-center justify-center text-gray-500">
                      Map Integration
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
