"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Shield, 
  Check, 
  Star, 
  Phone, 
  Search, 
  MapPin, 
  Clock, 
  Users,
  Heart,
  Zap,
  Award
} from "lucide-react"
import Link from "next/link"

interface MembershipPlan {
  id: string
  name: string
  price: number
  period: string
  popular?: boolean
  features: string[]
  description: string
  color: string
}

const membershipPlans: MembershipPlan[] = [
  {
    id: "basic",
    name: "Basic Protection",
    price: 19.99,
    period: "year",
    description: "Essential lost pet protection for peace of mind",
    color: "blue",
    features: [
      "24/7 Lost Pet Helpline",
      "Missing Pet Alert Network",
      "Digital Lost Pet Flyers",
      "Basic Search Assistance",
      "Pet Recovery Tips & Guidance",
      "Email Support"
    ]
  },
  {
    id: "premium",
    name: "Premium Protection",
    price: 39.99,
    period: "year",
    popular: true,
    description: "Comprehensive protection with enhanced recovery services",
    color: "green",
    features: [
      "Everything in Basic Plan",
      "Professional Search Coordination",
      "Social Media Campaign Management",
      "Local Volunteer Network Activation",
      "Phone Support Priority",
      "Recovery Reward Program",
      "GPS Tracking Integration",
      "Veterinary Network Alerts"
    ]
  },
  {
    id: "platinum",
    name: "Platinum Protection",
    price: 79.99,
    period: "year",
    description: "Ultimate protection with dedicated recovery specialists",
    color: "purple",
    features: [
      "Everything in Premium Plan",
      "Dedicated Recovery Specialist",
      "Professional Search Team Deployment",
      "Drone Search Services",
      "K-9 Search Team Coordination",
      "Media Relations Support",
      "Legal Assistance",
      "Emergency Transportation",
      "24/7 Phone Support",
      "Recovery Guarantee Program"
    ]
  }
]

export default function MembershipsPage() {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)

  const handleSelectPlan = (planId: string) => {
    setSelectedPlan(planId)
    // Here you would typically redirect to a payment page
    console.log(`Selected plan: ${planId}`)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Shield className="h-12 w-12 text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              Lost Pet Protection Memberships
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Choose the protection plan that's right for you and your beloved pet. 
            Our comprehensive services help reunite families with their lost companions.
          </p>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center mb-2">
                <Heart className="h-8 w-8 text-red-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">98%</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Recovery Rate</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center mb-2">
                <Clock className="h-8 w-8 text-blue-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">24hrs</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Average Recovery</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center mb-2">
                <Users className="h-8 w-8 text-green-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">50K+</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Happy Members</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center mb-2">
                <Award className="h-8 w-8 text-yellow-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">15+</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Years Experience</div>
            </CardContent>
          </Card>
        </div>

        {/* Membership Plans */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {membershipPlans.map((plan) => (
            <Card 
              key={plan.id} 
              className={`relative ${plan.popular ? 'ring-2 ring-green-500 scale-105' : ''} transition-transform hover:scale-105`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-green-500 text-white px-4 py-1">
                    <Star className="h-3 w-3 mr-1" />
                    Most Popular
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {plan.name}
                </CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-400">
                  {plan.description}
                </CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-900 dark:text-gray-100">
                    ${plan.price}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">/{plan.period}</span>
                </div>
              </CardHeader>
              
              <CardContent>
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button 
                  className={`w-full ${plan.popular ? 'bg-green-600 hover:bg-green-700' : ''}`}
                  onClick={() => handleSelectPlan(plan.id)}
                >
                  {plan.popular ? (
                    <>
                      <Zap className="h-4 w-4 mr-2" />
                      Choose Premium
                    </>
                  ) : (
                    `Select ${plan.name}`
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* How It Works */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center text-gray-900 dark:text-gray-100">
              How Our Protection Works
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Phone className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">1. Report Lost Pet</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Call our 24/7 hotline or use our app to report your missing pet immediately.
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-green-100 dark:bg-green-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Search className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">2. Activate Search</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Our team immediately activates search protocols and alerts local networks.
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-purple-100 dark:bg-purple-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <MapPin className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">3. Coordinate Recovery</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Professional search teams and volunteers work together to locate your pet.
                </p>
              </div>
              
              <div className="text-center">
                <div className="bg-red-100 dark:bg-red-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Heart className="h-8 w-8 text-red-600" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">4. Reunite Family</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  We facilitate the safe return of your beloved pet to your family.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* FAQ */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center text-gray-900 dark:text-gray-100">
              Frequently Asked Questions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  What's included in the membership?
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Each membership includes 24/7 support, search coordination, and access to our recovery network. 
                  Higher tiers include additional services like professional search teams and dedicated specialists.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  How quickly do you respond?
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  We respond immediately to lost pet reports. Our 24/7 hotline ensures you get help 
                  the moment you need it, day or night.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Can I upgrade my membership?
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Yes, you can upgrade your membership at any time. The price difference will be 
                  prorated for the remaining period of your current membership.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  What if my pet is never found?
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  While our success rate is 98%, Platinum members are covered by our Recovery Guarantee Program, 
                  which provides additional support and compensation in rare cases.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA */}
        <div className="text-center mt-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Ready to Protect Your Pet?
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Join thousands of pet parents who trust us to keep their furry family members safe.
          </p>
          <div className="space-x-4">
            <Link href="/lost-pet-protection/report-lost">
              <Button variant="outline" size="lg">
                Report Lost Pet
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg">
                Contact Us
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
