"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Search, 
  MapPin, 
  Calendar, 
  Phone, 
  Mail, 
  Upload,
  AlertTriangle,
  Clock,
  Heart,
  CheckCircle
} from "lucide-react"
import { toast } from "react-hot-toast"

interface LostPetForm {
  // Pet Information
  petName: string
  species: string
  breed: string
  age: string
  gender: string
  size: string
  color: string
  markings: string
  microchipId: string
  
  // Lost Information
  lastSeenDate: string
  lastSeenTime: string
  lastSeenLocation: string
  circumstances: string
  
  // Owner Information
  ownerName: string
  ownerPhone: string
  ownerEmail: string
  ownerAddress: string
  
  // Additional Information
  rewardAmount: string
  additionalInfo: string
  photos: File[]
}

export default function ReportLostPetPage() {
  const [formData, setFormData] = useState<LostPetForm>({
    petName: "",
    species: "",
    breed: "",
    age: "",
    gender: "",
    size: "",
    color: "",
    markings: "",
    microchipId: "",
    lastSeenDate: "",
    lastSeenTime: "",
    lastSeenLocation: "",
    circumstances: "",
    ownerName: "",
    ownerPhone: "",
    ownerEmail: "",
    ownerAddress: "",
    rewardAmount: "",
    additionalInfo: "",
    photos: []
  })

  const [submitting, setSubmitting] = useState(false)
  const [step, setStep] = useState(1)

  const handleInputChange = (field: keyof LostPetForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFormData(prev => ({
        ...prev,
        photos: Array.from(e.target.files || [])
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      // Create FormData for file upload
      const submitData = new FormData()
      
      // Add all form fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'photos') {
          formData.photos.forEach((file, index) => {
            submitData.append(`photo_${index}`, file)
          })
        } else {
          submitData.append(key, value as string)
        }
      })

      const response = await fetch("/api/lost-pets/report", {
        method: "POST",
        body: submitData,
      })

      if (response.ok) {
        toast.success("Lost pet report submitted successfully! We'll start searching immediately.")
        // Reset form or redirect
        setFormData({
          petName: "",
          species: "",
          breed: "",
          age: "",
          gender: "",
          size: "",
          color: "",
          markings: "",
          microchipId: "",
          lastSeenDate: "",
          lastSeenTime: "",
          lastSeenLocation: "",
          circumstances: "",
          ownerName: "",
          ownerPhone: "",
          ownerEmail: "",
          ownerAddress: "",
          rewardAmount: "",
          additionalInfo: "",
          photos: []
        })
        setStep(1)
      } else {
        const error = await response.json()
        toast.error(error.error || "Failed to submit report")
      }
    } catch (error) {
      console.error("Error submitting report:", error)
      toast.error("Failed to submit report")
    } finally {
      setSubmitting(false)
    }
  }

  const nextStep = () => {
    if (step < 4) setStep(step + 1)
  }

  const prevStep = () => {
    if (step > 1) setStep(step - 1)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Search className="h-12 w-12 text-red-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              Report a Lost Pet
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Every minute counts when your pet is missing. Fill out this form to immediately 
            activate our search network and recovery services.
          </p>
        </div>

        {/* Emergency Notice */}
        <Card className="mb-8 border-red-200 bg-red-50 dark:bg-red-900/20">
          <CardContent className="pt-6">
            <div className="flex items-start">
              <AlertTriangle className="h-6 w-6 text-red-600 mr-3 mt-1" />
              <div>
                <h3 className="font-semibold text-red-800 dark:text-red-200 mb-2">
                  Time is Critical - Act Fast!
                </h3>
                <div className="text-sm text-red-700 dark:text-red-300 space-y-1">
                  <p>• The first 24 hours are crucial for pet recovery</p>
                  <p>• Call our 24/7 hotline: <strong>(555) 123-PETS</strong> for immediate assistance</p>
                  <p>• Start searching your immediate area while filling out this form</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            {[1, 2, 3, 4].map((stepNumber) => (
              <div key={stepNumber} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step >= stepNumber 
                    ? "bg-blue-600 text-white" 
                    : "bg-gray-200 text-gray-600"
                }`}>
                  {stepNumber}
                </div>
                {stepNumber < 4 && (
                  <div className={`w-16 h-1 ${
                    step > stepNumber ? "bg-blue-600" : "bg-gray-200"
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-center mt-2">
            <span className="text-sm text-gray-600">
              Step {step} of 4: {
                step === 1 ? "Pet Information" :
                step === 2 ? "Lost Details" :
                step === 3 ? "Contact Information" :
                "Additional Details"
              }
            </span>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
          {/* Step 1: Pet Information */}
          {step === 1 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Heart className="h-5 w-5 mr-2" />
                  Pet Information
                </CardTitle>
                <CardDescription>
                  Tell us about your missing pet
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="petName">Pet Name *</Label>
                    <Input
                      id="petName"
                      value={formData.petName}
                      onChange={(e) => handleInputChange("petName", e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="species">Species *</Label>
                    <select
                      id="species"
                      value={formData.species}
                      onChange={(e) => handleInputChange("species", e.target.value)}
                      className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="">Select species</option>
                      <option value="dog">Dog</option>
                      <option value="cat">Cat</option>
                      <option value="bird">Bird</option>
                      <option value="rabbit">Rabbit</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="breed">Breed</Label>
                    <Input
                      id="breed"
                      value={formData.breed}
                      onChange={(e) => handleInputChange("breed", e.target.value)}
                      placeholder="e.g., Golden Retriever, Mixed"
                    />
                  </div>
                  <div>
                    <Label htmlFor="age">Age</Label>
                    <Input
                      id="age"
                      value={formData.age}
                      onChange={(e) => handleInputChange("age", e.target.value)}
                      placeholder="e.g., 3 years, 6 months"
                    />
                  </div>
                  <div>
                    <Label htmlFor="gender">Gender</Label>
                    <select
                      id="gender"
                      value={formData.gender}
                      onChange={(e) => handleInputChange("gender", e.target.value)}
                      className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Select gender</option>
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                      <option value="unknown">Unknown</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="size">Size</Label>
                    <select
                      id="size"
                      value={formData.size}
                      onChange={(e) => handleInputChange("size", e.target.value)}
                      className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Select size</option>
                      <option value="small">Small (under 25 lbs)</option>
                      <option value="medium">Medium (25-60 lbs)</option>
                      <option value="large">Large (60-100 lbs)</option>
                      <option value="extra-large">Extra Large (over 100 lbs)</option>
                    </select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="color">Primary Color/Markings *</Label>
                  <Input
                    id="color"
                    value={formData.color}
                    onChange={(e) => handleInputChange("color", e.target.value)}
                    placeholder="e.g., Brown and white, Black with tan markings"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="markings">Distinctive Markings or Features</Label>
                  <Textarea
                    id="markings"
                    value={formData.markings}
                    onChange={(e) => handleInputChange("markings", e.target.value)}
                    placeholder="Describe any unique markings, scars, or distinctive features..."
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="microchipId">Microchip ID (if known)</Label>
                  <Input
                    id="microchipId"
                    value={formData.microchipId}
                    onChange={(e) => handleInputChange("microchipId", e.target.value)}
                    placeholder="15-digit microchip number"
                  />
                </div>

                <div className="flex justify-end">
                  <Button type="button" onClick={nextStep}>
                    Next Step
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Lost Details */}
          {step === 2 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  When and Where Did You Last See Your Pet?
                </CardTitle>
                <CardDescription>
                  Provide details about when and where your pet went missing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="lastSeenDate">Last Seen Date *</Label>
                    <Input
                      id="lastSeenDate"
                      type="date"
                      value={formData.lastSeenDate}
                      onChange={(e) => handleInputChange("lastSeenDate", e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastSeenTime">Last Seen Time</Label>
                    <Input
                      id="lastSeenTime"
                      type="time"
                      value={formData.lastSeenTime}
                      onChange={(e) => handleInputChange("lastSeenTime", e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="lastSeenLocation">Last Seen Location *</Label>
                  <Input
                    id="lastSeenLocation"
                    value={formData.lastSeenLocation}
                    onChange={(e) => handleInputChange("lastSeenLocation", e.target.value)}
                    placeholder="Street address, intersection, or landmark"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="circumstances">How Did Your Pet Go Missing?</Label>
                  <Textarea
                    id="circumstances"
                    value={formData.circumstances}
                    onChange={(e) => handleInputChange("circumstances", e.target.value)}
                    placeholder="Describe the circumstances - escaped from yard, slipped collar during walk, etc."
                    rows={4}
                  />
                </div>

                <div className="flex justify-between">
                  <Button type="button" variant="outline" onClick={prevStep}>
                    Previous
                  </Button>
                  <Button type="button" onClick={nextStep}>
                    Next Step
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 3: Contact Information */}
          {step === 3 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Phone className="h-5 w-5 mr-2" />
                  Your Contact Information
                </CardTitle>
                <CardDescription>
                  How can we reach you with updates?
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="ownerName">Your Name *</Label>
                    <Input
                      id="ownerName"
                      value={formData.ownerName}
                      onChange={(e) => handleInputChange("ownerName", e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="ownerPhone">Phone Number *</Label>
                    <Input
                      id="ownerPhone"
                      type="tel"
                      value={formData.ownerPhone}
                      onChange={(e) => handleInputChange("ownerPhone", e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="ownerEmail">Email Address *</Label>
                    <Input
                      id="ownerEmail"
                      type="email"
                      value={formData.ownerEmail}
                      onChange={(e) => handleInputChange("ownerEmail", e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="ownerAddress">Your Address</Label>
                  <Textarea
                    id="ownerAddress"
                    value={formData.ownerAddress}
                    onChange={(e) => handleInputChange("ownerAddress", e.target.value)}
                    placeholder="Your home address"
                    rows={3}
                  />
                </div>

                <div className="flex justify-between">
                  <Button type="button" variant="outline" onClick={prevStep}>
                    Previous
                  </Button>
                  <Button type="button" onClick={nextStep}>
                    Next Step
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 4: Additional Details */}
          {step === 4 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Upload className="h-5 w-5 mr-2" />
                  Photos and Additional Information
                </CardTitle>
                <CardDescription>
                  Upload photos and provide any additional details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="photos">Pet Photos</Label>
                  <Input
                    id="photos"
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleFileChange}
                    className="mt-1"
                  />
                  <p className="text-sm text-gray-600 mt-1">
                    Upload recent, clear photos of your pet. Multiple angles are helpful.
                  </p>
                </div>

                <div>
                  <Label htmlFor="rewardAmount">Reward Amount (Optional)</Label>
                  <Input
                    id="rewardAmount"
                    value={formData.rewardAmount}
                    onChange={(e) => handleInputChange("rewardAmount", e.target.value)}
                    placeholder="e.g., $500"
                  />
                </div>

                <div>
                  <Label htmlFor="additionalInfo">Additional Information</Label>
                  <Textarea
                    id="additionalInfo"
                    value={formData.additionalInfo}
                    onChange={(e) => handleInputChange("additionalInfo", e.target.value)}
                    placeholder="Any other information that might help find your pet..."
                    rows={4}
                  />
                </div>

                <div className="flex justify-between">
                  <Button type="button" variant="outline" onClick={prevStep}>
                    Previous
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={submitting}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    {submitting ? (
                      <>
                        <Clock className="h-4 w-4 mr-2 animate-spin" />
                        Submitting Report...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Submit Lost Pet Report
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </form>

        {/* Quick Tips */}
        <Card className="mt-8 max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>Immediate Action Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Search Your Area</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Check hiding spots (under porches, in garages)</li>
                  <li>• Ask neighbors to check their properties</li>
                  <li>• Post on local social media groups</li>
                  <li>• Contact local shelters and veterinarians</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Create Visibility</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Put out familiar scents (your clothing, their bed)</li>
                  <li>• Leave food and water outside</li>
                  <li>• Create and post flyers with photos</li>
                  <li>• Use our 24/7 hotline: (555) 123-PETS</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
