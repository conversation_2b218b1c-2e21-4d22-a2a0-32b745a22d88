"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Search, 
  MapPin, 
  Clock, 
  Phone,
  AlertTriangle,
  CheckCircle,
  Heart,
  Share2,
  Download,
  Eye,
  BookOpen,
  FileText,
  Video,
  ExternalLink,
  Filter,
  Star,
  Users,
  Shield,
  Camera,
  Megaphone
} from "lucide-react"

interface Resource {
  id: string
  title: string
  description: string
  category: "guide" | "template" | "checklist" | "video" | "tool" | "contact"
  type: "pdf" | "video" | "article" | "tool" | "contact"
  url: string
  downloadUrl?: string
  featured: boolean
  tags: string[]
  views: number
  rating: number
}

const resources: Resource[] = [
  {
    id: "1",
    title: "Lost Pet Action Plan - First 24 Hours",
    description: "Step-by-step guide for the critical first 24 hours when your pet goes missing. Includes immediate actions, search strategies, and contact lists.",
    category: "guide",
    type: "pdf",
    url: "/resources/lost-pet-action-plan.pdf",
    downloadUrl: "/resources/lost-pet-action-plan.pdf",
    featured: true,
    tags: ["emergency", "action-plan", "search", "immediate"],
    views: 2847,
    rating: 4.9
  },
  {
    id: "2",
    title: "Missing Pet Poster Template",
    description: "Professional poster template with proven design elements that get results. Includes tips for effective poster placement and messaging.",
    category: "template",
    type: "pdf",
    url: "/resources/missing-pet-poster-template.pdf",
    downloadUrl: "/resources/missing-pet-poster-template.pdf",
    featured: true,
    tags: ["poster", "template", "design", "flyer"],
    views: 1923,
    rating: 4.8
  },
  {
    id: "3",
    title: "Search and Rescue Techniques",
    description: "Comprehensive video guide on effective search techniques, including scent tracking, calling methods, and volunteer coordination.",
    category: "video",
    type: "video",
    url: "/resources/search-rescue-video",
    featured: true,
    tags: ["search", "rescue", "techniques", "training"],
    views: 1456,
    rating: 4.7
  },
  {
    id: "4",
    title: "Social Media Alert Templates",
    description: "Ready-to-use social media templates for Facebook, Instagram, and Twitter. Includes hashtag strategies and sharing tips.",
    category: "template",
    type: "pdf",
    url: "/resources/social-media-templates.pdf",
    downloadUrl: "/resources/social-media-templates.pdf",
    featured: false,
    tags: ["social-media", "template", "facebook", "instagram", "twitter"],
    views: 987,
    rating: 4.6
  },
  {
    id: "5",
    title: "Pet Recovery Checklist",
    description: "Complete checklist to ensure you don't miss any important steps in your pet recovery efforts. Print and check off as you go.",
    category: "checklist",
    type: "pdf",
    url: "/resources/pet-recovery-checklist.pdf",
    downloadUrl: "/resources/pet-recovery-checklist.pdf",
    featured: false,
    tags: ["checklist", "recovery", "steps", "organization"],
    views: 756,
    rating: 4.5
  },
  {
    id: "6",
    title: "Emergency Contact Directory",
    description: "Comprehensive list of emergency contacts including shelters, veterinarians, animal control, and rescue organizations in your area.",
    category: "contact",
    type: "tool",
    url: "/resources/emergency-contacts",
    featured: false,
    tags: ["contacts", "emergency", "shelters", "veterinarians"],
    views: 634,
    rating: 4.4
  },
  {
    id: "7",
    title: "Prevention Tips and Pet Safety",
    description: "Proactive measures to prevent your pet from getting lost, including home security, ID tags, and behavioral training tips.",
    category: "guide",
    type: "article",
    url: "/resources/prevention-tips",
    featured: false,
    tags: ["prevention", "safety", "training", "security"],
    views: 543,
    rating: 4.3
  },
  {
    id: "8",
    title: "Reunification Success Stories",
    description: "Inspiring stories of successful pet reunifications with lessons learned and strategies that worked.",
    category: "guide",
    type: "article",
    url: "/resources/success-stories",
    featured: false,
    tags: ["success", "stories", "inspiration", "case-studies"],
    views: 432,
    rating: 4.8
  }
]

const categories = [
  { id: "all", label: "All Resources", icon: BookOpen },
  { id: "guide", label: "Guides", icon: FileText },
  { id: "template", label: "Templates", icon: Download },
  { id: "checklist", label: "Checklists", icon: CheckCircle },
  { id: "video", label: "Videos", icon: Video },
  { id: "tool", label: "Tools", icon: Shield },
  { id: "contact", label: "Contacts", icon: Phone }
]

export default function LostPetResourcesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [sortBy, setSortBy] = useState("featured")

  const filteredResources = resources
    .filter(resource => {
      const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      
      const matchesCategory = selectedCategory === "all" || resource.category === selectedCategory
      
      return matchesSearch && matchesCategory
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "featured":
          return (b.featured ? 1 : 0) - (a.featured ? 1 : 0)
        case "popular":
          return b.views - a.views
        case "rating":
          return b.rating - a.rating
        case "alphabetical":
          return a.title.localeCompare(b.title)
        default:
          return 0
      }
    })

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "pdf": return Download
      case "video": return Video
      case "article": return FileText
      case "tool": return Shield
      case "contact": return Phone
      default: return FileText
    }
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      guide: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      template: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      checklist: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
      video: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      tool: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      contact: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
    return colors[category as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <BookOpen className="h-12 w-12 text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              Lost Pet Resources
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Comprehensive resources to help you find your lost pet quickly and effectively. 
            From action plans to poster templates, we have everything you need.
          </p>
        </div>

        {/* Emergency Banner */}
        <div className="max-w-4xl mx-auto mb-8">
          <Card className="border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800">
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-6 w-6 text-red-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-semibold text-red-900 dark:text-red-100 mb-2">
                    Pet Missing Right Now?
                  </h3>
                  <p className="text-red-800 dark:text-red-200 mb-4">
                    Time is critical! Start with our 24-hour action plan and report your missing pet immediately.
                  </p>
                  <div className="flex flex-wrap gap-3">
                    <Button className="bg-red-600 hover:bg-red-700 text-white">
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      Report Lost Pet
                    </Button>
                    <Button variant="outline" className="border-red-600 text-red-600 hover:bg-red-50">
                      <Download className="h-4 w-4 mr-2" />
                      Download Action Plan
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <div className="max-w-6xl mx-auto mb-8">
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search resources..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2 overflow-x-auto pb-2">
              {categories.map((category) => {
                const IconComponent = category.icon
                return (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    onClick={() => setSelectedCategory(category.id)}
                    className="whitespace-nowrap"
                  >
                    <IconComponent className="h-4 w-4 mr-2" />
                    {category.label}
                  </Button>
                )
              })}
            </div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md bg-white dark:bg-gray-800"
            >
              <option value="featured">Featured First</option>
              <option value="popular">Most Popular</option>
              <option value="rating">Highest Rated</option>
              <option value="alphabetical">A-Z</option>
            </select>
          </div>
        </div>

        {/* Featured Resources */}
        {selectedCategory === "all" && (
          <div className="max-w-6xl mx-auto mb-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">Featured Resources</h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {resources.filter(resource => resource.featured).map((resource) => {
                const TypeIcon = getTypeIcon(resource.type)
                
                return (
                  <Card key={resource.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="relative">
                      <div className="h-32 bg-gradient-to-r from-blue-400 to-purple-500"></div>
                      <div className="absolute top-3 left-3">
                        <Badge className={getCategoryColor(resource.category)}>
                          {resource.category}
                        </Badge>
                      </div>
                      <div className="absolute top-3 right-3">
                        <Badge className="bg-yellow-500 text-yellow-900">
                          <Star className="h-3 w-3 mr-1" />
                          Featured
                        </Badge>
                      </div>
                    </div>
                    <CardContent className="p-4">
                      <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">
                        {resource.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 mb-3 text-sm line-clamp-2">
                        {resource.description}
                      </p>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center">
                            <Eye className="h-3 w-3 mr-1" />
                            {resource.views.toLocaleString()}
                          </div>
                          <div className="flex items-center">
                            <Star className="h-3 w-3 mr-1 text-yellow-500" />
                            {resource.rating}
                          </div>
                        </div>
                        <TypeIcon className="h-4 w-4" />
                      </div>

                      <div className="flex gap-2">
                        <Button size="sm" className="flex-1" asChild>
                          <a href={resource.url}>
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </a>
                        </Button>
                        {resource.downloadUrl && (
                          <Button size="sm" variant="outline" asChild>
                            <a href={resource.downloadUrl}>
                              <Download className="h-3 w-3 mr-1" />
                              Download
                            </a>
                          </Button>
                        )}
                        <Button size="sm" variant="outline">
                          <Share2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        )}

        {/* All Resources */}
        <div className="max-w-6xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            {selectedCategory === "all" ? "All Resources" : `${categories.find(c => c.id === selectedCategory)?.label} Resources`}
          </h2>
          
          {filteredResources.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  No resources found
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Try adjusting your search or filter criteria.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredResources.map((resource) => {
                const TypeIcon = getTypeIcon(resource.type)
                
                return (
                  <Card key={resource.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <Badge className={getCategoryColor(resource.category)}>
                          {resource.category}
                        </Badge>
                        <TypeIcon className="h-4 w-4 text-gray-400" />
                      </div>
                      
                      <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">
                        {resource.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 mb-3 text-sm line-clamp-3">
                        {resource.description}
                      </p>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center">
                            <Eye className="h-3 w-3 mr-1" />
                            {resource.views.toLocaleString()}
                          </div>
                          <div className="flex items-center">
                            <Star className="h-3 w-3 mr-1 text-yellow-500" />
                            {resource.rating}
                          </div>
                        </div>
                        {resource.featured && (
                          <Badge className="bg-yellow-500 text-yellow-900 text-xs">
                            Featured
                          </Badge>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button size="sm" className="flex-1" asChild>
                          <a href={resource.url}>
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </a>
                        </Button>
                        {resource.downloadUrl && (
                          <Button size="sm" variant="outline" asChild>
                            <a href={resource.downloadUrl}>
                              <Download className="h-3 w-3 mr-1" />
                              Download
                            </a>
                          </Button>
                        )}
                        <Button size="sm" variant="outline">
                          <Share2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="max-w-4xl mx-auto mt-16">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 text-center mb-8">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="bg-red-100 dark:bg-red-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Report Lost Pet
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Report your missing pet to our database and alert the community
                </p>
                <Button className="w-full bg-red-600 hover:bg-red-700">
                  Report Now
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="bg-green-100 dark:bg-green-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Heart className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Report Found Pet
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Help reunite a found pet with their worried family
                </p>
                <Button className="w-full bg-green-600 hover:bg-green-700">
                  Report Found
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardContent className="pt-6">
                <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Join Search Team
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Volunteer to help search for missing pets in your area
                </p>
                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  Join Team
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
