"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Shield, 
  MapPin, 
  Clock, 
  CheckCircle,
  AlertCircle,
  Phone,
  Calendar,
  CreditCard,
  Heart,
  Search,
  Star,
  Award,
  Zap,
  Users,
  FileText
} from "lucide-react"
import { toast } from "react-hot-toast"

interface MicrochipForm {
  petName: string
  petType: "dog" | "cat" | "other"
  petBreed: string
  petAge: string
  petWeight: string
  ownerName: string
  ownerEmail: string
  ownerPhone: string
  address: string
  city: string
  state: string
  zipCode: string
  emergencyContact: string
  emergencyPhone: string
  veterinarian: string
  vetPhone: string
  appointmentDate: string
  appointmentTime: string
  specialInstructions: string
  newsletter: boolean
}

const benefits = [
  {
    icon: Shield,
    title: "Permanent Identification",
    description: "Unlike collars and tags, microchips can't fall off or be removed"
  },
  {
    icon: Search,
    title: "Quick Recovery",
    description: "Shelters and vets can instantly identify your pet and contact you"
  },
  {
    icon: MapPin,
    title: "Nationwide Database",
    description: "Your pet can be identified anywhere in the country"
  },
  {
    icon: Heart,
    title: "Peace of Mind",
    description: "Know that your pet can always find their way back to you"
  }
]

const process = [
  {
    step: 1,
    title: "Schedule Appointment",
    description: "Book your microchipping appointment online or by phone"
  },
  {
    step: 2,
    title: "Quick Procedure",
    description: "The microchip is inserted between the shoulder blades in seconds"
  },
  {
    step: 3,
    title: "Register Information",
    description: "We register your contact information in the national database"
  },
  {
    step: 4,
    title: "Keep Updated",
    description: "Update your information whenever you move or change numbers"
  }
]

const pricing = [
  {
    title: "Basic Microchip",
    price: 25,
    features: [
      "ISO standard microchip",
      "Database registration",
      "Basic recovery service",
      "1-year registration"
    ]
  },
  {
    title: "Premium Protection",
    price: 45,
    features: [
      "ISO standard microchip",
      "Lifetime database registration",
      "24/7 recovery hotline",
      "Lost pet alerts",
      "Medical records storage"
    ],
    popular: true
  },
  {
    title: "Complete Care",
    price: 65,
    features: [
      "ISO standard microchip",
      "Lifetime registration",
      "24/7 recovery service",
      "Lost pet alerts & posters",
      "Medical records storage",
      "Travel assistance",
      "Reward program"
    ]
  }
]

export default function MicrochippingPage() {
  const [formData, setFormData] = useState<MicrochipForm>({
    petName: "",
    petType: "dog",
    petBreed: "",
    petAge: "",
    petWeight: "",
    ownerName: "",
    ownerEmail: "",
    ownerPhone: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    emergencyContact: "",
    emergencyPhone: "",
    veterinarian: "",
    vetPhone: "",
    appointmentDate: "",
    appointmentTime: "",
    specialInstructions: "",
    newsletter: true
  })

  const [submitting, setSubmitting] = useState(false)
  const [selectedPackage, setSelectedPackage] = useState("Premium Protection")

  const handleInputChange = (field: keyof MicrochipForm, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      const response = await fetch("/api/microchip-appointments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          package: selectedPackage
        }),
      })

      if (response.ok) {
        toast.success("Microchipping appointment scheduled successfully! We'll send you a confirmation email.")
        // Reset form
        setFormData({
          petName: "",
          petType: "dog",
          petBreed: "",
          petAge: "",
          petWeight: "",
          ownerName: "",
          ownerEmail: "",
          ownerPhone: "",
          address: "",
          city: "",
          state: "",
          zipCode: "",
          emergencyContact: "",
          emergencyPhone: "",
          veterinarian: "",
          vetPhone: "",
          appointmentDate: "",
          appointmentTime: "",
          specialInstructions: "",
          newsletter: true
        })
      } else {
        const error = await response.json()
        toast.error(error.error || "Failed to schedule appointment")
      }
    } catch (error) {
      console.error("Error scheduling appointment:", error)
      toast.error("Failed to schedule appointment")
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Shield className="h-12 w-12 text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              Pet Microchipping Services
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Give your pet the best chance of coming home with our professional microchipping services. 
            Quick, safe, and permanent identification that lasts a lifetime.
          </p>
        </div>

        {/* Benefits Section */}
        <div className="max-w-6xl mx-auto mb-16">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 text-center mb-8">
            Why Microchip Your Pet?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {benefits.map((benefit, index) => {
              const IconComponent = benefit.icon
              return (
                <Card key={index} className="text-center">
                  <CardContent className="pt-6">
                    <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="h-8 w-8 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                      {benefit.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm">
                      {benefit.description}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Process Section */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 text-center mb-8">
            How It Works
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {process.map((step, index) => (
              <div key={index} className="text-center">
                <div className="bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 text-lg font-bold">
                  {step.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  {step.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Pricing Section */}
        <div className="max-w-6xl mx-auto mb-16">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 text-center mb-8">
            Choose Your Protection Plan
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {pricing.map((plan, index) => (
              <Card 
                key={index} 
                className={`relative ${plan.popular ? 'border-blue-500 shadow-lg' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-xl">{plan.title}</CardTitle>
                  <div className="text-3xl font-bold text-blue-600">
                    ${plan.price}
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button 
                    className="w-full mt-6"
                    variant={plan.popular ? "default" : "outline"}
                    onClick={() => setSelectedPackage(plan.title)}
                  >
                    {selectedPackage === plan.title ? "Selected" : "Select Plan"}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Appointment Form */}
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">Schedule Your Appointment</CardTitle>
              <CardDescription>
                Book your pet's microchipping appointment. Selected plan: <strong>{selectedPackage}</strong>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Pet Information */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Pet Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="petName">Pet Name *</Label>
                      <Input
                        id="petName"
                        value={formData.petName}
                        onChange={(e) => handleInputChange("petName", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="petType">Pet Type *</Label>
                      <select
                        id="petType"
                        value={formData.petType}
                        onChange={(e) => handleInputChange("petType", e.target.value as any)}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        required
                      >
                        <option value="dog">Dog</option>
                        <option value="cat">Cat</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="petBreed">Breed</Label>
                      <Input
                        id="petBreed"
                        value={formData.petBreed}
                        onChange={(e) => handleInputChange("petBreed", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="petAge">Age</Label>
                      <Input
                        id="petAge"
                        placeholder="e.g., 2 years, 6 months"
                        value={formData.petAge}
                        onChange={(e) => handleInputChange("petAge", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="petWeight">Weight</Label>
                      <Input
                        id="petWeight"
                        placeholder="e.g., 25 lbs"
                        value={formData.petWeight}
                        onChange={(e) => handleInputChange("petWeight", e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* Owner Information */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Owner Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="ownerName">Full Name *</Label>
                      <Input
                        id="ownerName"
                        value={formData.ownerName}
                        onChange={(e) => handleInputChange("ownerName", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="ownerEmail">Email Address *</Label>
                      <Input
                        id="ownerEmail"
                        type="email"
                        value={formData.ownerEmail}
                        onChange={(e) => handleInputChange("ownerEmail", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="ownerPhone">Phone Number *</Label>
                      <Input
                        id="ownerPhone"
                        type="tel"
                        value={formData.ownerPhone}
                        onChange={(e) => handleInputChange("ownerPhone", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="address">Address *</Label>
                      <Input
                        id="address"
                        value={formData.address}
                        onChange={(e) => handleInputChange("address", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="city">City *</Label>
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={(e) => handleInputChange("city", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="state">State *</Label>
                      <Input
                        id="state"
                        value={formData.state}
                        onChange={(e) => handleInputChange("state", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="zipCode">ZIP Code *</Label>
                      <Input
                        id="zipCode"
                        value={formData.zipCode}
                        onChange={(e) => handleInputChange("zipCode", e.target.value)}
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Emergency Contact */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Emergency Contact</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="emergencyContact">Emergency Contact Name</Label>
                      <Input
                        id="emergencyContact"
                        value={formData.emergencyContact}
                        onChange={(e) => handleInputChange("emergencyContact", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="emergencyPhone">Emergency Contact Phone</Label>
                      <Input
                        id="emergencyPhone"
                        type="tel"
                        value={formData.emergencyPhone}
                        onChange={(e) => handleInputChange("emergencyPhone", e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* Veterinarian Information */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Veterinarian Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="veterinarian">Veterinarian Name</Label>
                      <Input
                        id="veterinarian"
                        value={formData.veterinarian}
                        onChange={(e) => handleInputChange("veterinarian", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="vetPhone">Veterinarian Phone</Label>
                      <Input
                        id="vetPhone"
                        type="tel"
                        value={formData.vetPhone}
                        onChange={(e) => handleInputChange("vetPhone", e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* Appointment Scheduling */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Preferred Appointment</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="appointmentDate">Preferred Date *</Label>
                      <Input
                        id="appointmentDate"
                        type="date"
                        value={formData.appointmentDate}
                        onChange={(e) => handleInputChange("appointmentDate", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="appointmentTime">Preferred Time *</Label>
                      <select
                        id="appointmentTime"
                        value={formData.appointmentTime}
                        onChange={(e) => handleInputChange("appointmentTime", e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        required
                      >
                        <option value="">Select a time</option>
                        <option value="9:00 AM">9:00 AM</option>
                        <option value="10:00 AM">10:00 AM</option>
                        <option value="11:00 AM">11:00 AM</option>
                        <option value="1:00 PM">1:00 PM</option>
                        <option value="2:00 PM">2:00 PM</option>
                        <option value="3:00 PM">3:00 PM</option>
                        <option value="4:00 PM">4:00 PM</option>
                        <option value="5:00 PM">5:00 PM</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Special Instructions */}
                <div>
                  <Label htmlFor="specialInstructions">Special Instructions</Label>
                  <Textarea
                    id="specialInstructions"
                    placeholder="Any special instructions or concerns about your pet..."
                    value={formData.specialInstructions}
                    onChange={(e) => handleInputChange("specialInstructions", e.target.value)}
                    rows={3}
                  />
                </div>

                {/* Newsletter Subscription */}
                <div>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.newsletter}
                      onChange={(e) => handleInputChange("newsletter", e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <span>Subscribe to pet care tips and updates</span>
                  </label>
                </div>

                <Button 
                  type="submit" 
                  disabled={submitting}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                  size="lg"
                >
                  {submitting ? "Scheduling..." : `Schedule Appointment - ${selectedPackage}`}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* FAQ Section */}
        <div className="max-w-4xl mx-auto mt-16">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 text-center mb-8">
            Frequently Asked Questions
          </h2>
          <div className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <h3 className="font-semibold mb-2">Does microchipping hurt my pet?</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  The microchipping procedure is quick and causes minimal discomfort, similar to a routine vaccination. 
                  Most pets don't even notice it happening.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <h3 className="font-semibold mb-2">How long does the procedure take?</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  The actual microchip insertion takes just a few seconds. The entire appointment, including paperwork 
                  and registration, typically takes 15-20 minutes.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <h3 className="font-semibold mb-2">Can the microchip move or fall out?</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Microchips are designed to stay in place permanently. They're made of biocompatible materials and 
                  become encapsulated by tissue, preventing movement.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <h3 className="font-semibold mb-2">What if I move or change my contact information?</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  You can update your contact information anytime through the microchip registry database. 
                  It's important to keep this information current for the system to work effectively.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
