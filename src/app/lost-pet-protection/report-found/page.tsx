"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Heart, 
  MapPin, 
  Calendar, 
  Phone, 
  Mail, 
  Upload,
  CheckCircle,
  Clock,
  Camera,
  AlertCircle
} from "lucide-react"
import { toast } from "react-hot-toast"

interface FoundPetForm {
  // Pet Information
  species: string
  breed: string
  size: string
  color: string
  gender: string
  condition: string
  
  // Found Information
  foundDate: string
  foundTime: string
  foundLocation: string
  currentLocation: string
  
  // Finder Information
  finderName: string
  finderPhone: string
  finderEmail: string
  
  // Additional Information
  description: string
  photos: File[]
}

export default function ReportFoundPetPage() {
  const [formData, setFormData] = useState<FoundPetForm>({
    species: "",
    breed: "",
    size: "",
    color: "",
    gender: "",
    condition: "",
    foundDate: "",
    foundTime: "",
    foundLocation: "",
    currentLocation: "",
    finderName: "",
    finderPhone: "",
    finderEmail: "",
    description: "",
    photos: []
  })

  const [submitting, setSubmitting] = useState(false)

  const handleInputChange = (field: keyof FoundPetForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFormData(prev => ({
        ...prev,
        photos: Array.from(e.target.files || [])
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      const submitData = new FormData()
      
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'photos') {
          formData.photos.forEach((file, index) => {
            submitData.append(`photo_${index}`, file)
          })
        } else {
          submitData.append(key, value as string)
        }
      })

      const response = await fetch("/api/found-pets/report", {
        method: "POST",
        body: submitData,
      })

      if (response.ok) {
        toast.success("Found pet report submitted successfully! We'll help find the owner.")
        setFormData({
          species: "",
          breed: "",
          size: "",
          color: "",
          gender: "",
          condition: "",
          foundDate: "",
          foundTime: "",
          foundLocation: "",
          currentLocation: "",
          finderName: "",
          finderPhone: "",
          finderEmail: "",
          description: "",
          photos: []
        })
      } else {
        const error = await response.json()
        toast.error(error.error || "Failed to submit report")
      }
    } catch (error) {
      console.error("Error submitting report:", error)
      toast.error("Failed to submit report")
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Heart className="h-12 w-12 text-green-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              Report a Found Pet
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Thank you for caring! Help us reunite this pet with their worried family by 
            providing as much detail as possible.
          </p>
        </div>

        {/* Safety Notice */}
        <Card className="mb-8 border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20">
          <CardContent className="pt-6">
            <div className="flex items-start">
              <AlertCircle className="h-6 w-6 text-yellow-600 mr-3 mt-1" />
              <div>
                <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                  Safety First
                </h3>
                <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                  <p>• Approach the pet calmly and carefully - they may be scared or injured</p>
                  <p>• If the pet seems aggressive or severely injured, contact animal control</p>
                  <p>• Check for ID tags, collar, or microchip information</p>
                  <p>• For immediate assistance: <strong>(555) 123-PETS</strong></p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <form onSubmit={handleSubmit} className="max-w-4xl mx-auto space-y-8">
          {/* Pet Description */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Camera className="h-5 w-5 mr-2" />
                Describe the Pet
              </CardTitle>
              <CardDescription>
                Help us identify this pet with detailed information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="species">Species *</Label>
                  <select
                    id="species"
                    value={formData.species}
                    onChange={(e) => handleInputChange("species", e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select species</option>
                    <option value="dog">Dog</option>
                    <option value="cat">Cat</option>
                    <option value="bird">Bird</option>
                    <option value="rabbit">Rabbit</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="breed">Breed (if known)</Label>
                  <Input
                    id="breed"
                    value={formData.breed}
                    onChange={(e) => handleInputChange("breed", e.target.value)}
                    placeholder="e.g., Golden Retriever, Mixed"
                  />
                </div>
                <div>
                  <Label htmlFor="size">Size</Label>
                  <select
                    id="size"
                    value={formData.size}
                    onChange={(e) => handleInputChange("size", e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select size</option>
                    <option value="small">Small (under 25 lbs)</option>
                    <option value="medium">Medium (25-60 lbs)</option>
                    <option value="large">Large (60-100 lbs)</option>
                    <option value="extra-large">Extra Large (over 100 lbs)</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="gender">Gender (if known)</Label>
                  <select
                    id="gender"
                    value={formData.gender}
                    onChange={(e) => handleInputChange("gender", e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="unknown">Unknown</option>
                  </select>
                </div>
              </div>

              <div>
                <Label htmlFor="color">Color and Markings *</Label>
                <Input
                  id="color"
                  value={formData.color}
                  onChange={(e) => handleInputChange("color", e.target.value)}
                  placeholder="e.g., Brown and white, Black with tan markings"
                  required
                />
              </div>

              <div>
                <Label htmlFor="condition">Pet's Condition</Label>
                <select
                  id="condition"
                  value={formData.condition}
                  onChange={(e) => handleInputChange("condition", e.target.value)}
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select condition</option>
                  <option value="good">Good - appears healthy</option>
                  <option value="tired">Tired/exhausted</option>
                  <option value="scared">Scared/anxious</option>
                  <option value="injured">Injured - needs medical attention</option>
                  <option value="sick">Appears sick</option>
                </select>
              </div>

              <div>
                <Label htmlFor="description">Detailed Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  placeholder="Describe any distinctive features, behavior, collar/tags, or other identifying information..."
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* Location and Time */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="h-5 w-5 mr-2" />
                Where and When Did You Find This Pet?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="foundDate">Date Found *</Label>
                  <Input
                    id="foundDate"
                    type="date"
                    value={formData.foundDate}
                    onChange={(e) => handleInputChange("foundDate", e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="foundTime">Time Found</Label>
                  <Input
                    id="foundTime"
                    type="time"
                    value={formData.foundTime}
                    onChange={(e) => handleInputChange("foundTime", e.target.value)}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="foundLocation">Where You Found the Pet *</Label>
                <Input
                  id="foundLocation"
                  value={formData.foundLocation}
                  onChange={(e) => handleInputChange("foundLocation", e.target.value)}
                  placeholder="Street address, intersection, or landmark"
                  required
                />
              </div>

              <div>
                <Label htmlFor="currentLocation">Where is the Pet Now? *</Label>
                <Input
                  id="currentLocation"
                  value={formData.currentLocation}
                  onChange={(e) => handleInputChange("currentLocation", e.target.value)}
                  placeholder="Your address, veterinary clinic, animal shelter, etc."
                  required
                />
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Phone className="h-5 w-5 mr-2" />
                Your Contact Information
              </CardTitle>
              <CardDescription>
                How can pet owners reach you?
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="finderName">Your Name *</Label>
                  <Input
                    id="finderName"
                    value={formData.finderName}
                    onChange={(e) => handleInputChange("finderName", e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="finderPhone">Phone Number *</Label>
                  <Input
                    id="finderPhone"
                    type="tel"
                    value={formData.finderPhone}
                    onChange={(e) => handleInputChange("finderPhone", e.target.value)}
                    required
                  />
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="finderEmail">Email Address *</Label>
                  <Input
                    id="finderEmail"
                    type="email"
                    value={formData.finderEmail}
                    onChange={(e) => handleInputChange("finderEmail", e.target.value)}
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Photos */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Upload className="h-5 w-5 mr-2" />
                Photos
              </CardTitle>
              <CardDescription>
                Clear photos help owners identify their pets quickly
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="photos">Pet Photos</Label>
                <Input
                  id="photos"
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileChange}
                  className="mt-1"
                />
                <p className="text-sm text-gray-600 mt-1">
                  Upload clear photos showing the pet's face, body, and any distinctive markings.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Submit */}
          <div className="text-center">
            <Button 
              type="submit" 
              disabled={submitting}
              size="lg"
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {submitting ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Submitting Report...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Submit Found Pet Report
                </>
              )}
            </Button>
          </div>
        </form>

        {/* Next Steps */}
        <Card className="mt-8 max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>What Happens Next?</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Immediate Actions</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• We'll cross-reference with lost pet reports</li>
                  <li>• Your report will be shared with local shelters</li>
                  <li>• We'll post on social media networks</li>
                  <li>• Veterinarians will be notified to check for microchips</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">While You Wait</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Keep the pet safe and comfortable</li>
                  <li>• Take them to a vet to check for microchips</li>
                  <li>• Post on local community boards and social media</li>
                  <li>• Contact us with any updates: (555) 123-PETS</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
