"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  CheckCircle, 
  Circle,
  Heart, 
  Home,
  DollarSign,
  Calendar,
  Users,
  Shield,
  BookOpen,
  AlertTriangle,
  Clock,
  CheckSquare
} from "lucide-react"
import { motion } from "framer-motion"

interface ChecklistItem {
  id: string
  title: string
  description: string
  category: string
  priority: "high" | "medium" | "low"
  timeframe: string
  completed?: boolean
}

const checklistItems: ChecklistItem[] = [
  {
    id: "research",
    title: "Research Pet Types & Breeds",
    description: "Learn about different pets and breeds that match your lifestyle",
    category: "preparation",
    priority: "high",
    timeframe: "Before visiting"
  },
  {
    id: "budget",
    title: "Plan Your Budget",
    description: "Calculate adoption fees, supplies, food, vet bills, and ongoing costs",
    category: "financial",
    priority: "high",
    timeframe: "Before visiting"
  },
  {
    id: "housing",
    title: "Check Housing Requirements",
    description: "Verify pet policies with landlords, HOA, or housing restrictions",
    category: "preparation",
    priority: "high",
    timeframe: "Before visiting"
  },
  {
    id: "supplies",
    title: "Purchase Essential Supplies",
    description: "Get food, bowls, bed, toys, leash, collar, and carrier",
    category: "supplies",
    priority: "medium",
    timeframe: "Before bringing home"
  },
  {
    id: "petproof",
    title: "Pet-Proof Your Home",
    description: "Remove hazards, secure cabinets, and create safe spaces",
    category: "preparation",
    priority: "high",
    timeframe: "Before bringing home"
  },
  {
    id: "vet",
    title: "Find a Veterinarian",
    description: "Research and contact local vets, schedule initial appointment",
    category: "healthcare",
    priority: "high",
    timeframe: "Before bringing home"
  },
  {
    id: "schedule",
    title: "Plan Your Schedule",
    description: "Arrange time off work for the first few days with your new pet",
    category: "preparation",
    priority: "medium",
    timeframe: "Before bringing home"
  },
  {
    id: "family",
    title: "Prepare Family Members",
    description: "Discuss responsibilities and rules with all household members",
    category: "preparation",
    priority: "medium",
    timeframe: "Before bringing home"
  },
  {
    id: "application",
    title: "Complete Adoption Application",
    description: "Fill out all required paperwork and provide references",
    category: "paperwork",
    priority: "high",
    timeframe: "During visit"
  },
  {
    id: "meet",
    title: "Meet & Greet Session",
    description: "Spend quality time with your potential pet",
    category: "interaction",
    priority: "high",
    timeframe: "During visit"
  },
  {
    id: "questions",
    title: "Ask Important Questions",
    description: "Learn about health, behavior, history, and special needs",
    category: "interaction",
    priority: "high",
    timeframe: "During visit"
  },
  {
    id: "transition",
    title: "Plan Transition Period",
    description: "Prepare for adjustment period and potential challenges",
    category: "aftercare",
    priority: "medium",
    timeframe: "After adoption"
  }
]

export default function AdoptionChecklistPage() {
  const [completedItems, setCompletedItems] = useState<string[]>([])
  const [selectedCategory, setSelectedCategory] = useState("all")

  const toggleItem = (itemId: string) => {
    setCompletedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const filteredItems = selectedCategory === "all" 
    ? checklistItems 
    : checklistItems.filter(item => item.category === selectedCategory)

  const categories = Array.from(new Set(checklistItems.map(item => item.category)))
  const completionPercentage = Math.round((completedItems.length / checklistItems.length) * 100)

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800"
      case "medium": return "bg-yellow-100 text-yellow-800"
      case "low": return "bg-green-100 text-green-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Adoption
              <span className="block text-blue-300">Checklist</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-purple-100">
              Your complete guide to preparing for pet adoption
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Progress Card */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckSquare className="w-6 h-6 mr-2 text-blue-600" />
              Your Progress
            </CardTitle>
            <CardDescription>
              Track your adoption preparation progress
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <span className="text-2xl font-bold text-blue-600">{completionPercentage}%</span>
              <span className="text-gray-600">{completedItems.length} of {checklistItems.length} completed</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <motion.div
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${completionPercentage}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </CardContent>
        </Card>

        {/* Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory("all")}
              >
                All Items
              </Button>
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="capitalize"
                >
                  {category}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Checklist Items */}
        <div className="grid gap-4">
          {filteredItems.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card 
                className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                  completedItems.includes(item.id) 
                    ? 'bg-green-50 border-green-200' 
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => toggleItem(item.id)}
              >
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 mt-1">
                      {completedItems.includes(item.id) ? (
                        <CheckCircle className="w-6 h-6 text-green-600" />
                      ) : (
                        <Circle className="w-6 h-6 text-gray-400" />
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className={`text-lg font-semibold ${
                            completedItems.includes(item.id) 
                              ? 'text-green-800 line-through' 
                              : 'text-gray-900'
                          }`}>
                            {item.title}
                          </h3>
                          <p className={`text-sm mt-1 ${
                            completedItems.includes(item.id) 
                              ? 'text-green-600' 
                              : 'text-gray-600'
                          }`}>
                            {item.description}
                          </p>
                        </div>
                        
                        <div className="flex flex-col gap-2 ml-4">
                          <Badge className={getPriorityColor(item.priority)}>
                            {item.priority} priority
                          </Badge>
                          <Badge variant="outline" className="capitalize">
                            {item.category}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="flex items-center mt-3 text-sm text-gray-500">
                        <Clock className="w-4 h-4 mr-1" />
                        {item.timeframe}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Completion Message */}
        {completionPercentage === 100 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="mt-8"
          >
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Congratulations!</AlertTitle>
              <AlertDescription className="text-green-700">
                You've completed all the adoption preparation steps. You're ready to welcome your new pet home!
              </AlertDescription>
            </Alert>
          </motion.div>
        )}

        {/* Important Notes */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2 text-orange-600" />
              Important Reminders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3 text-gray-700">
              <li className="flex items-start">
                <Heart className="w-4 h-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Every pet is unique - be patient during the adjustment period</span>
              </li>
              <li className="flex items-start">
                <DollarSign className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Budget for unexpected veterinary expenses</span>
              </li>
              <li className="flex items-start">
                <Calendar className="w-4 h-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Schedule a vet visit within the first week of adoption</span>
              </li>
              <li className="flex items-start">
                <Users className="w-4 h-4 text-purple-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Involve all family members in the preparation process</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
