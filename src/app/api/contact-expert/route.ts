import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

const contactExpertSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  petType: z.string().min(1, "Pet type is required"),
  expertType: z.string().min(1, "Expert type is required"),
  urgency: z.string().min(1, "Urgency level is required"),
  subject: z.string().min(1, "Subject is required"),
  question: z.string().min(20, "Question must be at least 20 characters"),
  preferredContact: z.string().min(1, "Preferred contact method is required"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the request body
    const validatedData = contactExpertSchema.parse(body)

    // In a real application, you would:
    // 1. Save the expert contact request to the database
    // 2. Send notification emails to the appropriate experts
    // 3. Send confirmation email to the user
    // 4. Create a ticket in your support system

    // For now, we'll simulate the process
    console.log("Expert contact request received:", {
      ...validatedData,
      timestamp: new Date().toISOString(),
    })

    // Simulate different response times based on urgency
    const responseTime = getResponseTime(validatedData.urgency)
    
    // In a real app, you would send emails here
    await simulateEmailNotification(validatedData)

    return NextResponse.json({
      success: true,
      message: "Your question has been sent to our experts successfully!",
      expectedResponse: responseTime,
      ticketId: generateTicketId(),
    })

  } catch (error) {
    console.error("Error processing expert contact request:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Invalid form data", 
          errors: error.errors 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { 
        success: false, 
        message: "Failed to send your question. Please try again." 
      },
      { status: 500 }
    )
  }
}

function getResponseTime(urgency: string): string {
  switch (urgency) {
    case "emergency":
      return "Within 1 hour"
    case "high":
      return "Within 4-6 hours"
    case "medium":
      return "Within 24 hours"
    case "low":
    default:
      return "Within 2-3 business days"
  }
}

function generateTicketId(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substr(2, 5)
  return `EXP-${timestamp}-${random}`.toUpperCase()
}

async function simulateEmailNotification(data: any): Promise<void> {
  // In a real application, you would use a service like:
  // - Resend
  // - SendGrid
  // - AWS SES
  // - Nodemailer with SMTP
  
  console.log("Sending email notifications...")
  
  // Simulate expert notification email
  console.log(`Expert notification sent to ${data.expertType} specialists`)
  
  // Simulate user confirmation email
  console.log(`Confirmation email sent to ${data.email}`)
  
  // Simulate delay
  await new Promise(resolve => setTimeout(resolve, 100))
}
