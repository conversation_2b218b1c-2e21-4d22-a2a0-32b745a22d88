import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { petSchema } from "@/lib/validations"
import { UserRole, PetStatus } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "12")
    const species = searchParams.get("species")
    const breed = searchParams.get("breed")
    const size = searchParams.get("size")
    const age = searchParams.get("age")
    const gender = searchParams.get("gender")
    const status = searchParams.get("status") as PetStatus || PetStatus.AVAILABLE
    const search = searchParams.get("search")
    const goodWithKids = searchParams.get("goodWithKids")
    const goodWithDogs = searchParams.get("goodWithDogs")
    const goodWithCats = searchParams.get("goodWithCats")
    const personality = searchParams.get("personality")
    
    // Build where clause
    const where: any = {
      status
    }
    
    if (species) where.species = { contains: species, mode: "insensitive" }
    if (breed) where.breed = { contains: breed, mode: "insensitive" }
    if (size) where.size = size
    if (gender) where.gender = gender
    if (goodWithKids === "true") where.goodWithKids = true
    if (goodWithDogs === "true") where.goodWithDogs = true
    if (goodWithCats === "true") where.goodWithCats = true

    // Personality filtering
    if (personality) {
      const personalityTraits = personality.split(',').filter(Boolean)
      if (personalityTraits.length > 0) {
        where.personalityTraits = {
          hasSome: personalityTraits
        }
      }
    }
    
    if (age) {
      const ageRanges = {
        "puppy": { gte: 0, lte: 12 },
        "young": { gte: 13, lte: 36 },
        "adult": { gte: 37, lte: 84 },
        "senior": { gte: 85 }
      }
      const range = ageRanges[age as keyof typeof ageRanges]
      if (range) where.age = range
    }
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { breed: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ]
    }
    
    const skip = (page - 1) * limit

    const [pets, total] = await Promise.all([
      prisma.pet.findMany({
        where,
        include: {
          photos: {
            select: {
              id: true,
              url: true,
              isPrimary: true
            }
          },
          organization: {
            select: {
              id: true,
              name: true,
              city: true,
              state: true
            }
          },
          _count: {
            select: {
              applications: true,
              favorites: true,
            }
          }
        },
        orderBy: { arrivalDate: "desc" },
        skip,
        take: limit,
      }),
      prisma.pet.count({ where })
    ])

    if (!pets) {
      return NextResponse.json({ 
        pets: [],
        pagination: {
          page,
          limit,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        }
      })
    }

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      pets,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })
  } catch (error) {
    console.error("Error fetching pets:", error)
    return NextResponse.json(
      { error: "Failed to fetch pets" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    // Check if user has permission to create pets
    if (session.user.role !== UserRole.STAFF && session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }
    
    const body = await request.json()
    
    // Validate input
    const validatedData = petSchema.parse(body)
    
    // Generate slug
    const baseSlug = validatedData.name.toLowerCase().replace(/[^a-z0-9]/g, "-")
    let slug = baseSlug
    let counter = 1
    
    while (await prisma.pet.findUnique({ where: { slug } })) {
      slug = `${baseSlug}-${counter}`
      counter++
    }
    
    // Create pet
    const pet = await prisma.pet.create({
      data: {
        ...validatedData,
        slug,
        searchKeywords: [
          validatedData.name,
          validatedData.species,
          validatedData.breed,
          validatedData.color,
          ...validatedData.personalityTraits,
        ].filter(Boolean) as string[],
      },
      include: {
        photos: true,
        organization: {
          select: {
            name: true,
            city: true,
            state: true,
          }
        }
      }
    })
    
    return NextResponse.json({
      message: "Pet created successfully",
      pet
    }, { status: 201 })
    
  } catch (error) {
    console.error("Error creating pet:", error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to create pet" },
      { status: 500 }
    )
  }
}
