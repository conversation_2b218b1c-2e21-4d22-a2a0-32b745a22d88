import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

const downloadSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  selectedGuides: z.array(z.string()).min(1, "Please select at least one guide"),
  newsletter: z.boolean().optional(),
})

const availableGuides = [
  {
    id: "complete-pet-care",
    title: "Complete Pet Care Guide",
    filename: "complete-pet-care-guide.pdf",
    size: "2.3 MB"
  },
  {
    id: "new-pet-checklist",
    title: "New Pet Owner Checklist",
    filename: "new-pet-owner-checklist.pdf",
    size: "850 KB"
  },
  {
    id: "emergency-handbook",
    title: "Pet Emergency Handbook",
    filename: "pet-emergency-handbook.pdf",
    size: "1.1 MB"
  },
  {
    id: "training-basics",
    title: "Pet Training Fundamentals",
    filename: "pet-training-fundamentals.pdf",
    size: "1.8 MB"
  },
  {
    id: "nutrition-guide",
    title: "Pet Nutrition & Diet Guide",
    filename: "pet-nutrition-diet-guide.pdf",
    size: "1.5 MB"
  },
  {
    id: "senior-pet-care",
    title: "Senior Pet Care Manual",
    filename: "senior-pet-care-manual.pdf",
    size: "1.7 MB"
  }
]

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the request body
    const validatedData = downloadSchema.parse(body)

    // Validate that all selected guides exist
    const invalidGuides = validatedData.selectedGuides.filter(
      guideId => !availableGuides.find(guide => guide.id === guideId)
    )

    if (invalidGuides.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Some selected guides are not available",
          invalidGuides 
        },
        { status: 400 }
      )
    }

    // Get the selected guide details
    const selectedGuideDetails = validatedData.selectedGuides.map(guideId => 
      availableGuides.find(guide => guide.id === guideId)
    ).filter(Boolean)

    // Generate download links
    const downloadLinks = generateDownloadLinks(selectedGuideDetails)

    // In a real application, you would:
    // 1. Save the download request to the database
    // 2. Track download analytics
    // 3. Send email with actual download links
    // 4. Subscribe to newsletter if requested
    // 5. Generate time-limited secure download URLs

    console.log("PDF download request:", {
      ...validatedData,
      selectedGuideDetails,
      downloadLinks,
      timestamp: new Date().toISOString(),
    })

    // Handle newsletter subscription
    if (validatedData.newsletter) {
      await subscribeToNewsletter(validatedData.email, validatedData.name)
    }

    // Send download email
    await sendDownloadEmail(validatedData, selectedGuideDetails, downloadLinks)

    return NextResponse.json({
      success: true,
      message: "Download links have been sent to your email!",
      downloadLinks,
      selectedGuides: selectedGuideDetails,
      newsletterSubscribed: validatedData.newsletter || false,
    })

  } catch (error) {
    console.error("Error processing PDF download request:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Invalid form data", 
          errors: error.errors 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { 
        success: false, 
        message: "Failed to process download request. Please try again." 
      },
      { status: 500 }
    )
  }
}

function generateDownloadLinks(guides: any[]): any[] {
  return guides.map(guide => ({
    id: guide.id,
    title: guide.title,
    downloadUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/download-pdf/${guide.id}`,
    filename: guide.filename,
    size: guide.size,
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
  }))
}

async function subscribeToNewsletter(email: string, name: string): Promise<void> {
  // In a real application, you would integrate with your email service provider
  // Examples: Mailchimp, ConvertKit, SendGrid, etc.
  
  console.log("Newsletter subscription:", { email, name })
  
  // Simulate API call to email service
  await new Promise(resolve => setTimeout(resolve, 100))
}

async function sendDownloadEmail(
  userData: any, 
  guides: any[], 
  downloadLinks: any[]
): Promise<void> {
  console.log("Sending download email...")
  
  const emailContent = {
    to: userData.email,
    subject: "Your Pet Care Guides are Ready for Download!",
    content: {
      greeting: `Hi ${userData.name},`,
      message: "Thank you for downloading our pet care guides! Here are your download links:",
      guides: downloadLinks,
      footer: "These links will expire in 24 hours for security reasons.",
      unsubscribe: "If you no longer wish to receive emails from us, you can unsubscribe here."
    }
  }
  
  console.log("Email content:", emailContent)
  
  // In a real application, you would send the actual email here
  // using services like Resend, SendGrid, AWS SES, etc.
  
  // Simulate email sending delay
  await new Promise(resolve => setTimeout(resolve, 200))
}

// GET endpoint for actual file downloads
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const guideId = searchParams.get('guide')
  const token = searchParams.get('token')

  if (!guideId || !token) {
    return NextResponse.json(
      { error: "Missing guide ID or download token" },
      { status: 400 }
    )
  }

  // In a real application, you would:
  // 1. Validate the download token
  // 2. Check if the token has expired
  // 3. Serve the actual PDF file from storage (S3, local filesystem, etc.)
  // 4. Track the download

  const guide = availableGuides.find(g => g.id === guideId)
  
  if (!guide) {
    return NextResponse.json(
      { error: "Guide not found" },
      { status: 404 }
    )
  }

  // For demo purposes, return a JSON response
  // In production, you would return the actual PDF file
  return NextResponse.json({
    message: `This would download: ${guide.title}`,
    filename: guide.filename,
    size: guide.size,
    note: "In a real application, this would serve the actual PDF file"
  })
}
