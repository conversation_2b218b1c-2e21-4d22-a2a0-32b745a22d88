import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

const consultationSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(1, "Phone number is required"),
  petName: z.string().optional(),
  petType: z.string().min(1, "Pet type is required"),
  consultationType: z.string().min(1, "Consultation type is required"),
  expertType: z.string().min(1, "Expert type is required"),
  date: z.string().min(1, "Date is required"),
  timeSlot: z.string().min(1, "Time slot is required"),
  concerns: z.string().min(10, "Please describe your concerns"),
  previousVet: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the request body
    const validatedData = consultationSchema.parse(body)

    // Check if the time slot is available
    const isAvailable = await checkTimeSlotAvailability(
      validatedData.date, 
      validatedData.timeSlot,
      validatedData.expertType
    )

    if (!isAvailable) {
      return NextResponse.json(
        { 
          success: false, 
          message: "The selected time slot is no longer available. Please choose a different time." 
        },
        { status: 409 }
      )
    }

    // Generate consultation details
    const consultationId = generateConsultationId()
    const meetingDetails = generateMeetingDetails(validatedData.consultationType, consultationId)

    // In a real application, you would:
    // 1. Save the consultation to the database
    // 2. Block the time slot for the expert
    // 3. Send confirmation emails with meeting details
    // 4. Create calendar events
    // 5. Process payment if required

    console.log("Consultation scheduled:", {
      ...validatedData,
      consultationId,
      meetingDetails,
      timestamp: new Date().toISOString(),
    })

    // Simulate email notifications
    await simulateConsultationEmails(validatedData, consultationId, meetingDetails)

    return NextResponse.json({
      success: true,
      message: "Consultation scheduled successfully!",
      consultationId,
      meetingDetails,
      confirmationSent: true,
    })

  } catch (error) {
    console.error("Error scheduling consultation:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Invalid form data", 
          errors: error.errors 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { 
        success: false, 
        message: "Failed to schedule consultation. Please try again." 
      },
      { status: 500 }
    )
  }
}

async function checkTimeSlotAvailability(
  date: string, 
  timeSlot: string, 
  expertType: string
): Promise<boolean> {
  // In a real application, you would check against your database
  // For now, we'll simulate availability (90% chance of being available)
  return Math.random() > 0.1
}

function generateConsultationId(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substr(2, 5)
  return `CONS-${timestamp}-${random}`.toUpperCase()
}

function generateMeetingDetails(consultationType: string, consultationId: string) {
  switch (consultationType) {
    case "video":
      return {
        type: "Video Consultation",
        platform: "Zoom",
        meetingLink: `https://zoom.us/j/${Math.random().toString().substr(2, 10)}`,
        meetingId: Math.random().toString().substr(2, 10),
        password: Math.random().toString(36).substr(2, 6),
        instructions: "Join the meeting 5 minutes early. Ensure you have good lighting and your pet is comfortable."
      }
    case "phone":
      return {
        type: "Phone Consultation",
        phoneNumber: "+****************",
        extension: Math.floor(Math.random() * 9999).toString(),
        instructions: "We will call you at the scheduled time. Please ensure you're in a quiet environment."
      }
    case "in-person":
      return {
        type: "In-Person Visit",
        address: "123 Pet Care Lane, Loveville, CA 90210",
        parkingInfo: "Free parking available in front of the building",
        instructions: "Please arrive 10 minutes early and bring your pet's medical records if available."
      }
    default:
      return {
        type: "Consultation",
        instructions: "You will receive detailed instructions via email."
      }
  }
}

async function simulateConsultationEmails(
  data: any, 
  consultationId: string, 
  meetingDetails: any
): Promise<void> {
  console.log("Sending consultation confirmation emails...")
  
  // Simulate user confirmation email
  console.log(`Confirmation email sent to ${data.email}`)
  console.log("Email content:", {
    subject: `Consultation Confirmed - ${consultationId}`,
    consultationDetails: {
      date: data.date,
      time: data.timeSlot,
      type: data.consultationType,
      expert: data.expertType,
      meetingDetails
    }
  })
  
  // Simulate expert notification
  console.log(`Expert notification sent for ${data.expertType} consultation`)
  
  // Simulate calendar invites
  console.log("Calendar invite created and sent")
  
  // Simulate delay
  await new Promise(resolve => setTimeout(resolve, 100))
}
