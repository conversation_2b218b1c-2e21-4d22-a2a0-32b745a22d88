import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { User<PERSON><PERSON>, ApplicationStatus, PetSize, PetGender } from "@prisma/client"

interface Photo {
  id: string
  url: string
  isPrimary: boolean
}

interface Organization {
  id: string
  name: string
  city: string
  state: string
  phone: string
  email: string
}

interface Pet {
  id: string
  name: string
  species: string
  breed: string | null
  age: number | null
  gender: PetGender
  size: PetSize
  color: string | null
  description: string | null
  photos: Photo[]
  organization: Organization
}

interface User {
  id: string
  name: string | null
  email: string
}

interface Appointment {
  id: string
  type: string
  scheduledDate: Date
  status: string
  notes: string | null
}

interface Application {
  id: string
  status: ApplicationStatus
  submittedAt: Date | null
  reviewedAt: Date | null
  approvedAt: Date | null
  staffNotes: string | null
  rejectionReason: string | null
  userId: string
  pet: Pet
  user: User
  appointments: Appointment[]
  applicantName: string | null
  applicantEmail: string | null
  applicantPhone: string | null
  housingType: string | null
  ownOrRent: string | null
  landlordContact: string | null
  hasYard: boolean | null
  yardFenced: boolean | null
  petExperience: string | null
  otherPetsDetails: string | null
  currentVeterinarian: string | null
  workSchedule: string | null
  exercisePlans: string | null
  createdAt: Date
  updatedAt: Date
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const applicationId = params.id

    // First check if the application exists
    const application = await prisma.application.findUnique({
      where: { id: applicationId },
      include: {
        pet: {
          include: {
            photos: {
              orderBy: [
                { isPrimary: 'desc' },
                { createdAt: 'asc' }
              ]
            },
            organization: true
          }
        },
        appointments: {
          orderBy: {
            scheduledDate: 'asc'
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    }) as Application | null

    if (!application) {
      return NextResponse.json(
        { error: "Application not found" },
        { status: 404 }
      )
    }

    // Check if user has permission to view this application
    const userRole = session.user.role as UserRole
    if (userRole !== UserRole.STAFF && userRole !== UserRole.ADMIN) {
      if (application.userId !== session.user.id) {
        return NextResponse.json(
          { error: "You don't have permission to view this application" },
          { status: 403 }
        )
      }
    }

    // Create timeline of status changes
    const timeline = [
      {
        id: `${application.id}-submitted`,
        status: 'SUBMITTED',
        timestamp: application.submittedAt?.toISOString() || application.createdAt.toISOString(),
        notes: 'Application submitted successfully'
      }
    ]

    if (application.reviewedAt) {
      timeline.push({
        id: `${application.id}-reviewed`,
        status: 'UNDER_REVIEW',
        timestamp: application.reviewedAt.toISOString(),
        notes: 'Application is being reviewed by our team'
      })
    }

    if (application.approvedAt) {
      timeline.push({
        id: `${application.id}-approved`,
        status: 'APPROVED',
        timestamp: application.approvedAt.toISOString(),
        notes: 'Application approved! Please schedule pickup.'
      })
    }

    timeline.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())

    const formattedApplication = {
      id: application.id,
      status: application.status,
      submittedAt: application.submittedAt?.toISOString() || application.createdAt.toISOString(),
      reviewedAt: application.reviewedAt?.toISOString(),
      approvedAt: application.approvedAt?.toISOString(),
      staffNotes: application.staffNotes,
      rejectionReason: application.rejectionReason,
      
      // Personal Information
      firstName: application.applicantName?.split(' ')[0] || application.user.name?.split(' ')[0] || '',
      lastName: application.applicantName?.split(' ').slice(1).join(' ') || application.user.name?.split(' ').slice(1).join(' ') || '',
      email: application.applicantEmail || application.user.email,
      phone: application.applicantPhone || '',
      
      // Housing Information
      housingType: application.housingType || '',
      ownRent: application.ownOrRent || '',
      landlordContact: application.landlordContact || '',
      hasYard: application.hasYard || false,
      yardFenced: application.yardFenced || false,
      
      // Experience and Lifestyle
      petExperience: application.petExperience || '',
      currentPets: application.otherPetsDetails || '',
      veterinarianInfo: application.currentVeterinarian || '',
      
      pet: {
        id: application.pet.id,
        name: application.pet.name,
        species: application.pet.species,
        breed: application.pet.breed || 'Mixed',
        age: application.pet.age || 0,
        gender: application.pet.gender,
        size: application.pet.size,
        color: application.pet.color || 'Unknown',
        description: application.pet.description || '',
        photos: application.pet.photos,
        organization: application.pet.organization
      },
      
      appointments: application.appointments.map((apt: Appointment) => ({
        id: apt.id,
        type: apt.type,
        scheduledDate: apt.scheduledDate.toISOString(),
        status: apt.status,
        notes: apt.notes
      })),
      
      timeline
    }

    return NextResponse.json({
      application: formattedApplication
    })

  } catch (error) {
    console.error("Error fetching application details:", error)
    return NextResponse.json(
      { error: "Failed to fetch application details" },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Only staff and admin can update applications
    if (![UserRole.STAFF, UserRole.ADMIN].includes(session.user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    const applicationId = params.id
    const body = await request.json()

    const updatedApplication = await prisma.application.update({
      where: { id: applicationId },
      data: {
        status: body.status,
        staffNotes: body.staffNotes,
        rejectionReason: body.rejectionReason,
        reviewedAt: body.status === 'UNDER_REVIEW' ? new Date() : undefined,
        approvedAt: body.status === 'APPROVED' ? new Date() : undefined,
      },
      include: {
        pet: {
          include: {
            photos: true,
            organization: true
          }
        },
        appointments: true
      }
    })

    return NextResponse.json({
      application: updatedApplication
    })

  } catch (error) {
    console.error("Error updating application:", error)
    return NextResponse.json(
      { error: "Failed to update application" },
      { status: 500 }
    )
  }
}