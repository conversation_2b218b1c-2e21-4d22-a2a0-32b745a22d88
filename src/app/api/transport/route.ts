import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { authOptions } from "@/lib/auth"
import { UserRole, TransportStatus, TransportType } from "@prisma/client"
import { z } from "zod"

const transportRequestSchema = z.object({
  type: z.nativeEnum(TransportType),
  petId: z.string().min(1, "Pet ID is required"),
  pickupLocation: z.string().min(1, "Pickup location is required"),
  dropoffLocation: z.string().min(1, "Dropoff location is required"),
  scheduledDate: z.string().min(1, "Scheduled date is required"),
  urgency: z.enum(["LOW", "MEDIUM", "HIGH", "EMERGENCY"]).default("MEDIUM"),
  specialInstructions: z.string().optional(),
  contactPhone: z.string().min(1, "Contact phone is required"),
  estimatedDistance: z.number().optional(),
  estimatedDuration: z.number().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "20")
    const status = searchParams.get("status")
    const type = searchParams.get("type")
    const urgency = searchParams.get("urgency")
    const skip = (page - 1) * limit

    let where: any = {}

    // Regular users can only see their own transport requests
    if (![UserRole.STAFF, UserRole.ADMIN, UserRole.VOLUNTEER].includes(session.user.role)) {
      where.requestedById = session.user.id
    }

    if (status) where.status = status as TransportStatus
    if (type) where.type = type as TransportType
    if (urgency) where.urgency = urgency

    const [transports, total] = await Promise.all([
      prisma.transport.findMany({
        where,
        include: {
          pet: {
            select: {
              name: true,
              species: true,
              breed: true,
              size: true,
              specialNeeds: true,
            }
          },
          requestedBy: {
            select: {
              name: true,
              email: true,
              phone: true,
            }
          },
          assignedVolunteer: {
            select: {
              name: true,
              email: true,
              phone: true,
            }
          }
        },
        orderBy: [
          { urgency: "desc" },
          { scheduledDate: "asc" }
        ],
        skip,
        take: limit,
      }),
      prisma.transport.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      transports,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    })

  } catch (error) {
    console.error("Error fetching transport requests:", error)
    return NextResponse.json(
      { error: "Failed to fetch transport requests" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = transportRequestSchema.parse(body)

    // Check if the scheduled time is in the future
    const scheduledDate = new Date(validatedData.scheduledDate)
    if (scheduledDate <= new Date()) {
      return NextResponse.json(
        { error: "Transport must be scheduled for a future date" },
        { status: 400 }
      )
    }

    // Validate that the pet exists
    const pet = await prisma.pet.findUnique({
      where: { id: validatedData.petId },
      select: { id: true, name: true, specialNeeds: true }
    })

    if (!pet) {
      return NextResponse.json(
        { error: "Pet not found" },
        { status: 404 }
      )
    }

    // Calculate estimated distance and duration (placeholder - would integrate with mapping service)
    const estimatedDistance = validatedData.estimatedDistance || calculateDistance(
      validatedData.pickupLocation,
      validatedData.dropoffLocation
    )
    const estimatedDuration = validatedData.estimatedDuration || Math.ceil(estimatedDistance * 2) // 2 minutes per mile estimate

    const transport = await prisma.transport.create({
      data: {
        ...validatedData,
        scheduledDate: scheduledDate,
        estimatedDistance,
        estimatedDuration,
        requestedById: session.user.id,
        status: TransportStatus.REQUESTED,
      },
      include: {
        pet: {
          select: {
            name: true,
            species: true,
            breed: true,
            size: true,
            specialNeeds: true,
          }
        },
        requestedBy: {
          select: {
            name: true,
            email: true,
            phone: true,
          }
        }
      }
    })

    // Auto-assign to available volunteers if urgent
    if (validatedData.urgency === "EMERGENCY") {
      await tryAutoAssignVolunteer(transport.id, scheduledDate)
    }

    return NextResponse.json({
      message: "Transport request created successfully",
      transport
    }, { status: 201 })

  } catch (error) {
    console.error("Error creating transport request:", error)
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Failed to create transport request" },
      { status: 500 }
    )
  }

}

// Helper function to calculate distance (placeholder)
function calculateDistance(pickup: string, dropoff: string): number {
  // In a real implementation, this would use a mapping service like Google Maps API
  // For now, return a random distance between 5-50 miles
  return Math.floor(Math.random() * 45) + 5
}

// Helper function to auto-assign volunteers for emergency transports
async function tryAutoAssignVolunteer(transportId: string, scheduledDate: Date): Promise<void> {
    try {
      // Find available volunteers who have transport in their preferred activities
      const availableVolunteers = await prisma.volunteerProfile.findMany({
        where: {
          status: "ACTIVE",
          preferredActivities: {
            has: "Transport"
          },
          // Check if they don't have conflicting transports
          user: {
            assignedTransports: {
              none: {
                scheduledDate: {
                  gte: new Date(scheduledDate.getTime() - 2 * 60 * 60 * 1000), // 2 hours before
                  lte: new Date(scheduledDate.getTime() + 2 * 60 * 60 * 1000), // 2 hours after
                },
                status: {
                  in: ["ASSIGNED", "IN_PROGRESS"]
                }
              }
            }
          }
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            }
          }
        },
        take: 1, // Get the first available volunteer
      })

      if (availableVolunteers.length > 0) {
        await prisma.transport.update({
          where: { id: transportId },
          data: {
            assignedVolunteerId: availableVolunteers[0].user.id,
            status: TransportStatus.ASSIGNED,
            assignedAt: new Date(),
          }
        })

        // TODO: Send notification to volunteer
        console.log(`Auto-assigned emergency transport to volunteer: ${availableVolunteers[0].user.name}`)
      }
    } catch (error) {
      console.error("Error auto-assigning volunteer:", error)
      // Don't throw error - transport request should still be created
    }
  }
