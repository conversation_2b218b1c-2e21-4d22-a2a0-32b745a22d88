"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Truck, Plus, RefreshCw } from "lucide-react"
import toast from "react-hot-toast"
import Link from "next/link"

interface TransportData {
  trackingNumber: string
  petName: string
  status: string
  updatesCount: number
}

export default function TrackingTestPage() {
  const [transports, setTransports] = useState<TransportData[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)

  const fetchTransports = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/tracking/test")
      
      if (!response.ok) {
        throw new Error("Failed to fetch transport data")
      }

      const data = await response.json()
      setTransports(data.transports || [])
    } catch (error) {
      console.error("Error fetching transports:", error)
      toast.error("Failed to fetch transport data")
    } finally {
      setLoading(false)
    }
  }

  const createSampleData = async () => {
    try {
      setCreating(true)
      const response = await fetch("/api/tracking/test", {
        method: "POST"
      })
      
      if (!response.ok) {
        throw new Error("Failed to create sample data")
      }

      const data = await response.json()
      toast.success(data.message)
      fetchTransports() // Refresh the list
    } catch (error) {
      console.error("Error creating sample data:", error)
      toast.error("Failed to create sample data")
    } finally {
      setCreating(false)
    }
  }

  useEffect(() => {
    fetchTransports()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "DELIVERED":
        return "bg-green-100 text-green-800"
      case "IN_PROGRESS":
      case "PICKED_UP":
        return "bg-blue-100 text-blue-800"
      case "REQUESTED":
        return "bg-yellow-100 text-yellow-800"
      case "CANCELLED":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Pet Tracking Test Page
          </h1>
          <p className="text-gray-600 mb-6">
            This page helps you test the pet tracking functionality. You can create sample tracking data and test the tracking page.
          </p>
          <div className="flex justify-center space-x-4">
            <Button onClick={createSampleData} disabled={creating}>
              {creating ? (
                <span>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </span>
              ) : (
                <span>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Sample Data
                </span>
              )}
            </Button>
            <Button variant="outline" onClick={fetchTransports} disabled={loading}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Link href="/track">
              <Button variant="outline">
                <Truck className="h-4 w-4 mr-2" />
                Go to Tracking Page
              </Button>
            </Link>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Available Tracking Numbers</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
                <p className="text-gray-500">Loading transport data...</p>
              </div>
            ) : transports.length === 0 ? (
              <div className="text-center py-8">
                <Truck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">No transport data found</p>
                <p className="text-sm text-gray-400 mb-4">
                  Click "Create Sample Data" to generate test tracking records
                </p>
                <Button onClick={createSampleData} disabled={creating}>
                  {creating ? (
                    <span>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </span>
                  ) : (
                    <span>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Sample Data
                    </span>
                  )}
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-sm text-gray-600 mb-4">
                  Copy any tracking number below and paste it into the tracking page to test the functionality.
                </p>
                {transports.map((transport) => (
                  <div
                    key={transport.trackingNumber}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-mono font-medium text-lg">
                          {transport.trackingNumber}
                        </span>
                        <Badge className={getStatusColor(transport.status)}>
                          {transport.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600">
                        Pet: {transport.petName} • {transport.updatesCount} updates
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(transport.trackingNumber)
                          toast.success("Tracking number copied!")
                        }}
                      >
                        Copy
                      </Button>
                      <Link href={`/track?tracking=${transport.trackingNumber}`}>
                        <Button size="sm">
                          Track
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <div className="mt-8 p-6 bg-blue-50 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">
            How to Test Tracking
          </h3>
          <ol className="list-decimal list-inside space-y-2 text-blue-800">
            <li>Click "Create Sample Data" to generate test tracking records</li>
            <li>Copy a tracking number from the list above</li>
            <li>Go to the <Link href="/track" className="underline">Tracking Page</Link></li>
            <li>Paste the tracking number and click "Track Pet"</li>
            <li>View the real-time tracking information</li>
          </ol>
          <p className="text-sm text-blue-700 mt-4">
            <strong>Note:</strong> This test page is for development purposes only and should be removed in production.
          </p>
        </div>
      </div>
    </div>
  )
}
