"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Shield,
  Check,
  X,
  Star,
  Search,
  Phone,
  MapPin,
  Clock,
  Users,
  Award,
  Heart,
  AlertTriangle,
  CreditCard,
  Zap
} from "lucide-react"
import { motion } from "framer-motion"

interface MembershipPlan {
  id: string
  name: string
  price: {
    monthly: string
    annual: string
    savings?: string
  }
  features: {
    name: string
    included: boolean
    description?: string
  }[]
  benefits: string[]
  popular?: boolean
  recommended?: boolean
  color: string
}

const membershipPlans: MembershipPlan[] = [
  {
    id: "basic",
    name: "Basic Protection",
    price: { monthly: "$9.99", annual: "$99.99", savings: "Save $20" },
    features: [
      { name: "Lost Pet Alert Network", included: true, description: "Instant alerts to local shelters and vets" },
      { name: "24/7 Lost Pet Hotline", included: true },
      { name: "Digital Pet Profile", included: true },
      { name: "Basic Search Assistance", included: true },
      { name: "Reunion Guarantee", included: false },
      { name: "Professional Search Team", included: false },
      { name: "Reward Posting Service", included: false },
      { name: "Emergency Transport", included: false }
    ],
    benefits: [
      "Immediate alert system",
      "Basic recovery support",
      "Digital pet identification"
    ],
    color: "blue"
  },
  {
    id: "premium",
    name: "Premium Protection",
    price: { monthly: "$19.99", annual: "$199.99", savings: "Save $40" },
    features: [
      { name: "Lost Pet Alert Network", included: true, description: "Enhanced network coverage" },
      { name: "24/7 Lost Pet Hotline", included: true },
      { name: "Digital Pet Profile", included: true },
      { name: "Basic Search Assistance", included: true },
      { name: "Reunion Guarantee", included: true, description: "$500 guarantee" },
      { name: "Professional Search Team", included: true },
      { name: "Reward Posting Service", included: true },
      { name: "Emergency Transport", included: false }
    ],
    benefits: [
      "Professional search assistance",
      "Reward posting and management",
      "$500 reunion guarantee",
      "Priority support"
    ],
    popular: true,
    color: "green"
  },
  {
    id: "platinum",
    name: "Platinum Protection",
    price: { monthly: "$39.99", annual: "$399.99", savings: "Save $80" },
    features: [
      { name: "Lost Pet Alert Network", included: true, description: "Maximum network coverage" },
      { name: "24/7 Lost Pet Hotline", included: true },
      { name: "Digital Pet Profile", included: true },
      { name: "Basic Search Assistance", included: true },
      { name: "Reunion Guarantee", included: true, description: "$2,000 guarantee" },
      { name: "Professional Search Team", included: true },
      { name: "Reward Posting Service", included: true },
      { name: "Emergency Transport", included: true }
    ],
    benefits: [
      "Complete search and recovery service",
      "Emergency pet transport",
      "$2,000 reunion guarantee",
      "Dedicated case manager",
      "Unlimited support"
    ],
    recommended: true,
    color: "purple"
  }
]

export default function LostPetMembershipsPage() {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">("annual")

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-orange-600 to-red-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Lost Pet Protection
              <span className="block text-orange-300">Memberships</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-red-100">
              Comprehensive protection plans to help bring your pet home safely
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Statistics */}
        <div className="grid md:grid-cols-4 gap-6 mb-12">
          {[
            { icon: Search, title: "Pets Recovered", value: "15,847", description: "Successfully reunited with families" },
            { icon: Clock, title: "Average Recovery", value: "2.3 days", description: "Time to successful reunion" },
            { icon: Users, title: "Network Size", value: "50,000+", description: "Shelters, vets, and volunteers" },
            { icon: Award, title: "Success Rate", value: "94%", description: "Pets found and returned home" }
          ].map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <stat.icon className="w-6 h-6 text-orange-600" />
                  </div>
                  <div className="text-3xl font-bold text-orange-600 mb-1">{stat.value}</div>
                  <div className="font-semibold mb-1">{stat.title}</div>
                  <div className="text-sm text-gray-600">{stat.description}</div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Billing Toggle */}
        <div className="flex justify-center mb-8">
          <div className="bg-gray-100 p-1 rounded-lg">
            <Button
              variant={billingCycle === "monthly" ? "default" : "ghost"}
              size="sm"
              onClick={() => setBillingCycle("monthly")}
            >
              Monthly
            </Button>
            <Button
              variant={billingCycle === "annual" ? "default" : "ghost"}
              size="sm"
              onClick={() => setBillingCycle("annual")}
            >
              Annual
              <Badge className="ml-2 bg-green-500 text-white">Save up to $80</Badge>
            </Button>
          </div>
        </div>

        {/* Membership Plans */}
        <div className="grid lg:grid-cols-3 gap-8 mb-12">
          {membershipPlans.map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className={`h-full relative ${
                plan.recommended ? 'ring-2 ring-purple-500 scale-105' : ''
              } ${plan.popular ? 'ring-2 ring-green-500' : ''}`}>
                {plan.recommended && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-purple-500 text-white">
                      <Award className="w-3 h-3 mr-1" />
                      Recommended
                    </Badge>
                  </div>
                )}
                {plan.popular && (
                  <div className="absolute -top-3 right-4">
                    <Badge className="bg-green-500 text-white">
                      <Star className="w-3 h-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
                
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <div className="text-4xl font-bold text-gray-900">
                    {plan.price[billingCycle]}
                    <span className="text-lg font-normal text-gray-500">
                      /{billingCycle === "monthly" ? "month" : "year"}
                    </span>
                  </div>
                  {billingCycle === "annual" && plan.price.savings && (
                    <Badge className="bg-green-100 text-green-800">
                      {plan.price.savings}
                    </Badge>
                  )}
                </CardHeader>
                
                <CardContent className="space-y-6">
                  {/* Features */}
                  <div>
                    <h4 className="font-semibold mb-3">Features Included</h4>
                    <div className="space-y-2">
                      {plan.features.map((feature) => (
                        <div key={feature.name} className="flex items-start">
                          {feature.included ? (
                            <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                          ) : (
                            <X className="w-5 h-5 text-gray-300 mr-3 mt-0.5 flex-shrink-0" />
                          )}
                          <div>
                            <span className={feature.included ? "text-gray-900" : "text-gray-400"}>
                              {feature.name}
                            </span>
                            {feature.description && (
                              <div className="text-xs text-gray-500">{feature.description}</div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Benefits */}
                  <div>
                    <h4 className="font-semibold mb-3">Key Benefits</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {plan.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-start">
                          <Zap className="w-3 h-3 text-orange-500 mr-2 mt-1 flex-shrink-0" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <Button 
                    className={`w-full ${
                      plan.recommended 
                        ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800' 
                        : plan.popular
                        ? 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800'
                        : 'bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700'
                    }`}
                    onClick={() => setSelectedPlan(plan.id)}
                  >
                    Choose {plan.name}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* How It Works */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-center">How Lost Pet Protection Works</CardTitle>
            <CardDescription className="text-center">
              Our comprehensive system activates immediately when your pet goes missing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-4 gap-6">
              {[
                {
                  step: "1",
                  title: "Report Missing Pet",
                  description: "Call our 24/7 hotline or use our mobile app to report your missing pet",
                  icon: Phone
                },
                {
                  step: "2",
                  title: "Alert Network Activated",
                  description: "Instant alerts sent to local shelters, vets, and volunteer network",
                  icon: AlertTriangle
                },
                {
                  step: "3",
                  title: "Search Assistance",
                  description: "Professional search team deployed based on your membership level",
                  icon: Search
                },
                {
                  step: "4",
                  title: "Safe Return Home",
                  description: "Coordinated efforts to safely return your pet home",
                  icon: Heart
                }
              ].map((step, index) => (
                <div key={step.step} className="text-center">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <step.icon className="w-8 h-8 text-orange-600" />
                  </div>
                  <div className="text-lg font-semibold mb-2">Step {step.step}</div>
                  <div className="font-medium mb-2">{step.title}</div>
                  <div className="text-sm text-gray-600">{step.description}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* FAQ */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Frequently Asked Questions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold mb-2">What happens if my pet is never found?</h4>
                <p className="text-gray-600 text-sm">
                  Our Premium and Platinum plans include reunion guarantees. If we're unable to reunite you with your pet 
                  within 30 days, you'll receive the guaranteed amount to help with replacement costs.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">How quickly does the alert system activate?</h4>
                <p className="text-gray-600 text-sm">
                  Alerts are sent within minutes of reporting your pet missing. Our network includes over 50,000 
                  shelters, veterinarians, and volunteers who receive immediate notifications.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Can I upgrade my membership at any time?</h4>
                <p className="text-gray-600 text-sm">
                  Yes, you can upgrade your membership at any time. The enhanced features will be available immediately, 
                  and you'll only pay the prorated difference.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Is there a waiting period for coverage?</h4>
                <p className="text-gray-600 text-sm">
                  No waiting period! Your membership benefits are active immediately upon enrollment, 
                  giving you peace of mind from day one.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Emergency Contact */}
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertTitle className="text-red-800">Pet Missing Right Now?</AlertTitle>
          <AlertDescription className="text-red-700">
            <div className="mt-2">
              <p className="mb-3">Don't wait! Call our emergency hotline immediately:</p>
              <div className="flex items-center gap-4">
                <Button className="bg-red-600 hover:bg-red-700">
                  <Phone className="w-4 h-4 mr-2" />
                  Call 1-800-PET-HELP
                </Button>
                <span className="text-sm">Available 24/7, even without membership</span>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
