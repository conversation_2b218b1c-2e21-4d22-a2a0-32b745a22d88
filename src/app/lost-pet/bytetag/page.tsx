"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Smartphone,
  Wifi,
  Shield,
  Battery,
  MapPin,
  Clock,
  Heart,
  Star,
  CheckCircle,
  Zap,
  Users,
  Award,
  ShoppingCart,
  Info,
  Phone
} from "lucide-react"
import { motion } from "framer-motion"

interface ByteTagProduct {
  id: string
  name: string
  price: string
  originalPrice?: string
  features: string[]
  batteryLife: string
  range: string
  waterproof: boolean
  colors: string[]
  inStock: boolean
  rating: number
  reviews: number
  popular?: boolean
}

const byteTagProducts: ByteTagProduct[] = [
  {
    id: "bytetag-pro",
    name: "ByteTag Pro",
    price: "$89.99",
    originalPrice: "$109.99",
    features: [
      "Real-time GPS tracking",
      "Activity monitoring",
      "Safe zone alerts",
      "Health insights",
      "2-year warranty"
    ],
    batteryLife: "Up to 7 days",
    range: "Unlimited (cellular)",
    waterproof: true,
    colors: ["Black", "Blue", "Pink", "Green"],
    inStock: true,
    rating: 4.8,
    reviews: 2847,
    popular: true
  },
  {
    id: "bytetag-lite",
    name: "ByteTag Lite",
    price: "$49.99",
    features: [
      "Bluetooth tracking",
      "Community find network",
      "Last known location",
      "1-year warranty"
    ],
    batteryLife: "Up to 6 months",
    range: "200 feet (Bluetooth)",
    waterproof: true,
    colors: ["Black", "White", "Red"],
    inStock: true,
    rating: 4.5,
    reviews: 1523
  },
  {
    id: "bytetag-premium",
    name: "ByteTag Premium",
    price: "$129.99",
    features: [
      "Advanced GPS + cellular",
      "AI-powered insights",
      "Vet integration",
      "Emergency alerts",
      "3-year warranty",
      "Premium support"
    ],
    batteryLife: "Up to 10 days",
    range: "Unlimited (global)",
    waterproof: true,
    colors: ["Titanium", "Rose Gold", "Space Gray"],
    inStock: true,
    rating: 4.9,
    reviews: 892
  }
]

export default function ByteTagPage() {
  const [selectedProduct, setSelectedProduct] = useState<ByteTagProduct | null>(null)
  const [selectedColor, setSelectedColor] = useState<string>("")

  const renderStars = (rating: number) => (
    <div className="flex items-center">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`w-4 h-4 ${
            star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
          }`}
        />
      ))}
      <span className="ml-2 text-sm text-gray-600">{rating}</span>
    </div>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              ByteTag
              <span className="block text-blue-300">Smart Pet Tracking</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-purple-100">
              Never lose your pet again with advanced GPS tracking technology
            </p>
            <div className="flex justify-center gap-4">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                Shop Now
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                Learn More
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Key Features */}
        <div className="grid md:grid-cols-4 gap-6 mb-12">
          {[
            { icon: MapPin, title: "Real-Time Tracking", description: "Know exactly where your pet is, anytime" },
            { icon: Shield, title: "Safe Zone Alerts", description: "Get notified when your pet leaves safe areas" },
            { icon: Battery, title: "Long Battery Life", description: "Up to 10 days on a single charge" },
            { icon: Smartphone, title: "Mobile App", description: "Easy-to-use app for iOS and Android" }
          ].map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="text-center h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold mb-2">{feature.title}</h3>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Product Showcase */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-center mb-8">Choose Your ByteTag</h2>
          <div className="grid lg:grid-cols-3 gap-8">
            {byteTagProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card className={`h-full relative ${product.popular ? 'ring-2 ring-blue-500' : ''}`}>
                  {product.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-blue-500 text-white">
                        <Star className="w-3 h-3 mr-1" />
                        Most Popular
                      </Badge>
                    </div>
                  )}
                  
                  <CardHeader className="text-center">
                    <CardTitle className="text-2xl">{product.name}</CardTitle>
                    <div className="flex items-center justify-center gap-2">
                      <span className="text-3xl font-bold text-blue-600">{product.price}</span>
                      {product.originalPrice && (
                        <span className="text-lg text-gray-500 line-through">{product.originalPrice}</span>
                      )}
                    </div>
                    {renderStars(product.rating)}
                    <div className="text-sm text-gray-600">({product.reviews} reviews)</div>
                  </CardHeader>
                  
                  <CardContent className="space-y-6">
                    {/* Key Specs */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Battery Life:</span>
                        <div className="text-gray-600">{product.batteryLife}</div>
                      </div>
                      <div>
                        <span className="font-medium">Range:</span>
                        <div className="text-gray-600">{product.range}</div>
                      </div>
                      <div>
                        <span className="font-medium">Waterproof:</span>
                        <div className="text-gray-600">{product.waterproof ? "Yes" : "No"}</div>
                      </div>
                      <div>
                        <span className="font-medium">Stock:</span>
                        <div className={product.inStock ? "text-green-600" : "text-red-600"}>
                          {product.inStock ? "In Stock" : "Out of Stock"}
                        </div>
                      </div>
                    </div>

                    {/* Features */}
                    <div>
                      <h4 className="font-semibold mb-2">Features</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {product.features.map((feature, idx) => (
                          <li key={idx} className="flex items-start">
                            <CheckCircle className="w-3 h-3 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Colors */}
                    <div>
                      <h4 className="font-semibold mb-2">Available Colors</h4>
                      <div className="flex flex-wrap gap-2">
                        {product.colors.map((color) => (
                          <Badge key={color} variant="outline" className="text-xs">
                            {color}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Button 
                        className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                        disabled={!product.inStock}
                      >
                        <ShoppingCart className="w-4 h-4 mr-2" />
                        {product.inStock ? "Add to Cart" : "Out of Stock"}
                      </Button>
                      <Button variant="outline" className="w-full">
                        Learn More
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* How It Works */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-center">How ByteTag Works</CardTitle>
            <CardDescription className="text-center">
              Simple setup, powerful protection for your pet
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-4 gap-6">
              {[
                {
                  step: "1",
                  title: "Attach to Collar",
                  description: "Securely attach ByteTag to your pet's collar",
                  icon: Heart
                },
                {
                  step: "2",
                  title: "Download App",
                  description: "Install the ByteTag app and create your account",
                  icon: Smartphone
                },
                {
                  step: "3",
                  title: "Set Up Profile",
                  description: "Add your pet's information and emergency contacts",
                  icon: Users
                },
                {
                  step: "4",
                  title: "Track & Monitor",
                  description: "Monitor location, activity, and health in real-time",
                  icon: MapPin
                }
              ].map((step, index) => (
                <div key={step.step} className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <step.icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <div className="text-lg font-semibold mb-2">Step {step.step}</div>
                  <div className="font-medium mb-2">{step.title}</div>
                  <div className="text-sm text-gray-600">{step.description}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* App Features */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>ByteTag Mobile App Features</CardTitle>
            <CardDescription>
              Everything you need to keep your pet safe, right in your pocket
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="tracking" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="tracking">Tracking</TabsTrigger>
                <TabsTrigger value="health">Health</TabsTrigger>
                <TabsTrigger value="alerts">Alerts</TabsTrigger>
                <TabsTrigger value="community">Community</TabsTrigger>
              </TabsList>
              
              <TabsContent value="tracking" className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <MapPin className="w-4 h-4 mr-2 text-blue-600" />
                      Real-Time Location
                    </h4>
                    <p className="text-sm text-gray-600">
                      See your pet's exact location on a detailed map with GPS accuracy down to 3 feet.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Clock className="w-4 h-4 mr-2 text-blue-600" />
                      Location History
                    </h4>
                    <p className="text-sm text-gray-600">
                      View where your pet has been with detailed location history and timeline.
                    </p>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="health" className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Zap className="w-4 h-4 mr-2 text-green-600" />
                      Activity Monitoring
                    </h4>
                    <p className="text-sm text-gray-600">
                      Track your pet's daily activity, exercise levels, and sleep patterns.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Heart className="w-4 h-4 mr-2 text-red-600" />
                      Health Insights
                    </h4>
                    <p className="text-sm text-gray-600">
                      Get AI-powered insights about your pet's health and behavior patterns.
                    </p>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="alerts" className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Shield className="w-4 h-4 mr-2 text-orange-600" />
                      Safe Zone Alerts
                    </h4>
                    <p className="text-sm text-gray-600">
                      Set up safe zones and get instant alerts when your pet leaves these areas.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Phone className="w-4 h-4 mr-2 text-purple-600" />
                      Emergency Notifications
                    </h4>
                    <p className="text-sm text-gray-600">
                      Automatic emergency alerts to you and your emergency contacts.
                    </p>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="community" className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Users className="w-4 h-4 mr-2 text-blue-600" />
                      Community Network
                    </h4>
                    <p className="text-sm text-gray-600">
                      Connect with other ByteTag users to help find lost pets in your area.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Award className="w-4 h-4 mr-2 text-yellow-600" />
                      Lost Pet Alerts
                    </h4>
                    <p className="text-sm text-gray-600">
                      Receive and share lost pet alerts with the ByteTag community network.
                    </p>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Subscription Plans */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>ByteTag Service Plans</CardTitle>
            <CardDescription>
              Choose the right service plan for your ByteTag device
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              {[
                {
                  name: "Basic Plan",
                  price: "$4.99/month",
                  features: ["Real-time tracking", "Basic alerts", "Location history", "Mobile app access"]
                },
                {
                  name: "Premium Plan",
                  price: "$9.99/month",
                  features: ["Everything in Basic", "Health monitoring", "Advanced analytics", "Priority support"],
                  popular: true
                },
                {
                  name: "Family Plan",
                  price: "$19.99/month",
                  features: ["Up to 5 pets", "All Premium features", "Family sharing", "Vet integration"]
                }
              ].map((plan, index) => (
                <Card key={plan.name} className={plan.popular ? "ring-2 ring-blue-500" : ""}>
                  <CardHeader className="text-center">
                    {plan.popular && (
                      <Badge className="bg-blue-500 text-white mb-2">Most Popular</Badge>
                    )}
                    <CardTitle>{plan.name}</CardTitle>
                    <div className="text-2xl font-bold text-blue-600">{plan.price}</div>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {plan.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start text-sm">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <Button className="w-full mt-4">
                      Choose Plan
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Customer Reviews */}
        <Card>
          <CardHeader>
            <CardTitle>What Pet Parents Say</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              {[
                {
                  name: "Sarah M.",
                  rating: 5,
                  review: "ByteTag saved my dog's life! He got out during a storm and I found him within 30 minutes thanks to the GPS tracking.",
                  pet: "Golden Retriever"
                },
                {
                  name: "Mike T.",
                  rating: 5,
                  review: "The battery life is amazing and the app is so easy to use. I love getting activity updates about my cat.",
                  pet: "Maine Coon"
                },
                {
                  name: "Lisa K.",
                  rating: 4,
                  review: "Great product! The safe zone alerts give me peace of mind when my dog is in the backyard.",
                  pet: "Border Collie"
                }
              ].map((review, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-center mb-2">
                      {renderStars(review.rating)}
                    </div>
                    <p className="text-sm text-gray-600 mb-3">"{review.review}"</p>
                    <div className="text-sm">
                      <div className="font-medium">{review.name}</div>
                      <div className="text-gray-500">{review.pet} parent</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
