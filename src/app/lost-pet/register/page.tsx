"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Shield,
  User,
  Heart,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Camera,
  FileText,
  CheckCircle,
  AlertTriangle,
  Info,
  Search,
  Upload,
  Save,
  Edit
} from "lucide-react"
import { motion } from "framer-motion"

interface PetRegistration {
  petInfo: {
    name: string
    species: string
    breed: string
    color: string
    age: string
    weight: string
    gender: string
    microchipId: string
    description: string
  }
  ownerInfo: {
    firstName: string
    lastName: string
    email: string
    phone: string
    address: string
    city: string
    state: string
    zipCode: string
    emergencyContact: string
    emergencyPhone: string
  }
  veterinaryInfo: {
    clinicName: string
    vetName: string
    clinicPhone: string
    clinicAddress: string
  }
}

export default function PetRegisterPage() {
  const [activeTab, setActiveTab] = useState("register")
  const [currentStep, setCurrentStep] = useState(1)
  const [lookupId, setLookupId] = useState("")
  const [registration, setRegistration] = useState<PetRegistration>({
    petInfo: {
      name: "",
      species: "",
      breed: "",
      color: "",
      age: "",
      weight: "",
      gender: "",
      microchipId: "",
      description: ""
    },
    ownerInfo: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      address: "",
      city: "",
      state: "",
      zipCode: "",
      emergencyContact: "",
      emergencyPhone: ""
    },
    veterinaryInfo: {
      clinicName: "",
      vetName: "",
      clinicPhone: "",
      clinicAddress: ""
    }
  })

  const handleInputChange = (section: keyof PetRegistration, field: string, value: string) => {
    setRegistration(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
  }

  const nextStep = () => {
    if (currentStep < 4) setCurrentStep(currentStep + 1)
  }

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-green-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Pet Registration
              <span className="block text-blue-300">& Microchip Lookup</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-green-100">
              Register your pet's information and keep your microchip data current
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Main Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="register">Register Pet</TabsTrigger>
            <TabsTrigger value="lookup">Microchip Lookup</TabsTrigger>
            <TabsTrigger value="update">Update Information</TabsTrigger>
          </TabsList>

          {/* Register Pet Tab */}
          <TabsContent value="register" className="space-y-6">
            {/* Progress Indicator */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  {[1, 2, 3, 4].map((step) => (
                    <div key={step} className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        step <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
                      }`}>
                        {step < currentStep ? <CheckCircle className="w-4 h-4" /> : step}
                      </div>
                      {step < 4 && (
                        <div className={`w-16 h-1 mx-2 ${
                          step < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                        }`} />
                      )}
                    </div>
                  ))}
                </div>
                <div className="text-center">
                  <h3 className="font-semibold">
                    Step {currentStep}: {
                      currentStep === 1 ? "Pet Information" :
                      currentStep === 2 ? "Owner Information" :
                      currentStep === 3 ? "Veterinary Information" :
                      "Review & Submit"
                    }
                  </h3>
                </div>
              </CardContent>
            </Card>

            {/* Step 1: Pet Information */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Heart className="w-5 h-5 mr-2 text-red-600" />
                    Pet Information
                  </CardTitle>
                  <CardDescription>
                    Tell us about your pet
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Pet Name *</label>
                      <Input
                        placeholder="Enter pet's name"
                        value={registration.petInfo.name}
                        onChange={(e) => handleInputChange('petInfo', 'name', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Species *</label>
                      <select 
                        className="w-full p-2 border rounded-md"
                        value={registration.petInfo.species}
                        onChange={(e) => handleInputChange('petInfo', 'species', e.target.value)}
                      >
                        <option value="">Select species</option>
                        <option value="dog">Dog</option>
                        <option value="cat">Cat</option>
                        <option value="bird">Bird</option>
                        <option value="rabbit">Rabbit</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Breed</label>
                      <Input
                        placeholder="Enter breed"
                        value={registration.petInfo.breed}
                        onChange={(e) => handleInputChange('petInfo', 'breed', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Color/Markings</label>
                      <Input
                        placeholder="Describe color and markings"
                        value={registration.petInfo.color}
                        onChange={(e) => handleInputChange('petInfo', 'color', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Age</label>
                      <Input
                        placeholder="e.g., 2 years, 6 months"
                        value={registration.petInfo.age}
                        onChange={(e) => handleInputChange('petInfo', 'age', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Weight</label>
                      <Input
                        placeholder="e.g., 25 lbs"
                        value={registration.petInfo.weight}
                        onChange={(e) => handleInputChange('petInfo', 'weight', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Gender</label>
                      <select 
                        className="w-full p-2 border rounded-md"
                        value={registration.petInfo.gender}
                        onChange={(e) => handleInputChange('petInfo', 'gender', e.target.value)}
                      >
                        <option value="">Select gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="male-neutered">Male (Neutered)</option>
                        <option value="female-spayed">Female (Spayed)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Microchip ID *</label>
                      <Input
                        placeholder="15-digit microchip number"
                        value={registration.petInfo.microchipId}
                        onChange={(e) => handleInputChange('petInfo', 'microchipId', e.target.value)}
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Additional Description</label>
                    <textarea
                      className="w-full p-2 border rounded-md h-24"
                      placeholder="Any additional identifying features or special notes"
                      value={registration.petInfo.description}
                      onChange={(e) => handleInputChange('petInfo', 'description', e.target.value)}
                    />
                  </div>
                  <div className="flex justify-end">
                    <Button onClick={nextStep}>
                      Next Step
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 2: Owner Information */}
            {currentStep === 2 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="w-5 h-5 mr-2 text-blue-600" />
                    Owner Information
                  </CardTitle>
                  <CardDescription>
                    Your contact information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">First Name *</label>
                      <Input
                        placeholder="Enter first name"
                        value={registration.ownerInfo.firstName}
                        onChange={(e) => handleInputChange('ownerInfo', 'firstName', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Last Name *</label>
                      <Input
                        placeholder="Enter last name"
                        value={registration.ownerInfo.lastName}
                        onChange={(e) => handleInputChange('ownerInfo', 'lastName', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Email *</label>
                      <Input
                        type="email"
                        placeholder="Enter email address"
                        value={registration.ownerInfo.email}
                        onChange={(e) => handleInputChange('ownerInfo', 'email', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Phone Number *</label>
                      <Input
                        placeholder="Enter phone number"
                        value={registration.ownerInfo.phone}
                        onChange={(e) => handleInputChange('ownerInfo', 'phone', e.target.value)}
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium mb-1">Address *</label>
                      <Input
                        placeholder="Enter street address"
                        value={registration.ownerInfo.address}
                        onChange={(e) => handleInputChange('ownerInfo', 'address', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">City *</label>
                      <Input
                        placeholder="Enter city"
                        value={registration.ownerInfo.city}
                        onChange={(e) => handleInputChange('ownerInfo', 'city', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">State *</label>
                      <Input
                        placeholder="Enter state"
                        value={registration.ownerInfo.state}
                        onChange={(e) => handleInputChange('ownerInfo', 'state', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Zip Code *</label>
                      <Input
                        placeholder="Enter zip code"
                        value={registration.ownerInfo.zipCode}
                        onChange={(e) => handleInputChange('ownerInfo', 'zipCode', e.target.value)}
                      />
                    </div>
                  </div>
                  
                  <div className="border-t pt-4">
                    <h4 className="font-semibold mb-3">Emergency Contact</h4>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">Emergency Contact Name</label>
                        <Input
                          placeholder="Enter emergency contact name"
                          value={registration.ownerInfo.emergencyContact}
                          onChange={(e) => handleInputChange('ownerInfo', 'emergencyContact', e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Emergency Contact Phone</label>
                        <Input
                          placeholder="Enter emergency contact phone"
                          value={registration.ownerInfo.emergencyPhone}
                          onChange={(e) => handleInputChange('ownerInfo', 'emergencyPhone', e.target.value)}
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={prevStep}>
                      Previous
                    </Button>
                    <Button onClick={nextStep}>
                      Next Step
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 3: Veterinary Information */}
            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Shield className="w-5 h-5 mr-2 text-green-600" />
                    Veterinary Information
                  </CardTitle>
                  <CardDescription>
                    Your pet's veterinary clinic information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Clinic Name</label>
                      <Input
                        placeholder="Enter veterinary clinic name"
                        value={registration.veterinaryInfo.clinicName}
                        onChange={(e) => handleInputChange('veterinaryInfo', 'clinicName', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Veterinarian Name</label>
                      <Input
                        placeholder="Enter veterinarian's name"
                        value={registration.veterinaryInfo.vetName}
                        onChange={(e) => handleInputChange('veterinaryInfo', 'vetName', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Clinic Phone</label>
                      <Input
                        placeholder="Enter clinic phone number"
                        value={registration.veterinaryInfo.clinicPhone}
                        onChange={(e) => handleInputChange('veterinaryInfo', 'clinicPhone', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Clinic Address</label>
                      <Input
                        placeholder="Enter clinic address"
                        value={registration.veterinaryInfo.clinicAddress}
                        onChange={(e) => handleInputChange('veterinaryInfo', 'clinicAddress', e.target.value)}
                      />
                    </div>
                  </div>
                  
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertTitle>Why do we need veterinary information?</AlertTitle>
                    <AlertDescription>
                      Veterinary information helps us quickly reunite you with your pet and ensures 
                      they receive proper medical care if found injured.
                    </AlertDescription>
                  </Alert>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={prevStep}>
                      Previous
                    </Button>
                    <Button onClick={nextStep}>
                      Review & Submit
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 4: Review & Submit */}
            {currentStep === 4 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="w-5 h-5 mr-2 text-purple-600" />
                    Review & Submit
                  </CardTitle>
                  <CardDescription>
                    Please review your information before submitting
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Review sections would go here */}
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">Pet Information</h4>
                      <div className="bg-gray-50 p-4 rounded-lg text-sm">
                        <p><strong>Name:</strong> {registration.petInfo.name}</p>
                        <p><strong>Species:</strong> {registration.petInfo.species}</p>
                        <p><strong>Microchip ID:</strong> {registration.petInfo.microchipId}</p>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold mb-2">Owner Information</h4>
                      <div className="bg-gray-50 p-4 rounded-lg text-sm">
                        <p><strong>Name:</strong> {registration.ownerInfo.firstName} {registration.ownerInfo.lastName}</p>
                        <p><strong>Email:</strong> {registration.ownerInfo.email}</p>
                        <p><strong>Phone:</strong> {registration.ownerInfo.phone}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={prevStep}>
                      Previous
                    </Button>
                    <Button className="bg-green-600 hover:bg-green-700">
                      <Save className="w-4 h-4 mr-2" />
                      Submit Registration
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Microchip Lookup Tab */}
          <TabsContent value="lookup" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Search className="w-5 h-5 mr-2 text-blue-600" />
                  Microchip Lookup
                </CardTitle>
                <CardDescription>
                  Find pet information using a microchip ID
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="max-w-md">
                  <label className="block text-sm font-medium mb-1">Microchip ID</label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter 15-digit microchip number"
                      value={lookupId}
                      onChange={(e) => setLookupId(e.target.value)}
                    />
                    <Button>
                      <Search className="w-4 h-4 mr-2" />
                      Search
                    </Button>
                  </div>
                </div>
                
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>Found a lost pet?</AlertTitle>
                  <AlertDescription>
                    If you've found a pet with a microchip, enter the chip number here to find the owner's contact information.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Update Information Tab */}
          <TabsContent value="update" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Edit className="w-5 h-5 mr-2 text-green-600" />
                  Update Pet Information
                </CardTitle>
                <CardDescription>
                  Update your pet's registration information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="max-w-md">
                  <label className="block text-sm font-medium mb-1">Microchip ID</label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter your pet's microchip number"
                      value={lookupId}
                      onChange={(e) => setLookupId(e.target.value)}
                    />
                    <Button>
                      <Search className="w-4 h-4 mr-2" />
                      Find Record
                    </Button>
                  </div>
                </div>
                
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Keep Your Information Current</AlertTitle>
                  <AlertDescription>
                    It's important to update your contact information whenever you move or change phone numbers 
                    to ensure your pet can be returned to you quickly.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
