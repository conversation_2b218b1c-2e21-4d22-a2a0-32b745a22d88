"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Shield,
  MapPin,
  Clock,
  DollarSign,
  CheckCircle,
  AlertTriangle,
  Phone,
  Calendar,
  Search,
  Heart,
  Award,
  Users,
  Zap,
  Info,
  FileText,
  Building
} from "lucide-react"
import { motion } from "framer-motion"

interface MicrochipProvider {
  id: string
  name: string
  price: string
  location: string
  rating: number
  reviews: number
  services: string[]
  availability: string
  phone: string
  address: string
  distance: string
}

const microchipProviders: MicrochipProvider[] = [
  {
    id: "1",
    name: "PetCare Veterinary Clinic",
    price: "$45",
    location: "Downtown",
    rating: 4.9,
    reviews: 342,
    services: ["Microchipping", "Registration", "Health Check", "Vaccination"],
    availability: "Same day appointments",
    phone: "(*************",
    address: "123 Main St, Downtown",
    distance: "2.3 miles"
  },
  {
    id: "2",
    name: "Animal Hospital Plus",
    price: "$35",
    location: "Westside",
    rating: 4.7,
    reviews: 198,
    services: ["Microchipping", "Registration", "Emergency Care"],
    availability: "Walk-ins welcome",
    phone: "(*************",
    address: "456 Oak Ave, Westside",
    distance: "4.1 miles"
  },
  {
    id: "3",
    name: "Mobile Pet Services",
    price: "$55",
    location: "Mobile Service",
    rating: 4.8,
    reviews: 156,
    services: ["Home Microchipping", "Registration", "Health Consultation"],
    availability: "By appointment",
    phone: "(*************",
    address: "Serves entire metro area",
    distance: "Comes to you"
  }
]

export default function MicrochippingPage() {
  const [zipCode, setZipCode] = useState("")
  const [selectedProvider, setSelectedProvider] = useState<MicrochipProvider | null>(null)

  const renderStars = (rating: number) => (
    <div className="flex items-center">
      {[1, 2, 3, 4, 5].map((star) => (
        <div
          key={star}
          className={`w-4 h-4 ${
            star <= rating ? 'text-yellow-400' : 'text-gray-300'
          }`}
        >
          ★
        </div>
      ))}
      <span className="ml-2 text-sm text-gray-600">{rating}</span>
    </div>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Pet
              <span className="block text-green-300">Microchipping</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Permanent identification that brings pets home safely
            </p>
            <div className="flex justify-center gap-4">
              <Button size="lg" className="bg-white text-green-600 hover:bg-gray-100">
                Find Providers
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600">
                Learn More
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Statistics */}
        <div className="grid md:grid-cols-4 gap-6 mb-12">
          {[
            { icon: Shield, title: "Success Rate", value: "99.8%", description: "Pets returned home with microchips" },
            { icon: Clock, title: "Quick Process", value: "5 min", description: "Average microchipping time" },
            { icon: Heart, title: "Pets Reunited", value: "2.7M+", description: "Successfully returned to families" },
            { icon: Award, title: "Lifetime ID", value: "25+ years", description: "Microchip lifespan" }
          ].map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <stat.icon className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="text-3xl font-bold text-green-600 mb-1">{stat.value}</div>
                  <div className="font-semibold mb-1">{stat.title}</div>
                  <div className="text-sm text-gray-600">{stat.description}</div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Find Providers */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Search className="w-5 h-5 mr-2 text-blue-600" />
              Find Microchipping Providers Near You
            </CardTitle>
            <CardDescription>
              Enter your zip code to find certified microchipping services in your area
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 max-w-md">
              <Input
                placeholder="Enter zip code"
                value={zipCode}
                onChange={(e) => setZipCode(e.target.value)}
              />
              <Button>
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Provider Results */}
        <div className="grid lg:grid-cols-3 gap-6 mb-12">
          {microchipProviders.map((provider, index) => (
            <motion.div
              key={provider.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="h-full">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{provider.name}</CardTitle>
                      <CardDescription>{provider.location}</CardDescription>
                    </div>
                    <Badge className="bg-green-100 text-green-800">{provider.price}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    {renderStars(provider.rating)}
                    <span className="text-sm text-gray-600">({provider.reviews} reviews)</span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Services</h4>
                    <div className="flex flex-wrap gap-1">
                      {provider.services.map((service) => (
                        <Badge key={service} variant="outline" className="text-xs">
                          {service}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-2 text-gray-500" />
                      {provider.availability}
                    </div>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-2 text-gray-500" />
                      {provider.distance}
                    </div>
                    <div className="flex items-center">
                      <Phone className="w-4 h-4 mr-2 text-gray-500" />
                      {provider.phone}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Button className="w-full">
                      <Calendar className="w-4 h-4 mr-2" />
                      Book Appointment
                    </Button>
                    <Button variant="outline" className="w-full">
                      <Phone className="w-4 h-4 mr-2" />
                      Call Now
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* How Microchipping Works */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>How Microchipping Works</CardTitle>
            <CardDescription>
              Understanding the microchipping process and benefits
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="process" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="process">Process</TabsTrigger>
                <TabsTrigger value="benefits">Benefits</TabsTrigger>
                <TabsTrigger value="aftercare">Aftercare</TabsTrigger>
                <TabsTrigger value="faq">FAQ</TabsTrigger>
              </TabsList>
              
              <TabsContent value="process" className="space-y-6">
                <div className="grid md:grid-cols-4 gap-6">
                  {[
                    {
                      step: "1",
                      title: "Preparation",
                      description: "Your pet is gently restrained, and the injection site is cleaned",
                      icon: Heart
                    },
                    {
                      step: "2",
                      title: "Injection",
                      description: "The microchip is injected between the shoulder blades using a sterile needle",
                      icon: Zap
                    },
                    {
                      step: "3",
                      title: "Registration",
                      description: "Your contact information is registered with the microchip database",
                      icon: FileText
                    },
                    {
                      step: "4",
                      title: "Verification",
                      description: "The microchip is scanned to ensure proper placement and function",
                      icon: CheckCircle
                    }
                  ].map((step, index) => (
                    <div key={step.step} className="text-center">
                      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <step.icon className="w-8 h-8 text-green-600" />
                      </div>
                      <div className="text-lg font-semibold mb-2">Step {step.step}</div>
                      <div className="font-medium mb-2">{step.title}</div>
                      <div className="text-sm text-gray-600">{step.description}</div>
                    </div>
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="benefits" className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Shield className="w-4 h-4 mr-2 text-green-600" />
                      Permanent Identification
                    </h4>
                    <p className="text-sm text-gray-600">
                      Unlike collars and tags, microchips cannot be lost, removed, or damaged.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Users className="w-4 h-4 mr-2 text-blue-600" />
                      Universal Recognition
                    </h4>
                    <p className="text-sm text-gray-600">
                      All veterinary clinics and animal shelters have microchip scanners.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Clock className="w-4 h-4 mr-2 text-purple-600" />
                      Quick Reunification
                    </h4>
                    <p className="text-sm text-gray-600">
                      Pets with microchips are returned to owners 2.5x faster than those without.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <DollarSign className="w-4 h-4 mr-2 text-orange-600" />
                      Cost-Effective
                    </h4>
                    <p className="text-sm text-gray-600">
                      One-time cost provides lifetime identification and peace of mind.
                    </p>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="aftercare" className="space-y-4">
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>Important Aftercare Instructions</AlertTitle>
                  <AlertDescription>
                    <ul className="mt-2 space-y-1 text-sm">
                      <li>• Keep the injection site dry for 24 hours</li>
                      <li>• Monitor for any signs of swelling or irritation</li>
                      <li>• Update your contact information if you move</li>
                      <li>• Ask your vet to scan the chip during regular checkups</li>
                      <li>• Keep your registration current with the database</li>
                    </ul>
                  </AlertDescription>
                </Alert>
              </TabsContent>
              
              <TabsContent value="faq" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Does microchipping hurt my pet?</h4>
                    <p className="text-sm text-gray-600">
                      The injection feels similar to a routine vaccination. Most pets show minimal discomfort.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Can the microchip move around in my pet's body?</h4>
                    <p className="text-sm text-gray-600">
                      Microchips are designed to stay in place. Migration is extremely rare and usually minimal.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">How long do microchips last?</h4>
                    <p className="text-sm text-gray-600">
                      Microchips are designed to last your pet's entire lifetime - typically 25+ years.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">What if I move or change my phone number?</h4>
                    <p className="text-sm text-gray-600">
                      You must update your contact information with the microchip registry to ensure your pet can be returned.
                    </p>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Emergency Alert */}
        <Alert className="border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertTitle className="text-orange-800">Found a Pet with a Microchip?</AlertTitle>
          <AlertDescription className="text-orange-700">
            <div className="mt-2">
              <p className="mb-3">Take the pet to any veterinary clinic or animal shelter to have the microchip scanned.</p>
              <Button className="bg-orange-600 hover:bg-orange-700">
                <Building className="w-4 h-4 mr-2" />
                Find Nearest Scanner Location
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
