"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Search,
  MapPin,
  Clock,
  Phone,
  Heart,
  AlertTriangle,
  CheckCircle,
  FileText,
  Users,
  Megaphone,
  Camera,
  Share2,
  Download,
  ExternalLink,
  Info,
  Lightbulb,
  Target
} from "lucide-react"
import { motion } from "framer-motion"

interface Resource {
  id: string
  title: string
  description: string
  category: string
  type: "guide" | "checklist" | "template" | "contact"
  downloadUrl?: string
  externalUrl?: string
  urgent?: boolean
}

const resources: Resource[] = [
  {
    id: "1",
    title: "Lost Pet Action Plan",
    description: "Step-by-step guide for the first 24 hours when your pet goes missing",
    category: "Emergency",
    type: "guide",
    downloadUrl: "/resources/lost-pet-action-plan.pdf",
    urgent: true
  },
  {
    id: "2",
    title: "Missing Pet Flyer Template",
    description: "Professional template for creating effective missing pet flyers",
    category: "Search Tools",
    type: "template",
    downloadUrl: "/resources/missing-pet-flyer-template.pdf"
  },
  {
    id: "3",
    title: "Local Shelter Directory",
    description: "Complete list of animal shelters and rescue organizations in your area",
    category: "Contacts",
    type: "contact",
    externalUrl: "/shelter-directory"
  },
  {
    id: "4",
    title: "Social Media Search Strategy",
    description: "How to effectively use social media to find your lost pet",
    category: "Search Tools",
    type: "guide"
  },
  {
    id: "5",
    title: "Prevention Checklist",
    description: "Essential steps to prevent your pet from getting lost",
    category: "Prevention",
    type: "checklist"
  },
  {
    id: "6",
    title: "Microchip Registration Guide",
    description: "How to properly register and maintain your pet's microchip information",
    category: "Prevention",
    type: "guide"
  }
]

const emergencyContacts = [
  {
    name: "24/7 Lost Pet Hotline",
    phone: "1-800-PET-HELP",
    description: "Immediate assistance for lost pets",
    available: "24/7"
  },
  {
    name: "Animal Control Services",
    phone: "311",
    description: "Local animal control and stray animal pickup",
    available: "Business hours"
  },
  {
    name: "Emergency Veterinary Clinic",
    phone: "(555) VET-HELP",
    description: "Emergency medical care for found injured pets",
    available: "24/7"
  }
]

export default function LostPetResourcesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")

  const categories = ["All", "Emergency", "Search Tools", "Prevention", "Contacts"]

  const filteredResources = resources.filter(resource => {
    const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "All" || resource.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const urgentResources = resources.filter(resource => resource.urgent)

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-orange-600 to-red-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Lost Pet
              <span className="block text-orange-300">Resources</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-red-100">
              Comprehensive guides, tools, and contacts to help bring your pet home
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Emergency Alert */}
        <Alert className="mb-8 border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertTitle className="text-red-800">Pet Missing Right Now?</AlertTitle>
          <AlertDescription className="text-red-700">
            <div className="mt-2 space-y-2">
              <p>Don't panic! Time is critical. Follow these immediate steps:</p>
              <div className="flex flex-wrap gap-2">
                <Button size="sm" className="bg-red-600 hover:bg-red-700">
                  <Phone className="w-3 h-3 mr-1" />
                  Call Hotline
                </Button>
                <Button size="sm" variant="outline" className="border-red-600 text-red-600">
                  <Download className="w-3 h-3 mr-1" />
                  Action Plan
                </Button>
                <Button size="sm" variant="outline" className="border-red-600 text-red-600">
                  <FileText className="w-3 h-3 mr-1" />
                  Make Flyers
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>

        {/* Quick Access - Urgent Resources */}
        {urgentResources.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-4 flex items-center">
              <AlertTriangle className="w-6 h-6 mr-2 text-red-600" />
              Emergency Resources
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {urgentResources.map((resource, index) => (
                <motion.div
                  key={resource.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="border-red-200 bg-red-50">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <Badge className="bg-red-600 text-white">URGENT</Badge>
                        <FileText className="w-5 h-5 text-red-600" />
                      </div>
                      <CardTitle className="text-lg">{resource.title}</CardTitle>
                      <CardDescription className="text-red-700">
                        {resource.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button className="w-full bg-red-600 hover:bg-red-700">
                        <Download className="w-4 h-4 mr-2" />
                        Download Now
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Emergency Contacts */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Phone className="w-5 h-5 mr-2 text-blue-600" />
              Emergency Contacts
            </CardTitle>
            <CardDescription>
              Important phone numbers to have ready
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              {emergencyContacts.map((contact, index) => (
                <div key={index} className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-900">{contact.name}</h4>
                  <div className="text-2xl font-bold text-blue-600 my-2">{contact.phone}</div>
                  <p className="text-sm text-blue-700 mb-2">{contact.description}</p>
                  <Badge variant="outline" className="text-xs">
                    <Clock className="w-3 h-3 mr-1" />
                    {contact.available}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Search and Filter */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search resources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                {categories.map(category => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Resource Grid */}
        <div className="grid lg:grid-cols-3 gap-6 mb-12">
          {filteredResources.map((resource, index) => (
            <motion.div
              key={resource.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="h-full">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <Badge>{resource.category}</Badge>
                    <div className="flex items-center">
                      {resource.type === "guide" && <FileText className="w-4 h-4 text-blue-600" />}
                      {resource.type === "checklist" && <CheckCircle className="w-4 h-4 text-green-600" />}
                      {resource.type === "template" && <Camera className="w-4 h-4 text-purple-600" />}
                      {resource.type === "contact" && <Phone className="w-4 h-4 text-orange-600" />}
                    </div>
                  </div>
                  <CardTitle className="text-lg">{resource.title}</CardTitle>
                  <CardDescription>{resource.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {resource.downloadUrl && (
                      <Button className="w-full" variant="outline">
                        <Download className="w-4 h-4 mr-2" />
                        Download
                      </Button>
                    )}
                    {resource.externalUrl && (
                      <Button className="w-full" variant="outline">
                        <ExternalLink className="w-4 h-4 mr-2" />
                        View Resource
                      </Button>
                    )}
                    {!resource.downloadUrl && !resource.externalUrl && (
                      <Button className="w-full">
                        View Details
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Additional Resources Tabs */}
        <Tabs defaultValue="tips" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="tips">Search Tips</TabsTrigger>
            <TabsTrigger value="prevention">Prevention</TabsTrigger>
            <TabsTrigger value="social">Social Media</TabsTrigger>
            <TabsTrigger value="support">Support</TabsTrigger>
          </TabsList>

          <TabsContent value="tips" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="w-5 h-5 mr-2 text-blue-600" />
                  Effective Search Strategies
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <MapPin className="w-4 h-4 mr-2 text-green-600" />
                      Physical Search
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Search at dawn and dusk when pets are most active</li>
                      <li>• Bring familiar items like toys or blankets</li>
                      <li>• Check hiding spots: under porches, in garages, etc.</li>
                      <li>• Ask neighbors to check their properties</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Megaphone className="w-4 h-4 mr-2 text-purple-600" />
                      Spread the Word
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Post flyers in high-traffic areas</li>
                      <li>• Contact local shelters and veterinary clinics</li>
                      <li>• Use social media and lost pet websites</li>
                      <li>• Notify mail carriers and delivery drivers</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="prevention" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Lightbulb className="w-5 h-5 mr-2 text-yellow-600" />
                  Prevention is Key
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Essential Prevention Steps</h4>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                          <span className="text-sm">Microchip your pet and keep registration current</span>
                        </div>
                        <div className="flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                          <span className="text-sm">Use proper ID tags with current contact information</span>
                        </div>
                        <div className="flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                          <span className="text-sm">Secure your yard with proper fencing</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                          <span className="text-sm">Train your pet to come when called</span>
                        </div>
                        <div className="flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                          <span className="text-sm">Keep recent photos of your pet</span>
                        </div>
                        <div className="flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5" />
                          <span className="text-sm">Spay/neuter to reduce roaming behavior</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="social" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Share2 className="w-5 h-5 mr-2 text-blue-600" />
                  Social Media Strategy
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Effective Social Media Posts</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Use clear, recent photos of your pet</li>
                      <li>• Include specific location where pet was lost</li>
                      <li>• Mention distinctive features or markings</li>
                      <li>• Add relevant hashtags (#LostPet #YourCity)</li>
                      <li>• Post in local community groups</li>
                      <li>• Share on multiple platforms (Facebook, Instagram, Twitter)</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Recommended Platforms</h4>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <h5 className="font-medium">Facebook</h5>
                        <p className="text-xs text-gray-600">Local community groups, lost pet pages</p>
                      </div>
                      <div className="bg-purple-50 p-3 rounded-lg">
                        <h5 className="font-medium">Nextdoor</h5>
                        <p className="text-xs text-gray-600">Neighborhood-specific platform</p>
                      </div>
                      <div className="bg-pink-50 p-3 rounded-lg">
                        <h5 className="font-medium">Instagram</h5>
                        <p className="text-xs text-gray-600">Visual platform with location tags</p>
                      </div>
                      <div className="bg-green-50 p-3 rounded-lg">
                        <h5 className="font-medium">PawBoost</h5>
                        <p className="text-xs text-gray-600">Dedicated lost pet platform</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="support" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Heart className="w-5 h-5 mr-2 text-red-600" />
                  Emotional Support & Community
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">You're Not Alone</h4>
                    <p className="text-sm text-gray-600 mb-4">
                      Losing a pet is emotionally devastating. Remember that thousands of pets are successfully 
                      reunited with their families every day. Stay hopeful and keep searching.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Support Resources</h4>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full justify-start">
                        <Users className="w-4 h-4 mr-2" />
                        Join Lost Pet Support Groups
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <Phone className="w-4 h-4 mr-2" />
                        24/7 Emotional Support Hotline
                      </Button>
                      <Button variant="outline" className="w-full justify-start">
                        <Heart className="w-4 h-4 mr-2" />
                        Connect with Volunteers
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
