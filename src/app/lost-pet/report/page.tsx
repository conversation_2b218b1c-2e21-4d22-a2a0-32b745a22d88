"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Search,
  MapPin,
  Camera,
  Clock,
  Phone,
  Mail,
  Heart,
  AlertTriangle,
  CheckCircle,
  Upload,
  Calendar,
  User,
  PawPrint
} from "lucide-react"
import { motion } from "framer-motion"

interface ReportForm {
  type: "lost" | "found"
  petType: string
  petName: string
  breed: string
  color: string
  size: string
  age: string
  gender: string
  description: string
  lastSeen: string
  location: string
  contactName: string
  contactPhone: string
  contactEmail: string
  additionalInfo: string
  hasCollar: boolean
  hasMicrochip: boolean
  microchipNumber: string
  reward: string
}

export default function ReportLostPetPage() {
  const [activeTab, setActiveTab] = useState<"lost" | "found">("lost")
  const [submitted, setSubmitted] = useState(false)
  const [formData, setFormData] = useState<Partial<ReportForm>>({
    type: "lost",
    hasCollar: false,
    hasMicrochip: false
  })

  const handleInputChange = (field: keyof ReportForm, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically send the data to your backend
    console.log("Report submitted:", formData)
    setSubmitted(true)
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="max-w-md mx-auto"
        >
          <Card className="text-center">
            <CardHeader>
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl text-green-800">Report Submitted!</CardTitle>
              <CardDescription>
                Your {formData.type} pet report has been submitted successfully
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-6">
                We'll share your report with our network and notify you of any matches. 
                Keep checking back for updates.
              </p>
              <div className="space-y-2">
                <Button onClick={() => window.location.href = '/'} className="w-full">
                  Return to Home
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setSubmitted(false)} 
                  className="w-full"
                >
                  Submit Another Report
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-orange-600 to-red-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Report a Lost or
              <span className="block text-orange-300">Found Pet</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-red-100">
              Help reunite pets with their families through our community network
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Emergency Alert */}
        <Alert className="mb-8 border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertTitle className="text-orange-800">Time is Critical!</AlertTitle>
          <AlertDescription className="text-orange-700">
            The first 24-48 hours are crucial for finding lost pets. Report immediately and start searching in your immediate area.
          </AlertDescription>
        </Alert>

        {/* Quick Stats */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {[
            { icon: Search, title: "Active Searches", value: "247", description: "Pets currently being searched for" },
            { icon: Heart, title: "Reunited", value: "1,834", description: "Successful reunions this year" },
            { icon: Clock, title: "Average Time", value: "3.2 days", description: "Average time to reunion" }
          ].map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <stat.icon className="w-6 h-6 text-orange-600" />
                  </div>
                  <div className="text-3xl font-bold text-orange-600 mb-1">{stat.value}</div>
                  <div className="font-semibold mb-1">{stat.title}</div>
                  <div className="text-sm text-gray-600">{stat.description}</div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Report Form */}
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center">
              <PawPrint className="w-6 h-6 mr-2 text-orange-600" />
              Submit a Pet Report
            </CardTitle>
            <CardDescription>
              Fill out this form to report a lost or found pet. The more details you provide, the better we can help.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={(value) => {
              setActiveTab(value as "lost" | "found")
              setFormData(prev => ({ ...prev, type: value as "lost" | "found" }))
            }}>
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="lost" className="flex items-center space-x-2">
                  <Search className="w-4 h-4" />
                  <span>Lost Pet</span>
                </TabsTrigger>
                <TabsTrigger value="found" className="flex items-center space-x-2">
                  <Heart className="w-4 h-4" />
                  <span>Found Pet</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="lost" className="space-y-6">
                <Alert className="border-red-200 bg-red-50">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <AlertTitle className="text-red-800">Lost Pet Report</AlertTitle>
                  <AlertDescription className="text-red-700">
                    Report your missing pet to our network. Include as much detail as possible to help with identification.
                  </AlertDescription>
                </Alert>
              </TabsContent>

              <TabsContent value="found" className="space-y-6">
                <Alert className="border-green-200 bg-green-50">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertTitle className="text-green-800">Found Pet Report</AlertTitle>
                  <AlertDescription className="text-green-700">
                    Report a pet you've found to help reunite them with their family. Thank you for being a good Samaritan!
                  </AlertDescription>
                </Alert>
              </TabsContent>
            </Tabs>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Pet Information */}
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Pet Type *</label>
                  <select
                    value={formData.petType || ""}
                    onChange={(e) => handleInputChange("petType", e.target.value)}
                    required
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="">Select pet type</option>
                    <option value="dog">Dog</option>
                    <option value="cat">Cat</option>
                    <option value="bird">Bird</option>
                    <option value="rabbit">Rabbit</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Pet Name {activeTab === "lost" ? "*" : "(if known)"}
                  </label>
                  <Input
                    value={formData.petName || ""}
                    onChange={(e) => handleInputChange("petName", e.target.value)}
                    required={activeTab === "lost"}
                    placeholder="Enter pet's name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Breed</label>
                  <Input
                    value={formData.breed || ""}
                    onChange={(e) => handleInputChange("breed", e.target.value)}
                    placeholder="e.g., Golden Retriever, Mixed"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Primary Color *</label>
                  <Input
                    value={formData.color || ""}
                    onChange={(e) => handleInputChange("color", e.target.value)}
                    required
                    placeholder="e.g., Brown, Black, White"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Size *</label>
                  <select
                    value={formData.size || ""}
                    onChange={(e) => handleInputChange("size", e.target.value)}
                    required
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="">Select size</option>
                    <option value="small">Small (under 25 lbs)</option>
                    <option value="medium">Medium (25-60 lbs)</option>
                    <option value="large">Large (60-100 lbs)</option>
                    <option value="extra-large">Extra Large (over 100 lbs)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Gender</label>
                  <select
                    value={formData.gender || ""}
                    onChange={(e) => handleInputChange("gender", e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="">Unknown</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                  </select>
                </div>
              </div>

              {/* Location and Time */}
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {activeTab === "lost" ? "Last Seen Location" : "Found Location"} *
                  </label>
                  <Input
                    value={formData.location || ""}
                    onChange={(e) => handleInputChange("location", e.target.value)}
                    required
                    placeholder="Street address or general area"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    {activeTab === "lost" ? "Date/Time Last Seen" : "Date/Time Found"} *
                  </label>
                  <Input
                    type="datetime-local"
                    value={formData.lastSeen || ""}
                    onChange={(e) => handleInputChange("lastSeen", e.target.value)}
                    required
                  />
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium mb-2">Detailed Description *</label>
                <Textarea
                  value={formData.description || ""}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  required
                  rows={4}
                  placeholder="Describe the pet's appearance, behavior, any distinguishing features..."
                />
              </div>

              {/* Contact Information */}
              <div className="grid md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Your Name *</label>
                  <Input
                    value={formData.contactName || ""}
                    onChange={(e) => handleInputChange("contactName", e.target.value)}
                    required
                    placeholder="Full name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Phone Number *</label>
                  <Input
                    type="tel"
                    value={formData.contactPhone || ""}
                    onChange={(e) => handleInputChange("contactPhone", e.target.value)}
                    required
                    placeholder="(*************"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Email Address *</label>
                  <Input
                    type="email"
                    value={formData.contactEmail || ""}
                    onChange={(e) => handleInputChange("contactEmail", e.target.value)}
                    required
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              {/* Additional Information */}
              {activeTab === "lost" && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.hasCollar || false}
                        onChange={(e) => handleInputChange("hasCollar", e.target.checked)}
                        className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                      />
                      <span>Has collar/tags</span>
                    </label>

                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.hasMicrochip || false}
                        onChange={(e) => handleInputChange("hasMicrochip", e.target.checked)}
                        className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                      />
                      <span>Has microchip</span>
                    </label>
                  </div>

                  {formData.hasMicrochip && (
                    <div>
                      <label className="block text-sm font-medium mb-2">Microchip Number</label>
                      <Input
                        value={formData.microchipNumber || ""}
                        onChange={(e) => handleInputChange("microchipNumber", e.target.value)}
                        placeholder="15-digit microchip number"
                      />
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium mb-2">Reward Amount (optional)</label>
                    <Input
                      value={formData.reward || ""}
                      onChange={(e) => handleInputChange("reward", e.target.value)}
                      placeholder="e.g., $500"
                    />
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium mb-2">Additional Information</label>
                <Textarea
                  value={formData.additionalInfo || ""}
                  onChange={(e) => handleInputChange("additionalInfo", e.target.value)}
                  rows={3}
                  placeholder="Any other relevant information..."
                />
              </div>

              <Button type="submit" className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700">
                Submit {activeTab === "lost" ? "Lost" : "Found"} Pet Report
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Tips Section */}
        <Card className="mt-8 max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>
              {activeTab === "lost" ? "Tips for Finding Your Lost Pet" : "Tips for Helping a Found Pet"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {activeTab === "lost" ? (
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-2">Immediate Actions</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Search your immediate neighborhood</li>
                    <li>• Contact local shelters and veterinarians</li>
                    <li>• Post on social media with photos</li>
                    <li>• Put out familiar scents (your clothing, their bed)</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Extended Search</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Create and distribute flyers</li>
                    <li>• Check with mail carriers and delivery drivers</li>
                    <li>• Search at dawn and dusk when pets are active</li>
                    <li>• Don't give up - pets can be found weeks later</li>
                  </ul>
                </div>
              </div>
            ) : (
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-2">Safety First</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Approach slowly and calmly</li>
                    <li>• Don't chase if the pet runs</li>
                    <li>• Use food or treats to gain trust</li>
                    <li>• Check for ID tags or collar</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Next Steps</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Take the pet to a vet to check for microchip</li>
                    <li>• Contact local animal control</li>
                    <li>• Post on social media and lost pet groups</li>
                    <li>• Keep the pet safe until owner is found</li>
                  </ul>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
