"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { toast } from "react-hot-toast"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { MagicalAuthLayout } from "@/components/auth/MagicalAuthLayout"
import { PetInputField } from "@/components/auth/PetInputField"
import { SparklingButton } from "@/components/auth/SparklingButton"

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("")
  const [loading, setLoading] = useState(false)
  const [sent, setSent] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email) {
      toast.error("Please enter your email address")
      return
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      toast.error("Please enter a valid email address")
      return
    }

    setLoading(true)
    try {
      const response = await fetch("/api/auth/forgot-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      })

      if (response.ok) {
        setSent(true)
        toast.success("Password reset instructions sent to your email! 📧")
      } else {
        const error = await response.json()
        toast.error(error.error || "Failed to send reset email")
      }
    } catch (error) {
      console.error("Error sending reset email:", error)
      toast.error("Failed to send reset email")
    } finally {
      setLoading(false)
    }
  }

  return (
    <MagicalAuthLayout
      title="Reset Your Password"
      subtitle="Enter your email to receive reset instructions"
    >
      <div className="max-w-md mx-auto">
        {/* Back to Sign In Link */}
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Link
            href="/auth/signin"
            className="inline-flex items-center text-pink-600 hover:text-pink-700 dark:text-pink-400 dark:hover:text-pink-300 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Sign In
          </Link>
        </motion.div>

        {/* Form Container */}
        <motion.div
          className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-3xl shadow-xl border border-white/20 dark:border-gray-700/20 p-8"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {!sent ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="text-center mb-6">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  Forgot Your Password?
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  No worries! Enter your email and we'll send you reset instructions.
                </p>
              </div>

              <PetInputField
                label="Email Address"
                icon="mail"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />

              <SparklingButton
                type="submit"
                variant="primary"
                size="lg"
                loading={loading}
                className="w-full"
                sparkleOnHover
              >
                Send Reset Instructions
              </SparklingButton>

              <div className="text-center">
                <p className="text-sm text-gray-600">
                  Remember your password?{" "}
                  <Link
                    href="/auth/signin"
                    className="text-pink-600 hover:text-pink-700 font-medium transition-colors"
                  >
                    Sign in here
                  </Link>
                </p>
              </div>
            </form>
          ) : (
            <div className="text-center space-y-6">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.5, type: "spring" }}
                className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto"
              >
                <span className="text-2xl">📧</span>
              </motion.div>

              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Check Your Email
                </h2>
                <p className="text-gray-600 mb-4">
                  We've sent password reset instructions to:
                </p>
                <p className="font-medium text-gray-900 mb-6">
                  {email}
                </p>
                <p className="text-sm text-gray-500">
                  Didn't receive the email? Check your spam folder or{" "}
                  <button
                    onClick={() => {
                      setSent(false)
                      setEmail("")
                    }}
                    className="text-pink-600 hover:text-pink-700 transition-colors"
                  >
                    try again
                  </button>
                </p>
              </div>

              <SparklingButton
                onClick={() => window.location.href = "/auth/signin"}
                variant="primary"
                size="lg"
                className="w-full"
              >
                Back to Sign In
              </SparklingButton>
            </div>
          )}
        </motion.div>
      </div>
    </MagicalAuthLayout>
  )
}
