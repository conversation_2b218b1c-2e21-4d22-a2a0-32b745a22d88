"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Search, 
  Shield, 
  Home,
  Car,
  Sun,
  Snowflake,
  AlertTriangle,
  CheckCircle,
  Lock,
  Eye,
  Heart,
  Zap,
  Thermometer,
  MapPin
} from "lucide-react"
import { motion } from "framer-motion"

interface SafetyTip {
  title: string
  category: string
  description: string
  tips: string[]
  warnings: string[]
  species: "dogs" | "cats" | "both"
  priority: "high" | "medium" | "low"
}

const safetyTips: SafetyTip[] = [
  {
    title: "Pet-Proofing Your Home",
    category: "home",
    description: "Essential steps to make your home safe for pets",
    tips: [
      "Secure cabinets with child-proof locks",
      "Cover electrical outlets and hide cords",
      "Remove small objects that could be swallowed",
      "Install baby gates to restrict access to dangerous areas",
      "Secure windows and balconies",
      "Store cleaning products in locked cabinets"
    ],
    warnings: [
      "Check for toxic plants before bringing them home",
      "Keep medications out of reach",
      "Secure trash cans with tight-fitting lids"
    ],
    species: "both",
    priority: "high"
  },
  {
    title: "Car Travel Safety",
    category: "travel",
    description: "Keeping your pet safe during car rides",
    tips: [
      "Use a proper pet carrier or car harness",
      "Never leave pets unattended in vehicles",
      "Ensure proper ventilation during travel",
      "Take frequent breaks on long trips",
      "Keep a pet travel kit with essentials",
      "Gradually acclimate pets to car travel"
    ],
    warnings: [
      "Cars can reach deadly temperatures in minutes",
      "Unrestrained pets can become projectiles in accidents",
      "Never let pets ride with their head out the window"
    ],
    species: "both",
    priority: "high"
  },
  {
    title: "Hot Weather Safety",
    category: "weather",
    description: "Protecting pets from heat-related dangers",
    tips: [
      "Provide plenty of fresh water",
      "Limit exercise during hot hours",
      "Provide shade and cool areas",
      "Never leave pets in parked cars",
      "Watch for signs of overheating",
      "Consider cooling mats or vests"
    ],
    warnings: [
      "Pavement can burn paw pads",
      "Brachycephalic breeds are at higher risk",
      "Heatstroke can be fatal"
    ],
    species: "both",
    priority: "high"
  },
  {
    title: "Cold Weather Safety",
    category: "weather",
    description: "Keeping pets warm and safe in winter",
    tips: [
      "Limit time outdoors in extreme cold",
      "Provide warm, dry shelter",
      "Consider pet sweaters for small/short-haired pets",
      "Check paws for ice and salt damage",
      "Increase food intake for outdoor pets",
      "Ensure water doesn't freeze"
    ],
    warnings: [
      "Antifreeze is toxic and has a sweet taste",
      "Ice can cut paw pads",
      "Hypothermia can occur quickly"
    ],
    species: "both",
    priority: "medium"
  },
  {
    title: "Outdoor Safety",
    category: "outdoor",
    description: "Safe practices for outdoor activities",
    tips: [
      "Keep pets on leash in unfamiliar areas",
      "Ensure ID tags and microchips are current",
      "Check for ticks after outdoor activities",
      "Avoid areas with wildlife",
      "Bring water for longer outings",
      "Know your pet's limits"
    ],
    warnings: [
      "Wild animals can carry diseases",
      "Some plants and mushrooms are toxic",
      "Water sources may contain harmful bacteria"
    ],
    species: "both",
    priority: "medium"
  },
  {
    title: "Emergency Preparedness",
    category: "emergency",
    description: "Being prepared for disasters and emergencies",
    tips: [
      "Create a pet emergency kit",
      "Have evacuation plans that include pets",
      "Keep important documents in waterproof container",
      "Identify pet-friendly shelters or hotels",
      "Practice evacuation procedures",
      "Keep emergency contact list updated"
    ],
    warnings: [
      "Don't wait until last minute to evacuate",
      "Some emergency shelters don't accept pets",
      "Stress can cause pets to behave unpredictably"
    ],
    species: "both",
    priority: "medium"
  }
]

const emergencyKit = [
  { item: "Food and water (3-day supply)", category: "essentials" },
  { item: "Medications and medical records", category: "medical" },
  { item: "Collar with ID tags", category: "identification" },
  { item: "Leash and carrier", category: "transport" },
  { item: "Waste bags and litter", category: "sanitation" },
  { item: "First aid kit", category: "medical" },
  { item: "Comfort items (toys, blankets)", category: "comfort" },
  { item: "Recent photos of pets", category: "identification" },
  { item: "Emergency contact numbers", category: "communication" },
  { item: "Cash for emergency expenses", category: "essentials" }
]

const seasonalHazards = {
  spring: [
    "Blooming plants (many are toxic)",
    "Increased outdoor allergens",
    "Baby wildlife (protective parents)",
    "Garden chemicals and fertilizers"
  ],
  summer: [
    "Hot pavement and surfaces",
    "Swimming pool drowning risk",
    "Barbecue foods (toxic to pets)",
    "Fireworks and loud noises"
  ],
  fall: [
    "Antifreeze preparations",
    "Mushrooms in yards",
    "School supplies (some toxic)",
    "Halloween candy and decorations"
  ],
  winter: [
    "Ice melting chemicals",
    "Space heaters and fireplaces",
    "Holiday decorations and foods",
    "Frozen water sources"
  ]
}

export default function SafetyPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedPriority, setSelectedPriority] = useState("all")
  const [selectedSpecies, setSelectedSpecies] = useState("both")

  const filteredTips = safetyTips.filter(tip => {
    const matchesSearch = tip.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tip.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tip.tips.some(t => t.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === "all" || tip.category === selectedCategory
    const matchesPriority = selectedPriority === "all" || tip.priority === selectedPriority
    const matchesSpecies = selectedSpecies === "both" || tip.species === selectedSpecies || tip.species === "both"
    return matchesSearch && matchesCategory && matchesPriority && matchesSpecies
  })

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800"
      case "medium": return "bg-yellow-100 text-yellow-800"
      case "low": return "bg-green-100 text-green-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const categories = Array.from(new Set(safetyTips.map(tip => tip.category)))

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-green-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Pet Safety
              <span className="block text-blue-300">& Protection Guide</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-green-100">
              Comprehensive safety measures to keep your pets protected in every situation
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search safety tips..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                <select
                  value={selectedSpecies}
                  onChange={(e) => setSelectedSpecies(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="both">All Pets</option>
                  <option value="dogs">Dogs Only</option>
                  <option value="cats">Cats Only</option>
                </select>
                <select
                  value={selectedPriority}
                  onChange={(e) => setSelectedPriority(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="all">All Priorities</option>
                  <option value="high">High Priority</option>
                  <option value="medium">Medium Priority</option>
                  <option value="low">Low Priority</option>
                </select>
                <Button
                  variant={selectedCategory === "all" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory("all")}
                >
                  All Categories
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className="capitalize"
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="safety-tips" className="mb-8">
          <TabsList className="grid w-full grid-cols-3 max-w-lg mx-auto">
            <TabsTrigger value="safety-tips">Safety Tips</TabsTrigger>
            <TabsTrigger value="emergency-kit">Emergency Kit</TabsTrigger>
            <TabsTrigger value="seasonal">Seasonal Hazards</TabsTrigger>
          </TabsList>

          <TabsContent value="safety-tips" className="mt-8">
            <div className="grid lg:grid-cols-2 gap-6">
              {filteredTips.map((tip, index) => (
                <motion.div
                  key={tip.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-xl flex items-center">
                            <Shield className="w-5 h-5 mr-2 text-blue-600" />
                            {tip.title}
                          </CardTitle>
                          <CardDescription className="mt-2">{tip.description}</CardDescription>
                        </div>
                        <div className="flex flex-col gap-2">
                          <Badge className={getPriorityColor(tip.priority)}>
                            {tip.priority} priority
                          </Badge>
                          <Badge variant="outline" className="capitalize">
                            {tip.species === "both" ? "Dogs & Cats" : tip.species}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-sm text-green-700 mb-2 flex items-center">
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Safety Tips:
                        </h4>
                        <ul className="text-sm text-green-600 space-y-1">
                          {tip.tips.map((t, idx) => (
                            <li key={idx} className="flex items-start">
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2 mt-2"></div>
                              {t}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-sm text-red-700 mb-2 flex items-center">
                          <AlertTriangle className="w-4 h-4 mr-1" />
                          Important Warnings:
                        </h4>
                        <ul className="text-sm text-red-600 space-y-1">
                          {tip.warnings.map((warning, idx) => (
                            <li key={idx} className="flex items-start">
                              <AlertTriangle className="w-3 h-3 mr-2 mt-0.5 flex-shrink-0" />
                              {warning}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="emergency-kit" className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-center">Pet Emergency Kit Checklist</CardTitle>
                <CardDescription className="text-center">
                  Essential items to keep ready for emergencies and disasters
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {emergencyKit.map((item, index) => (
                    <motion.div
                      key={item.item}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="flex items-start p-3 bg-blue-50 border border-blue-200 rounded-lg"
                    >
                      <CheckCircle className="w-4 h-4 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-blue-800">{item.item}</p>
                        <Badge variant="outline" className="text-xs mt-1 capitalize">
                          {item.category}
                        </Badge>
                      </div>
                    </motion.div>
                  ))}
                </div>
                <Alert className="mt-6 border-blue-200 bg-blue-50">
                  <Shield className="h-4 w-4" />
                  <AlertTitle>Emergency Preparedness</AlertTitle>
                  <AlertDescription>
                    Store your emergency kit in a waterproof container and check/update supplies every 6 months. 
                    Make sure all family members know where the kit is located.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="seasonal" className="mt-8">
            <div className="grid lg:grid-cols-2 gap-6">
              {Object.entries(seasonalHazards).map(([season, hazards], index) => (
                <motion.div
                  key={season}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle className="text-xl flex items-center capitalize">
                        {season === "spring" && <Heart className="w-5 h-5 mr-2 text-green-600" />}
                        {season === "summer" && <Sun className="w-5 h-5 mr-2 text-yellow-600" />}
                        {season === "fall" && <MapPin className="w-5 h-5 mr-2 text-orange-600" />}
                        {season === "winter" && <Snowflake className="w-5 h-5 mr-2 text-blue-600" />}
                        {season} Hazards
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {hazards.map((hazard, idx) => (
                          <li key={idx} className="flex items-start">
                            <AlertTriangle className="w-4 h-4 text-orange-600 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700">{hazard}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Quick Safety Reminders */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="text-2xl text-center">Quick Safety Reminders</CardTitle>
            <CardDescription className="text-center">
              Essential safety practices every pet owner should remember
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                {
                  title: "Identification",
                  description: "Keep ID tags and microchips current",
                  icon: Eye
                },
                {
                  title: "Supervision",
                  description: "Never leave pets unattended in dangerous situations",
                  icon: Shield
                },
                {
                  title: "Temperature",
                  description: "Monitor for signs of overheating or hypothermia",
                  icon: Thermometer
                },
                {
                  title: "Emergency Plan",
                  description: "Have a plan and practice it regularly",
                  icon: Zap
                }
              ].map((reminder, index) => (
                <motion.div
                  key={reminder.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <reminder.icon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">{reminder.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{reminder.description}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
