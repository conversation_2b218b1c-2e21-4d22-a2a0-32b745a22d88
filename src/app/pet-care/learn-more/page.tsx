"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Book, 
  PawPrint, 
  Heart, 
  Apple, 
  Home, 
  Bath, 
  Activity,
  Download,
  ArrowLeft,
  Share2
} from "lucide-react"
import Link from "next/link"

const GUIDE_SECTIONS = [
  {
    id: "getting-started",
    title: "Getting Started",
    content: `
      # Getting Started with Pet Care

      Welcome to our comprehensive pet care guide! This section covers all the basics you need to know as a new pet owner.

      ## Essential Supplies
      - Food and water bowls
      - Species-appropriate food
      - Collar and ID tag
      - Leash (for dogs)
      - Appropriate housing/bedding
      - Grooming supplies

      ## Daily Care Routine
      - Fresh water availability
      - Regular feeding schedule
      - Exercise and playtime
      - Bathroom breaks
      - Basic grooming

      ## First Week Tips
      1. Create a consistent schedule
      2. Set up a comfortable space
      3. Pet-proof your home
      4. Schedule a vet visit
      5. Start basic training
    `
  },
  {
    id: "nutrition",
    title: "Nutrition Guide",
    content: `
      # Pet Nutrition Guide

      Proper nutrition is key to your pet's health and happiness. Learn about dietary requirements and feeding best practices.

      ## Basic Nutrition Needs
      - Age-appropriate food
      - Balanced diet
      - Portion control
      - Fresh water
      - Healthy treats

      ## Feeding Schedule
      - Puppies/Kittens: 3-4 times daily
      - Adult dogs/cats: 1-2 times daily
      - Monitor weight and adjust portions
      
      ## Common Food Allergens
      - Dairy products
      - Wheat
      - Soy
      - Certain proteins
    `
  },
  {
    id: "health",
    title: "Health & Wellness",
    content: `
      # Health & Wellness Guide

      Regular health maintenance and preventive care are essential for a long, healthy life.

      ## Preventive Care
      - Regular vet check-ups
      - Vaccinations
      - Dental care
      - Parasite prevention
      - Exercise routine

      ## Common Health Issues
      - Signs of illness
      - Emergency situations
      - First aid basics
      - When to see a vet
    `
  },
  {
    id: "grooming",
    title: "Grooming Tips",
    content: `
      # Pet Grooming Guide

      Regular grooming keeps your pet clean, healthy, and comfortable.

      ## Basic Grooming Tools
      - Appropriate brush type
      - Nail clippers
      - Pet shampoo
      - Ear cleaner
      - Dental supplies

      ## Grooming Schedule
      - Daily brushing
      - Regular baths
      - Nail trimming
      - Teeth cleaning
      - Ear cleaning
    `
  }
]

export default function LearnMorePage() {
  const [activeSection, setActiveSection] = useState("getting-started")

  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-50 to-white py-12">
      <div className="max-w-6xl mx-auto px-4">
        <div className="mb-8">
          <Link href="/pet-care/guide">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Guide
            </Button>
          </Link>
          
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900">
              Detailed Pet Care Guide
            </h1>
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={() => window.print()}>
                <Download className="h-4 w-4 mr-2" />
                Save PDF
              </Button>
              <Button variant="outline">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar Navigation */}
          <Card className="lg:col-span-1">
            <CardContent className="p-4">
              <nav className="space-y-2">
                {GUIDE_SECTIONS.map((section) => (
                  <Button
                    key={section.id}
                    variant={activeSection === section.id ? "default" : "ghost"}
                    className="w-full justify-start"
                    onClick={() => setActiveSection(section.id)}
                  >
                    {section.title}
                  </Button>
                ))}
              </nav>
            </CardContent>
          </Card>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <Card>
              <CardContent className="p-6">
                <div className="prose max-w-none">
                  {/* You would use a markdown renderer here */}
                  <pre className="whitespace-pre-wrap font-sans">
                    {GUIDE_SECTIONS.find(s => s.id === activeSection)?.content}
                  </pre>
                </div>
              </CardContent>
            </Card>

            {/* Related Resources */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Need More Help?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">
                    Get personalized advice from our pet care experts
                  </p>
                  <Link href="/consultation">
                    <Button>Schedule Consultation</Button>
                  </Link>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Training Resources</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">
                    Access our library of training videos and tutorials
                  </p>
                  <Link href="/training">
                    <Button variant="outline">View Training Resources</Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
