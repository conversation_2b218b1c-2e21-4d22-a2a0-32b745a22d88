"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { 
  Heart, 
  Search, 
  Star, 
  Sparkles, 
  Crown, 
  Zap,
  <PERSON>lter,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>
} from "lucide-react"
import { motion } from "framer-motion"
import { toast } from "react-hot-toast"

interface PetName {
  name: string
  meaning?: string
  origin?: string
  popularity: "high" | "medium" | "low"
  category: string
}

const dogNames: Pet<PERSON><PERSON>[] = [
  { name: "Buddy", meaning: "Friend", origin: "English", popularity: "high", category: "classic" },
  { name: "Luna", meaning: "Moon", origin: "Latin", popularity: "high", category: "celestial" },
  { name: "<PERSON>", meaning: "Greatest", origin: "Latin", popularity: "high", category: "classic" },
  { name: "<PERSON>", meaning: "Beautiful", origin: "Italian", popularity: "high", category: "classic" },
  { name: "<PERSON>", meaning: "Free man", origin: "English", popularity: "high", category: "classic" },
  { name: "Lucy", meaning: "Light", origin: "Latin", popularity: "high", category: "classic" },
  { name: "Cooper", meaning: "Barrel maker", origin: "English", popularity: "medium", category: "occupational" },
  { name: "Daisy", meaning: "Day's eye", origin: "English", popularity: "medium", category: "nature" },
  { name: "Rocky", meaning: "Rock", origin: "English", popularity: "medium", category: "strong" },
  { name: "Molly", meaning: "Bitter", origin: "Irish", popularity: "medium", category: "classic" },
  { name: "Zeus", meaning: "God of sky", origin: "Greek", popularity: "medium", category: "mythological" },
  { name: "Athena", meaning: "Goddess of wisdom", origin: "Greek", popularity: "medium", category: "mythological" },
  { name: "Oreo", meaning: "Cookie brand", origin: "Modern", popularity: "medium", category: "food" },
  { name: "Peanut", meaning: "Small nut", origin: "English", popularity: "low", category: "food" },
  { name: "Shadow", meaning: "Dark area", origin: "English", popularity: "medium", category: "nature" }
]

const catNames: PetName[] = [
  { name: "Whiskers", meaning: "Cat's facial hair", origin: "English", popularity: "high", category: "classic" },
  { name: "Mittens", meaning: "Gloves", origin: "English", popularity: "high", category: "classic" },
  { name: "Oliver", meaning: "Olive tree", origin: "Latin", popularity: "high", category: "classic" },
  { name: "Luna", meaning: "Moon", origin: "Latin", popularity: "high", category: "celestial" },
  { name: "Simba", meaning: "Lion", origin: "Swahili", popularity: "medium", category: "strong" },
  { name: "Chloe", meaning: "Blooming", origin: "Greek", popularity: "medium", category: "nature" },
  { name: "Tiger", meaning: "Large cat", origin: "English", popularity: "medium", category: "strong" },
  { name: "Princess", meaning: "Royal daughter", origin: "English", popularity: "medium", category: "royal" },
  { name: "Smokey", meaning: "Gray colored", origin: "English", popularity: "medium", category: "color" },
  { name: "Felix", meaning: "Happy", origin: "Latin", popularity: "medium", category: "classic" },
  { name: "Nala", meaning: "Successful", origin: "African", popularity: "medium", category: "strong" },
  { name: "Ginger", meaning: "Spice", origin: "English", popularity: "low", category: "color" },
  { name: "Midnight", meaning: "12 AM", origin: "English", popularity: "low", category: "time" },
  { name: "Patches", meaning: "Small pieces", origin: "English", popularity: "low", category: "pattern" },
  { name: "Snowball", meaning: "Ball of snow", origin: "English", popularity: "low", category: "nature" }
]

const namingTips = [
  {
    title: "Keep it Short",
    description: "Choose names with 1-2 syllables for easier training and recognition.",
    icon: Zap
  },
  {
    title: "Avoid Confusion",
    description: "Don't pick names that sound like common commands (Kit sounds like 'sit').",
    icon: Star
  },
  {
    title: "Consider Personality",
    description: "Wait a few days to see your pet's personality before deciding.",
    icon: Heart
  },
  {
    title: "Test the Name",
    description: "Say the name out loud multiple times to ensure you like how it sounds.",
    icon: Sparkles
  },
  {
    title: "Think Long-term",
    description: "Choose a name you'll be comfortable using for 10-15 years.",
    icon: Crown
  }
]

export default function PetNamesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedSpecies, setSelectedSpecies] = useState("dogs")
  const [copiedName, setCopiedName] = useState<string | null>(null)

  const currentNames = selectedSpecies === "dogs" ? dogNames : catNames
  
  const filteredNames = currentNames.filter(name => {
    const matchesSearch = name.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         name.meaning?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         name.origin?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "all" || name.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const categories = Array.from(new Set(currentNames.map(name => name.category)))

  const getRandomName = () => {
    const randomName = currentNames[Math.floor(Math.random() * currentNames.length)]
    toast.success(`How about "${randomName.name}"? ${randomName.meaning ? `It means "${randomName.meaning}"` : ''}`)
  }

  const copyName = (name: string) => {
    navigator.clipboard.writeText(name)
    setCopiedName(name)
    toast.success(`"${name}" copied to clipboard!`)
    setTimeout(() => setCopiedName(null), 2000)
  }

  const getPopularityColor = (popularity: string) => {
    switch (popularity) {
      case "high": return "bg-red-100 text-red-800"
      case "medium": return "bg-yellow-100 text-yellow-800"
      case "low": return "bg-green-100 text-green-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Find the Perfect Name
              <span className="block text-yellow-300">for Your Pet</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Discover meaningful names that match your pet's personality and bring joy to your family
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                onClick={getRandomName}
                className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold"
              >
                <Shuffle className="w-5 h-5 mr-2" />
                Get Random Name
              </Button>
              <Button 
                size="lg" 
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                <Search className="w-5 h-5 mr-2" />
                Browse Names
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search names, meanings, or origins..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                <Button
                  variant={selectedCategory === "all" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory("all")}
                >
                  All Categories
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className="capitalize"
                  >
                    {category}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs value={selectedSpecies} onValueChange={setSelectedSpecies} className="mb-8">
          <TabsList className="grid w-full grid-cols-2 max-w-md mx-auto">
            <TabsTrigger value="dogs">Dog Names</TabsTrigger>
            <TabsTrigger value="cats">Cat Names</TabsTrigger>
          </TabsList>

          <TabsContent value={selectedSpecies} className="mt-8">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-12">
              {filteredNames.map((petName, index) => (
                <motion.div
                  key={petName.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <Card className="hover:shadow-lg transition-all duration-300 group cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-lg font-semibold group-hover:text-blue-600 transition-colors">
                          {petName.name}
                        </h3>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyName(petName.name)}
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          {copiedName === petName.name ? (
                            <Check className="w-4 h-4 text-green-600" />
                          ) : (
                            <Copy className="w-4 h-4" />
                          )}
                        </Button>
                      </div>
                      {petName.meaning && (
                        <p className="text-sm text-gray-600 mb-2">
                          <strong>Meaning:</strong> {petName.meaning}
                        </p>
                      )}
                      {petName.origin && (
                        <p className="text-sm text-gray-600 mb-3">
                          <strong>Origin:</strong> {petName.origin}
                        </p>
                      )}
                      <div className="flex items-center justify-between">
                        <Badge className={`capitalize ${getPopularityColor(petName.popularity)}`}>
                          {petName.popularity} popularity
                        </Badge>
                        <Badge variant="outline" className="capitalize">
                          {petName.category}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Naming Tips */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-2xl text-center">Pet Naming Tips</CardTitle>
            <CardDescription className="text-center">
              Expert advice for choosing the perfect name for your new companion
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {namingTips.map((tip, index) => (
                <motion.div
                  key={tip.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <tip.icon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">{tip.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{tip.description}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
