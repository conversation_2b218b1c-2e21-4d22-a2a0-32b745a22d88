"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { 
  Pill, 
  Search, 
  AlertTriangle, 
  Clock, 
  Heart,
  Shield,
  Thermometer,
  Stethoscope,
  Eye,
  Ear,
  Zap,
  Info
} from "lucide-react"
import { motion } from "framer-motion"

interface Medication {
  name: string
  genericName?: string
  category: string
  uses: string[]
  dosage: string
  frequency: string
  sideEffects: string[]
  warnings: string[]
  species: "dogs" | "cats" | "both"
}

const medications: Medication[] = [
  {
    name: "Rimadyl (Carprofen)",
    genericName: "Carprofen",
    category: "pain-relief",
    uses: ["Arthritis pain", "Post-surgery pain", "Inflammation"],
    dosage: "2mg per pound of body weight",
    frequency: "Twice daily with food",
    sideEffects: ["Vomiting", "Diarrhea", "Loss of appetite", "Lethargy"],
    warnings: ["Monitor liver function", "Not for pregnant pets", "Avoid with other NSAIDs"],
    species: "dogs"
  },
  {
    name: "Metacam (Meloxicam)",
    genericName: "Meloxicam",
    category: "pain-relief",
    uses: ["Arthritis", "Post-operative pain", "Inflammation"],
    dosage: "0.1mg per pound for dogs, 0.05mg per pound for cats",
    frequency: "Once daily",
    sideEffects: ["Stomach upset", "Kidney issues", "Liver problems"],
    warnings: ["Regular blood work required", "Not for dehydrated pets"],
    species: "both"
  },
  {
    name: "Benadryl (Diphenhydramine)",
    genericName: "Diphenhydramine",
    category: "antihistamine",
    uses: ["Allergic reactions", "Itching", "Motion sickness", "Anxiety"],
    dosage: "1mg per pound of body weight",
    frequency: "Every 8-12 hours",
    sideEffects: ["Drowsiness", "Dry mouth", "Urinary retention"],
    warnings: ["Check with vet for proper dosing", "Avoid extended use"],
    species: "both"
  },
  {
    name: "Frontline Plus",
    category: "flea-tick",
    uses: ["Flea prevention", "Tick prevention", "Flea eggs and larvae"],
    dosage: "Apply according to pet weight",
    frequency: "Monthly application",
    sideEffects: ["Skin irritation at application site", "Temporary hair loss"],
    warnings: ["Do not bathe for 48 hours after application", "Keep away from children"],
    species: "both"
  },
  {
    name: "Heartgard Plus",
    category: "heartworm",
    uses: ["Heartworm prevention", "Roundworm treatment", "Hookworm treatment"],
    dosage: "Based on pet weight range",
    frequency: "Monthly",
    sideEffects: ["Vomiting", "Diarrhea", "Loss of appetite"],
    warnings: ["Test for heartworm before starting", "Give year-round"],
    species: "dogs"
  }
]

const medicationCategories = [
  { id: "pain-relief", name: "Pain Relief", icon: Heart, color: "bg-red-100 text-red-800" },
  { id: "antihistamine", name: "Antihistamines", icon: Shield, color: "bg-blue-100 text-blue-800" },
  { id: "flea-tick", name: "Flea & Tick", icon: Zap, color: "bg-green-100 text-green-800" },
  { id: "heartworm", name: "Heartworm", icon: Heart, color: "bg-purple-100 text-purple-800" },
  { id: "antibiotics", name: "Antibiotics", icon: Pill, color: "bg-yellow-100 text-yellow-800" },
  { id: "eye-ear", name: "Eye & Ear", icon: Eye, color: "bg-indigo-100 text-indigo-800" }
]

const safetyTips = [
  {
    title: "Never Give Human Medications",
    description: "Many human medications are toxic to pets. Always consult your vet first.",
    icon: AlertTriangle,
    type: "warning"
  },
  {
    title: "Follow Dosage Instructions",
    description: "Always follow your veterinarian's dosage instructions exactly.",
    icon: Pill,
    type: "info"
  },
  {
    title: "Monitor for Side Effects",
    description: "Watch for any unusual behavior or symptoms after giving medication.",
    icon: Eye,
    type: "info"
  },
  {
    title: "Store Medications Safely",
    description: "Keep all medications out of reach of pets and children.",
    icon: Shield,
    type: "warning"
  },
  {
    title: "Complete the Full Course",
    description: "Finish all prescribed medications even if your pet seems better.",
    icon: Clock,
    type: "info"
  }
]

export default function MedicationsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedSpecies, setSelectedSpecies] = useState("both")

  const filteredMedications = medications.filter(med => {
    const matchesSearch = med.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         med.genericName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         med.uses.some(use => use.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === "all" || med.category === selectedCategory
    const matchesSpecies = selectedSpecies === "both" || med.species === selectedSpecies || med.species === "both"
    return matchesSearch && matchesCategory && matchesSpecies
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-green-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Pet Medications
              <span className="block text-green-300">Safety Guide</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Essential information about common pet medications, dosages, and safety guidelines
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Important Warning */}
        <Alert className="mb-8 border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Important Medical Disclaimer</AlertTitle>
          <AlertDescription>
            This information is for educational purposes only. Always consult with a licensed veterinarian 
            before giving any medication to your pet. Never give human medications to pets without veterinary approval.
          </AlertDescription>
        </Alert>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search medications, conditions, or uses..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                <select
                  value={selectedSpecies}
                  onChange={(e) => setSelectedSpecies(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="both">All Pets</option>
                  <option value="dogs">Dogs Only</option>
                  <option value="cats">Cats Only</option>
                </select>
                <Button
                  variant={selectedCategory === "all" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory("all")}
                >
                  All Categories
                </Button>
                {medicationCategories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Medications Grid */}
        <div className="grid lg:grid-cols-2 gap-6 mb-12">
          {filteredMedications.map((medication, index) => (
            <motion.div
              key={medication.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-xl">{medication.name}</CardTitle>
                      {medication.genericName && (
                        <CardDescription>Generic: {medication.genericName}</CardDescription>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Badge className={medicationCategories.find(cat => cat.id === medication.category)?.color}>
                        {medicationCategories.find(cat => cat.id === medication.category)?.name}
                      </Badge>
                      <Badge variant="outline" className="capitalize">
                        {medication.species === "both" ? "Dogs & Cats" : medication.species}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-sm text-gray-700 mb-2">Uses:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {medication.uses.map((use, idx) => (
                        <li key={idx} className="flex items-center">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></div>
                          {use}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-sm text-gray-700 mb-1">Dosage:</h4>
                      <p className="text-sm text-gray-600">{medication.dosage}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm text-gray-700 mb-1">Frequency:</h4>
                      <p className="text-sm text-gray-600">{medication.frequency}</p>
                    </div>
                  </div>

                  {medication.sideEffects.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-sm text-orange-700 mb-2">Common Side Effects:</h4>
                      <div className="flex flex-wrap gap-1">
                        {medication.sideEffects.map((effect, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {effect}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {medication.warnings.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-sm text-red-700 mb-2">Warnings:</h4>
                      <ul className="text-sm text-red-600 space-y-1">
                        {medication.warnings.map((warning, idx) => (
                          <li key={idx} className="flex items-start">
                            <AlertTriangle className="w-3 h-3 mr-2 mt-0.5 flex-shrink-0" />
                            {warning}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Safety Tips */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl text-center">Medication Safety Tips</CardTitle>
            <CardDescription className="text-center">
              Essential guidelines for safe medication administration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {safetyTips.map((tip, index) => (
                <motion.div
                  key={tip.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className={`p-4 rounded-lg border ${
                    tip.type === "warning" 
                      ? "border-red-200 bg-red-50" 
                      : "border-blue-200 bg-blue-50"
                  }`}
                >
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-3 ${
                    tip.type === "warning" 
                      ? "bg-red-100" 
                      : "bg-blue-100"
                  }`}>
                    <tip.icon className={`w-6 h-6 ${
                      tip.type === "warning" 
                        ? "text-red-600" 
                        : "text-blue-600"
                    }`} />
                  </div>
                  <h3 className="font-semibold mb-2">{tip.title}</h3>
                  <p className="text-sm text-gray-600">{tip.description}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
