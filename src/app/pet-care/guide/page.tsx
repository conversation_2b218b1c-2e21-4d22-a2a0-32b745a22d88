"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { 
  Download,
  Search,
  Book,
  PawPrint,
  Heart,
  Apple,
  Home,
  ActivitySquare,
  Bath,
  Calendar,
  Share2
} from "lucide-react"
import Link from "next/link"
import { toast } from "react-hot-toast"

interface GuideSection {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  content: string
}

const GUIDE_SECTIONS: GuideSection[] = [
  {
    id: "basics",
    title: "Pet Care Basics",
    description: "Essential information for new pet owners",
    icon: <PawPrint className="h-6 w-6" />,
    content: `
      # Getting Started with Pet Care
      
      Every pet needs basic care to stay healthy and happy. Here are the essentials:
      
      ## Daily Care Routine
      - Fresh water available at all times
      - Regular feeding schedule
      - Exercise and playtime
      - Bathroom breaks
      - Grooming basics
      
      ## Essential Supplies
      - Food and water bowls
      - Species-appropriate food
      - Collar and ID tag
      - Leash (for dogs)
      - Appropriate housing/bedding
      - Grooming supplies
      
      ## Health and Safety
      - Pet-proof your home
      - Find a veterinarian
      - Schedule regular check-ups
      - Keep vaccinations current
      - Learn basic first aid
    `
  },
  {
    id: "nutrition",
    title: "Nutrition Guide",
    description: "Proper feeding and dietary requirements",
    icon: <Apple className="h-6 w-6" />,
    content: "Detailed nutrition guide content..."
  },
  {
    id: "health",
    title: "Health & Wellness",
    description: "Preventive care and medical guidance",
    icon: <Heart className="h-6 w-6" />,
    content: "Health and wellness content..."
  },
  {
    id: "behavior",
    title: "Training & Behavior",
    description: "Training tips and behavioral guidance",
    icon: <ActivitySquare className="h-6 w-6" />,
    content: "Training and behavior content..."
  },
  {
    id: "grooming",
    title: "Grooming Tips",
    description: "Keeping your pet clean and healthy",
    icon: <Bath className="h-6 w-6" />,
    content: "Grooming guide content..."
  },
  {
    id: "environment",
    title: "Creating a Pet-Friendly Home",
    description: "Setting up your home for your pet",
    icon: <Home className="h-6 w-6" />,
    content: "Home setup guide content..."
  }
]

export default function PetCareGuidePage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedSection, setSelectedSection] = useState<GuideSection | null>(null)

  const filteredSections = GUIDE_SECTIONS.filter(section =>
    section.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    section.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleDownloadPDF = async () => {
    try {
      const response = await fetch("/api/pet-care/download-guide", {
        method: "GET",
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement("a")
        a.href = url
        a.download = "pet-care-guide.pdf"
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        toast.error("Failed to download guide")
      }
    } catch (error) {
      console.error("Error downloading guide:", error)
      toast.error("Something went wrong")
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-50 to-white py-12">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Complete Pet Care Guide
            </h1>
            <p className="text-lg text-gray-600">
              Your comprehensive resource for pet care information
            </p>
          </div>
          <Button
            onClick={handleDownloadPDF}
            size="lg"
            className="bg-green-600 hover:bg-green-700"
          >
            <Download className="h-4 w-4 mr-2" />
            Download PDF Guide
          </Button>
        </div>

        <div className="mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              type="search"
              placeholder="Search guide topics..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        {selectedSection ? (
          <div className="space-y-6">
            <Button
              variant="ghost"
              onClick={() => setSelectedSection(null)}
              className="mb-4"
            >
              ← Back to All Topics
            </Button>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-2xl">
                  {selectedSection.icon}
                  <span className="ml-3">{selectedSection.title}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  {/* You would use a markdown renderer here */}
                  <pre className="whitespace-pre-wrap font-sans">
                    {selectedSection.content}
                  </pre>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredSections.map((section) => (
              <Card
                key={section.id}
                className="cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => setSelectedSection(section)}
              >
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="bg-green-100 p-3 rounded-lg">
                      {section.icon}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {section.title}
                      </h3>
                      <p className="text-gray-600">
                        {section.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        <div className="mt-12">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Need Professional Advice?
                  </h3>
                  <p className="text-gray-600">
                    Consult with our pet care experts for personalized guidance
                  </p>
                </div>
                <Link href="/consultation">
                  <Button>Schedule Consultation</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
