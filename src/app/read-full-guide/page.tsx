"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  BookOpen, 
  Clock, 
  User, 
  Star,
  ChevronRight,
  ChevronLeft,
  Download,
  Share2,
  Bookmark,
  ArrowRight,
  CheckCircle,
  Heart,
  Lightbulb,
  AlertTriangle
} from "lucide-react"
import { motion } from "framer-motion"
import Link from "next/link"

const guideChapters = [
  {
    id: 1,
    title: "Getting Started with Your New Pet",
    sections: [
      "Preparing Your Home",
      "Essential Supplies Checklist",
      "First Day Expectations",
      "Creating a Safe Environment"
    ],
    readTime: "15 min",
    completed: true
  },
  {
    id: 2,
    title: "Health and Wellness Fundamentals",
    sections: [
      "Finding a Veterinarian",
      "Vaccination Schedules",
      "Preventive Care",
      "Signs of Illness"
    ],
    readTime: "20 min",
    completed: true
  },
  {
    id: 3,
    title: "Nutrition and Feeding Guidelines",
    sections: [
      "Choosing the Right Food",
      "Feeding Schedules",
      "Portion Control",
      "Special Dietary Needs"
    ],
    readTime: "18 min",
    completed: false
  },
  {
    id: 4,
    title: "Training and Behavior",
    sections: [
      "Basic Commands",
      "House Training",
      "Socialization",
      "Problem Behaviors"
    ],
    readTime: "25 min",
    completed: false
  },
  {
    id: 5,
    title: "Exercise and Enrichment",
    sections: [
      "Daily Exercise Needs",
      "Mental Stimulation",
      "Toys and Activities",
      "Seasonal Considerations"
    ],
    readTime: "12 min",
    completed: false
  },
  {
    id: 6,
    title: "Grooming and Hygiene",
    sections: [
      "Regular Grooming Routine",
      "Nail Care",
      "Dental Health",
      "Professional Grooming"
    ],
    readTime: "16 min",
    completed: false
  },
  {
    id: 7,
    title: "Emergency Preparedness",
    sections: [
      "First Aid Basics",
      "Emergency Kit",
      "When to Call the Vet",
      "Disaster Planning"
    ],
    readTime: "22 min",
    completed: false
  },
  {
    id: 8,
    title: "Senior Pet Care",
    sections: [
      "Age-Related Changes",
      "Comfort Measures",
      "Medical Monitoring",
      "Quality of Life"
    ],
    readTime: "14 min",
    completed: false
  }
]

const currentChapter = {
  id: 3,
  title: "Nutrition and Feeding Guidelines",
  content: `
# Chapter 3: Nutrition and Feeding Guidelines

Proper nutrition is the foundation of your pet's health and wellbeing. This comprehensive chapter will guide you through everything you need to know about feeding your pet correctly.

## 3.1 Choosing the Right Food

### Understanding Pet Food Labels
When selecting food for your pet, understanding how to read labels is crucial. Look for:

- **AAFCO Statement**: Ensures the food meets nutritional standards
- **Life Stage Appropriateness**: Puppy/kitten, adult, or senior formulations
- **Ingredient Quality**: First ingredient should be a named protein source
- **Guaranteed Analysis**: Minimum protein and fat, maximum fiber and moisture

### Types of Pet Food

**Dry Food (Kibble)**
- Convenient and cost-effective
- Helps maintain dental health
- Long shelf life
- Choose high-quality brands with real meat as first ingredient

**Wet Food (Canned)**
- Higher moisture content
- Often more palatable
- Good for pets who don't drink enough water
- More expensive per serving

**Raw Diets**
- Requires careful planning and veterinary guidance
- Risk of bacterial contamination
- Not recommended for immunocompromised pets
- Consult with a veterinary nutritionist

## 3.2 Feeding Schedules

### Puppies and Kittens (Under 1 Year)
- **8-12 weeks**: 4 meals per day
- **3-6 months**: 3 meals per day
- **6-12 months**: 2 meals per day
- Free feeding not recommended

### Adult Pets (1-7 Years)
- **Dogs**: 2 meals per day, 12 hours apart
- **Cats**: 2-3 smaller meals per day
- Consistent timing helps with digestion and house training

### Senior Pets (7+ Years)
- May benefit from smaller, more frequent meals
- Easier to digest foods
- Monitor weight closely
- Adjust portions based on activity level

## 3.3 Portion Control

### Determining the Right Amount
- Follow feeding guidelines on pet food packaging as a starting point
- Adjust based on your pet's:
  - Age and life stage
  - Activity level
  - Body condition
  - Health status

### Body Condition Scoring
Learn to assess your pet's body condition:

1. **Underweight**: Ribs easily felt, visible waist, no fat cover
2. **Ideal**: Ribs felt with slight pressure, waist visible, minimal fat cover
3. **Overweight**: Ribs difficult to feel, waist barely visible, fat deposits
4. **Obese**: Ribs not felt, no waist, extensive fat deposits

### Measuring Food
- Use a proper measuring cup, not a coffee mug
- Level measurements for accuracy
- Weigh food for most precise portions
- Account for treats in daily calorie intake

## 3.4 Special Dietary Needs

### Food Allergies and Sensitivities
Common signs include:
- Itchy skin or excessive scratching
- Digestive upset (vomiting, diarrhea)
- Ear infections
- Poor coat quality

**Management strategies:**
- Elimination diet under veterinary supervision
- Limited ingredient diets
- Novel protein sources
- Hypoallergenic formulations

### Medical Conditions Requiring Special Diets
- **Kidney Disease**: Reduced protein and phosphorus
- **Diabetes**: High fiber, consistent carbohydrates
- **Heart Disease**: Reduced sodium
- **Obesity**: Calorie-restricted, high fiber
- **Digestive Issues**: Easily digestible ingredients

### Life Stage Considerations
- **Pregnant/Nursing**: Increased calories and nutrients
- **Working Dogs**: Higher protein and fat
- **Indoor Cats**: Lower calorie, hairball control
- **Large Breed Puppies**: Controlled calcium and phosphorus

## Key Takeaways

✅ **Choose high-quality food** appropriate for your pet's life stage
✅ **Establish consistent feeding schedules** to aid digestion
✅ **Monitor body condition** and adjust portions accordingly
✅ **Consult your veterinarian** for special dietary needs
✅ **Transition foods gradually** over 7-10 days
✅ **Provide fresh water** at all times

## Common Mistakes to Avoid

❌ Free feeding (leaving food out all day)
❌ Feeding table scraps regularly
❌ Sudden diet changes
❌ Ignoring weight gain or loss
❌ Using food as the primary reward
❌ Not reading ingredient labels

---

*Remember: Every pet is unique. These guidelines provide a foundation, but always consult with your veterinarian for personalized nutrition advice.*
  `,
  author: "Dr. Sarah Johnson, DVM",
  lastUpdated: "December 2024",
  readTime: "18 min"
}

export default function ReadFullGuidePage() {
  const [currentChapterId, setCurrentChapterId] = useState(3)
  const [bookmarked, setBookmarked] = useState(false)
  
  const completedChapters = guideChapters.filter(ch => ch.completed).length
  const progressPercentage = (completedChapters / guideChapters.length) * 100

  const nextChapter = guideChapters.find(ch => ch.id === currentChapterId + 1)
  const prevChapter = guideChapters.find(ch => ch.id === currentChapterId - 1)

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white py-12">
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid lg:grid-cols-4 gap-8">
          {/* Table of Contents Sidebar */}
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            className="lg:col-span-1"
          >
            <div className="sticky top-8 space-y-6">
              {/* Progress */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Your Progress</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Completed</span>
                      <span>{completedChapters}/{guideChapters.length}</span>
                    </div>
                    <Progress value={progressPercentage} className="h-2" />
                    <p className="text-xs text-gray-600">
                      {Math.round(progressPercentage)}% complete
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Table of Contents */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Table of Contents</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="space-y-1">
                    {guideChapters.map((chapter) => (
                      <button
                        key={chapter.id}
                        onClick={() => setCurrentChapterId(chapter.id)}
                        className={`w-full text-left p-3 hover:bg-gray-50 transition-colors ${
                          currentChapterId === chapter.id ? "bg-blue-50 border-r-2 border-blue-500" : ""
                        }`}
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 ${
                            chapter.completed 
                              ? "bg-green-100 text-green-600" 
                              : currentChapterId === chapter.id
                              ? "bg-blue-100 text-blue-600"
                              : "bg-gray-100 text-gray-400"
                          }`}>
                            {chapter.completed ? (
                              <CheckCircle className="h-3 w-3" />
                            ) : (
                              <span className="text-xs font-medium">{chapter.id}</span>
                            )}
                          </div>
                          <div className="flex-1">
                            <h4 className={`text-sm font-medium ${
                              currentChapterId === chapter.id ? "text-blue-900" : "text-gray-900"
                            }`}>
                              {chapter.title}
                            </h4>
                            <div className="flex items-center space-x-2 mt-1">
                              <Clock className="h-3 w-3 text-gray-400" />
                              <span className="text-xs text-gray-500">{chapter.readTime}</span>
                            </div>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <Download className="h-4 w-4 mr-2" />
                    Download PDF
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Share2 className="h-4 w-4 mr-2" />
                    Share Guide
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => setBookmarked(!bookmarked)}
                  >
                    <Bookmark className={`h-4 w-4 mr-2 ${bookmarked ? "fill-current" : ""}`} />
                    {bookmarked ? "Bookmarked" : "Bookmark"}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </motion.div>

          {/* Main Content */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-3"
          >
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <Badge variant="secondary" className="mb-2">
                      Chapter {currentChapter.id}
                    </Badge>
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                      {currentChapter.title}
                    </h1>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        {currentChapter.author}
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {currentChapter.readTime}
                      </div>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 mr-1 text-yellow-400 fill-current" />
                        4.9 rating
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">Last updated</p>
                    <p className="text-sm font-medium">{currentChapter.lastUpdated}</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Chapter Content */}
                <div className="prose prose-lg max-w-none">
                  <div 
                    className="space-y-6 text-gray-700 leading-relaxed"
                    dangerouslySetInnerHTML={{ 
                      __html: currentChapter.content
                        .replace(/\n/g, '<br>')
                        .replace(/# (.*)/g, '<h1 class="text-2xl font-bold text-gray-900 mt-8 mb-4">$1</h1>')
                        .replace(/## (.*)/g, '<h2 class="text-xl font-semibold text-gray-900 mt-6 mb-3">$1</h2>')
                        .replace(/### (.*)/g, '<h3 class="text-lg font-medium text-gray-900 mt-4 mb-2">$1</h3>')
                        .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')
                        .replace(/- (.*)/g, '<li class="ml-4">$1</li>')
                        .replace(/✅ (.*)/g, '<div class="flex items-start space-x-2 p-3 bg-green-50 rounded-lg my-2"><div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"><svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg></div><span class="text-sm text-green-800">$1</span></div>')
                        .replace(/❌ (.*)/g, '<div class="flex items-start space-x-2 p-3 bg-red-50 rounded-lg my-2"><div class="w-5 h-5 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5"><svg class="w-3 h-3 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></div><span class="text-sm text-red-800">$1</span></div>')
                    }}
                  />
                </div>

                {/* Navigation */}
                <div className="flex justify-between items-center mt-12 pt-8 border-t border-gray-200">
                  {prevChapter ? (
                    <Button 
                      variant="outline" 
                      onClick={() => setCurrentChapterId(prevChapter.id)}
                      className="flex items-center"
                    >
                      <ChevronLeft className="h-4 w-4 mr-2" />
                      Previous: {prevChapter.title}
                    </Button>
                  ) : (
                    <div />
                  )}

                  {nextChapter ? (
                    <Button 
                      onClick={() => setCurrentChapterId(nextChapter.id)}
                      className="flex items-center"
                    >
                      Next: {nextChapter.title}
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Button>
                  ) : (
                    <Button asChild>
                      <Link href="/contact-expert">
                        Contact Expert
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Link>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Related Resources */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
              className="mt-8"
            >
              <Card>
                <CardHeader>
                  <CardTitle>Related Resources</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-3 gap-4">
                    <Button asChild variant="outline" className="h-auto p-4 flex-col space-y-2">
                      <Link href="/contact-expert">
                        <Lightbulb className="h-6 w-6" />
                        <span className="text-sm">Ask an Expert</span>
                      </Link>
                    </Button>
                    <Button asChild variant="outline" className="h-auto p-4 flex-col space-y-2">
                      <Link href="/schedule-consultation">
                        <Heart className="h-6 w-6" />
                        <span className="text-sm">Schedule Consultation</span>
                      </Link>
                    </Button>
                    <Button asChild variant="outline" className="h-auto p-4 flex-col space-y-2">
                      <Link href="/download-pdf">
                        <Download className="h-6 w-6" />
                        <span className="text-sm">Download PDF</span>
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
