"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Search,
  Star,
  Heart,
  Filter,
  Cat,
  Home,
  Users,
  Activity,
  Brush,
  Volume2
} from "lucide-react"
import { motion } from "framer-motion"

interface CatBreed {
  name: string
  origin: string
  size: "Small" | "Medium" | "Large"
  lifespan: string
  temperament: string[]
  description: string
  characteristics: {
    affection: number
    playfulness: number
    energyLevel: number
    grooming: number
    shedding: number
    kidFriendly: number
    petFriendly: number
    vocality: number
  }
  image: string
  colors: string[]
  weight: string
  goodFor: string[]
}

const catBreeds: CatBreed[] = [
  {
    name: "Persian",
    origin: "Iran (Persia)",
    size: "Medium",
    lifespan: "12-17 years",
    temperament: ["Gentle", "Quiet", "Sweet"],
    description: "Known for their long, luxurious coat and sweet personality, Persians are calm, gentle cats that prefer a serene environment.",
    characteristics: {
      affection: 5,
      playfulness: 2,
      energyLevel: 2,
      grooming: 5,
      shedding: 5,
      kidFriendly: 4,
      petFriendly: 3,
      vocality: 1
    },
    image: "/api/placeholder/300/200",
    colors: ["White", "Black", "Blue", "Cream", "Red", "Silver"],
    weight: "7-12 lbs",
    goodFor: ["Quiet homes", "Indoor living", "Seniors"]
  },
  {
    name: "Maine Coon",
    origin: "United States",
    size: "Large",
    lifespan: "13-14 years",
    temperament: ["Friendly", "Intelligent", "Playful"],
    description: "One of the largest domestic cat breeds, Maine Coons are known for their dog-like personality and impressive size.",
    characteristics: {
      affection: 4,
      playfulness: 4,
      energyLevel: 3,
      grooming: 4,
      shedding: 4,
      kidFriendly: 5,
      petFriendly: 5,
      vocality: 3
    },
    image: "/api/placeholder/300/200",
    colors: ["Brown Tabby", "Silver", "Red", "Blue", "Cream", "White"],
    weight: "10-25 lbs",
    goodFor: ["Families", "Multi-pet homes", "Active households"]
  },
  {
    name: "Siamese",
    origin: "Thailand",
    size: "Medium",
    lifespan: "12-15 years",
    temperament: ["Vocal", "Social", "Intelligent"],
    description: "Siamese cats are known for their striking blue eyes, pointed coloration, and very vocal nature. They form strong bonds with their owners.",
    characteristics: {
      affection: 5,
      playfulness: 4,
      energyLevel: 4,
      grooming: 2,
      shedding: 2,
      kidFriendly: 4,
      petFriendly: 3,
      vocality: 5
    },
    image: "/api/placeholder/300/200",
    colors: ["Seal Point", "Chocolate Point", "Blue Point", "Lilac Point"],
    weight: "6-14 lbs",
    goodFor: ["Interactive owners", "Single-cat homes", "Experienced owners"]
  },
  {
    name: "British Shorthair",
    origin: "United Kingdom",
    size: "Medium",
    lifespan: "12-17 years",
    temperament: ["Calm", "Independent", "Loyal"],
    description: "With their round faces and plush coat, British Shorthairs are easygoing cats that adapt well to various living situations.",
    characteristics: {
      affection: 3,
      playfulness: 2,
      energyLevel: 2,
      grooming: 2,
      shedding: 3,
      kidFriendly: 4,
      petFriendly: 4,
      vocality: 1
    },
    image: "/api/placeholder/300/200",
    colors: ["Blue", "Black", "White", "Cream", "Silver", "Golden"],
    weight: "9-17 lbs",
    goodFor: ["Busy families", "Apartments", "First-time owners"]
  },
  {
    name: "Ragdoll",
    origin: "United States",
    size: "Large",
    lifespan: "13-15 years",
    temperament: ["Docile", "Calm", "Affectionate"],
    description: "Ragdolls are known for going limp when picked up, hence their name. They're gentle giants with beautiful blue eyes.",
    characteristics: {
      affection: 5,
      playfulness: 3,
      energyLevel: 2,
      grooming: 4,
      shedding: 4,
      kidFriendly: 5,
      petFriendly: 4,
      vocality: 2
    },
    image: "/api/placeholder/300/200",
    colors: ["Colorpoint", "Mitted", "Bicolor"],
    weight: "10-20 lbs",
    goodFor: ["Families with children", "Indoor living", "Lap cats"]
  },
  {
    name: "Russian Blue",
    origin: "Russia",
    size: "Medium",
    lifespan: "15-20 years",
    temperament: ["Reserved", "Intelligent", "Loyal"],
    description: "Russian Blues have a distinctive blue-gray coat and green eyes. They're known for being somewhat shy but very loyal to their families.",
    characteristics: {
      affection: 3,
      playfulness: 3,
      energyLevel: 3,
      grooming: 2,
      shedding: 3,
      kidFriendly: 3,
      petFriendly: 2,
      vocality: 2
    },
    image: "/api/placeholder/300/200",
    colors: ["Blue"],
    weight: "7-12 lbs",
    goodFor: ["Quiet homes", "Single owners", "Routine-oriented households"]
  }
]

export default function CatBreedsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedSize, setSelectedSize] = useState<string>("all")
  const [sortBy, setSortBy] = useState<string>("name")

  const filteredBreeds = catBreeds.filter(breed => {
    const matchesSearch = breed.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         breed.temperament.some(trait => trait.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesSize = selectedSize === "all" || breed.size === selectedSize
    return matchesSearch && matchesSize
  }).sort((a, b) => {
    switch (sortBy) {
      case "name":
        return a.name.localeCompare(b.name)
      case "size":
        const sizeOrder = { "Small": 1, "Medium": 2, "Large": 3 }
        return sizeOrder[a.size] - sizeOrder[b.size]
      case "lifespan":
        return parseInt(a.lifespan) - parseInt(b.lifespan)
      default:
        return 0
    }
  })

  const renderStars = (rating: number) => (
    <div className="flex">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`w-4 h-4 ${
            star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
          }`}
        />
      ))}
    </div>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 to-pink-600 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Cat Breed
              <span className="block text-purple-300">Guide</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-pink-100">
              Discover the perfect feline companion for your lifestyle
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Search and Filter Section */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search cat breeds..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <select
                  value={selectedSize}
                  onChange={(e) => setSelectedSize(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="all">All Sizes</option>
                  <option value="Small">Small</option>
                  <option value="Medium">Medium</option>
                  <option value="Large">Large</option>
                </select>
                
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="name">Sort by Name</option>
                  <option value="size">Sort by Size</option>
                  <option value="lifespan">Sort by Lifespan</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Breeds Grid */}
        <div className="grid lg:grid-cols-2 gap-8">
          {filteredBreeds.map((breed, index) => (
            <motion.div
              key={breed.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-2xl flex items-center">
                        <Cat className="w-6 h-6 mr-2 text-purple-600" />
                        {breed.name}
                      </CardTitle>
                      <CardDescription className="text-lg">
                        Origin: {breed.origin}
                      </CardDescription>
                    </div>
                    <Badge className="bg-purple-100 text-purple-800">
                      {breed.size}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  {/* Basic Info */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Weight:</span>
                      <div className="text-gray-600">{breed.weight}</div>
                    </div>
                    <div>
                      <span className="font-medium">Lifespan:</span>
                      <div className="text-gray-600">{breed.lifespan}</div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-gray-600">{breed.description}</p>

                  {/* Temperament */}
                  <div>
                    <h4 className="font-semibold mb-2">Temperament</h4>
                    <div className="flex flex-wrap gap-1">
                      {breed.temperament.map((trait) => (
                        <Badge key={trait} variant="outline" className="text-xs">
                          {trait}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Characteristics */}
                  <div>
                    <h4 className="font-semibold mb-3">Characteristics</h4>
                    <div className="space-y-2 text-sm">
                      {[
                        { label: "Affection Level", value: breed.characteristics.affection, icon: Heart },
                        { label: "Playfulness", value: breed.characteristics.playfulness, icon: Activity },
                        { label: "Energy Level", value: breed.characteristics.energyLevel, icon: Activity },
                        { label: "Grooming Needs", value: breed.characteristics.grooming, icon: Brush },
                        { label: "Kid Friendly", value: breed.characteristics.kidFriendly, icon: Users },
                        { label: "Vocality", value: breed.characteristics.vocality, icon: Volume2 }
                      ].map((char) => (
                        <div key={char.label} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <char.icon className="w-4 h-4 mr-2 text-purple-600" />
                            <span>{char.label}</span>
                          </div>
                          {renderStars(char.value)}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Good For */}
                  <div>
                    <h4 className="font-semibold mb-2">Good For</h4>
                    <div className="flex flex-wrap gap-1">
                      {breed.goodFor.map((item) => (
                        <Badge key={item} className="bg-green-100 text-green-800 text-xs">
                          {item}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Colors */}
                  <div>
                    <h4 className="font-semibold mb-2">Common Colors</h4>
                    <div className="flex flex-wrap gap-1">
                      {breed.colors.map((color) => (
                        <Badge key={color} variant="outline" className="text-xs">
                          {color}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <Button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                    Find {breed.name} Cats for Adoption
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {filteredBreeds.length === 0 && (
          <div className="text-center py-12">
            <Cat className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No breeds found</h3>
            <p className="text-gray-500">Try adjusting your search criteria</p>
          </div>
        )}

        {/* Cat Care Tips */}
        <Card className="mt-12">
          <CardHeader>
            <CardTitle>Cat Care Essentials</CardTitle>
            <CardDescription>Important considerations for cat ownership</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-semibold mb-2 flex items-center">
                  <Home className="w-4 h-4 mr-2 text-purple-600" />
                  Indoor vs Outdoor
                </h4>
                <p className="text-sm text-gray-600">
                  Consider whether your chosen breed is suited for indoor-only living or needs outdoor access.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2 flex items-center">
                  <Brush className="w-4 h-4 mr-2 text-purple-600" />
                  Grooming Requirements
                </h4>
                <p className="text-sm text-gray-600">
                  Long-haired breeds require daily brushing, while short-haired cats need less maintenance.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2 flex items-center">
                  <Heart className="w-4 h-4 mr-2 text-purple-600" />
                  Health Considerations
                </h4>
                <p className="text-sm text-gray-600">
                  Research breed-specific health issues and ensure regular veterinary care.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
