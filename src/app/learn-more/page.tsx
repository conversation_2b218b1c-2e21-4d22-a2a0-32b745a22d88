"use client"

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "react-hot-toast"
import {
  Heart,
  PawPrint,
  Clock,
  DollarSign,
  FileText,
  CalendarCheck,
  Download,
  BookOpen,
  ArrowRight,
} from "lucide-react"
import Link from "next/link"

const ADOPTION_TOPICS = [
  {
    icon: Heart,
    title: "Why Adopt?",
    description: "Discover the benefits of pet adoption and how it saves lives.",
    color: "bg-pink-100 text-pink-600",
  },
  {
    icon: PawPrint,
    title: "Finding Your Match",
    description: "Tips for choosing the right pet for your lifestyle and family.",
    color: "bg-purple-100 text-purple-600",
  },
  {
    icon: Clock,
    title: "The Process",
    description: "Step-by-step guide to our adoption process and requirements.",
    color: "bg-blue-100 text-blue-600",
  },
  {
    icon: DollarSign,
    title: "Costs & Resources",
    description: "Understanding adoption fees and ongoing pet care expenses.",
    color: "bg-green-100 text-green-600",
  },
]

export default function LearnMorePage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white py-12">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Complete Pet Adoption Guide
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Everything you need to know about adopting and caring for your new pet.
            We're here to support you every step of the way.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {ADOPTION_TOPICS.map((topic) => (
            <Card key={topic.title} className="relative overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className={`p-2 rounded-lg ${topic.color}`}>
                    <topic.icon className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-medium mb-2">{topic.title}</h3>
                    <p className="text-sm text-gray-600">{topic.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {/* Comprehensive Guide */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-primary" />
                Complete Adoption Guide
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-gray-600">
                Our comprehensive guide covers everything from preparation to
                post-adoption care. Download it now or read it online.
              </p>
              <div className="grid sm:grid-cols-2 gap-4">
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={async () => {
                    try {
                      const response = await fetch("/api/pet-care/download-guide")
                      if (response.ok) {
                        const blob = await response.blob()
                        const url = window.URL.createObjectURL(blob)
                        const a = document.createElement("a")
                        a.href = url
                        a.download = "pet-care-guide.pdf"
                        document.body.appendChild(a)
                        a.click()
                        window.URL.revokeObjectURL(url)
                        document.body.removeChild(a)
                      } else {
                        toast.error("Failed to download guide")
                      }
                    } catch (error) {
                      console.error("Error downloading guide:", error)
                      toast.error("Something went wrong")
                    }
                  }}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
                <Button className="w-full" asChild>
                  <Link href="/pet-care/guide">
                    Read Full Guide
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Schedule Consultation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CalendarCheck className="h-5 w-5 text-primary" />
                Expert Consultation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-gray-600">
                Have specific questions? Schedule a one-on-one consultation with
                our adoption experts for personalized guidance.
              </p>
              <Button className="w-full" asChild>
                <Link href="/consultation">
                  Schedule Consultation
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Resources Section */}
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-primary" />
                Additional Resources
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid sm:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium mb-3">For New Pet Parents</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• First-time pet owner checklist</li>
                    <li>• Pet-proofing your home</li>
                    <li>• Essential supplies guide</li>
                    <li>• Training resources</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-medium mb-3">Health & Care</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• Veterinary care basics</li>
                    <li>• Nutrition guidelines</li>
                    <li>• Exercise requirements</li>
                    <li>• Common health issues</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to Start Your Journey?
          </h2>
          <p className="text-gray-600 mb-6">
            Browse our available pets or contact us with any questions.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/pets">
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                Find Your Perfect Match
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline">
                Contact Us
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
