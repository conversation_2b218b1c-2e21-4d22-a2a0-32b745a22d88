"use client"

import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Keyboard, 
  Eye, 
  Mic, 
  Monitor, 
  Mouse, 
  MessageSquare, 
  Mail 
} from "lucide-react"

export default function AccessibilityPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-50 to-white py-12">
      <div className="max-w-4xl mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Accessibility Commitment
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            We're committed to ensuring our website is accessible to everyone. Learn about our accessibility features and how to get support if you need it.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Keyboard className="h-5 w-5 mr-2" />
                Keyboard Navigation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-gray-600">
                <li>• Full keyboard navigation support</li>
                <li>• Visible focus indicators</li>
                <li>• Skip navigation links</li>
                <li>• Logical tab order</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                Visual Assistance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-gray-600">
                <li>• High contrast mode support</li>
                <li>• Text resizing options</li>
                <li>• Alt text for all images</li>
                <li>• Clear visual hierarchy</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mic className="h-5 w-5 mr-2" />
                Screen Reader Support
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-gray-600">
                <li>• ARIA labels and landmarks</li>
                <li>• Semantic HTML structure</li>
                <li>• Form field descriptions</li>
                <li>• Error announcements</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Monitor className="h-5 w-5 mr-2" />
                Device Compatibility
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-gray-600">
                <li>• Responsive design</li>
                <li>• Touch-friendly targets</li>
                <li>• Consistent navigation</li>
                <li>• Cross-browser support</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Standards & Guidelines */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Standards & Guidelines</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">
              Our website is built following the Web Content Accessibility Guidelines (WCAG) 2.1 Level AA standards. We regularly test and update our site to ensure compliance with these guidelines.
            </p>
            <div className="space-y-2">
              <h3 className="font-semibold text-gray-900">Key Principles:</h3>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                <li>Perceivable - Information must be presentable to users in ways they can perceive</li>
                <li>Operable - User interface components must be operable</li>
                <li>Understandable - Information and operation must be understandable</li>
                <li>Robust - Content must be robust enough to be interpreted by a variety of user agents</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Assistance & Support */}
        <Card>
          <CardHeader>
            <CardTitle>Need Assistance?</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <p className="text-gray-600">
              If you experience any difficulty using our website or need assistance with the adoption process, please don't hesitate to contact us.
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Button variant="outline" className="w-full" size="lg">
                <MessageSquare className="h-4 w-4 mr-2" />
                Live Chat Support
              </Button>
              <Button variant="outline" className="w-full" size="lg">
                <Mail className="h-4 w-4 mr-2" />
                Email Support
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
