"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowLeft, 
  Calendar, 
  User, 
  Clock,
  Heart,
  MessageCircle,
  Share2,
  BookOpen,
  Eye,
  ThumbsUp,
  Facebook,
  Twitter,
  Linkedin,
  Link as LinkIcon
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { toast } from "react-hot-toast"

interface BlogPost {
  id: string
  title: string
  excerpt: string
  content: string
  author: string
  publishedAt: string
  category: string
  tags: string[]
  readTime: number
  views: number
  likes: number
  comments: number
  featured: boolean
  imageUrl?: string
}

// Mock blog posts data (in a real app, this would come from an API)
const BLOG_POSTS: BlogPost[] = [
  {
    id: "1",
    title: "The Complete Guide to Adopting Your First Pet",
    excerpt: "Everything you need to know before bringing your new furry friend home, from preparation to the first few weeks of adjustment.",
    content: `
# The Complete Guide to Adopting Your First Pet

Adopting your first pet is an exciting and life-changing decision. Whether you're considering a dog, cat, or another companion animal, proper preparation is key to ensuring a smooth transition for both you and your new furry friend.

## Before You Adopt

### Assess Your Lifestyle
Before visiting a shelter, honestly evaluate your lifestyle:
- **Time commitment**: Pets require daily care, exercise, and attention
- **Living situation**: Consider your home size, yard space, and rental restrictions
- **Financial responsibility**: Budget for food, veterinary care, supplies, and emergencies
- **Family dynamics**: Ensure all family members are on board

### Research Different Pets
Different animals have varying needs:
- **Dogs**: Require daily walks, training, and social interaction
- **Cats**: More independent but still need attention and enrichment
- **Small animals**: Rabbits, guinea pigs, and birds have specific care requirements

## The Adoption Process

### Visit Local Shelters
- Spend time with potential pets
- Ask about their history and temperament
- Consider older animals - they often make wonderful companions

### Prepare Your Home
- Pet-proof your space by removing hazards
- Set up a designated area with food, water, and bedding
- Purchase essential supplies before bringing your pet home

## First Few Weeks

The adjustment period is crucial:
- **Establish routines** for feeding, exercise, and sleep
- **Be patient** as your pet adapts to their new environment
- **Schedule a veterinary checkup** within the first week

## Building a Bond

- Use positive reinforcement training methods
- Provide mental stimulation through toys and activities
- Be consistent with rules and boundaries

Remember, adopting a pet is a long-term commitment that can bring immense joy and companionship to your life. Take your time to make the right choice for both you and your future pet.

## Resources

- Contact local shelters for adoption events
- Consult with veterinarians about pet care
- Join pet owner communities for support and advice

Adopting a pet is one of the most rewarding experiences you can have. With proper preparation and commitment, you'll be giving a deserving animal a loving home while gaining a loyal companion for years to come.
    `,
    author: "Dr. Sarah Johnson",
    publishedAt: "2024-01-15",
    category: "Adoption Tips",
    tags: ["first-time-owners", "preparation", "adoption"],
    readTime: 8,
    views: 2450,
    likes: 89,
    comments: 23,
    featured: true,
    imageUrl: "/images/blog/first-pet-guide.jpg"
  },
  {
    id: "2",
    title: "Understanding Your Cat's Body Language",
    excerpt: "Learn to decode what your feline friend is trying to tell you through their posture, tail movements, and vocalizations.",
    content: `
# Understanding Your Cat's Body Language

Cats are masters of non-verbal communication. Learning to read their body language will help you better understand your feline friend's needs, emotions, and intentions.

## Tail Positions

Your cat's tail is like a mood ring:
- **Upright with slight curve**: Happy and confident
- **Puffed up**: Scared or agitated
- **Low or tucked**: Anxious or submissive
- **Twitching rapidly**: Excited or frustrated

## Ear Positions

Cat ears are incredibly expressive:
- **Forward and alert**: Interested and engaged
- **Flattened back**: Fearful or aggressive
- **Rotating**: Listening to different sounds

## Eye Communication

- **Slow blinking**: Sign of trust and affection
- **Dilated pupils**: Excited, scared, or overstimulated
- **Direct stare**: Can be challenging or threatening

## Body Postures

- **Arched back**: Can indicate fear or playfulness
- **Rolling over**: Trust and comfort
- **Crouched low**: Defensive or hunting mode

## Vocalizations

- **Purring**: Usually contentment, but can indicate pain
- **Meowing**: Communication specifically for humans
- **Hissing**: Warning or fear
- **Chirping**: Excitement, often when watching birds

Understanding these signals will strengthen your bond with your cat and help you respond appropriately to their needs.
    `,
    author: "Emily Rodriguez",
    publishedAt: "2024-01-12",
    category: "Pet Care",
    tags: ["cats", "behavior", "communication"],
    readTime: 6,
    views: 1890,
    likes: 67,
    comments: 15,
    featured: false,
    imageUrl: "/images/blog/cat-body-language.jpg"
  },
  {
    id: "3",
    title: "Success Story: Max's Journey from Shelter to Service Dog",
    excerpt: "Follow Max's incredible transformation from a scared shelter dog to a certified therapy animal helping children with autism.",
    content: `
# Success Story: Max's Journey from Shelter to Service Dog

When Max first arrived at our shelter, he was a trembling, 2-year-old German Shepherd mix who had been abandoned and showed signs of neglect. Today, he's a certified therapy dog working with children with autism. This is his incredible journey.

## The Beginning

Max came to us in rough shape:
- Underweight and malnourished
- Fearful of human contact
- Showed signs of previous trauma
- Needed extensive medical care

## The Healing Process

Our dedicated staff and volunteers worked tirelessly with Max:
- **Medical rehabilitation**: Proper nutrition and veterinary care
- **Behavioral therapy**: Slow, patient socialization
- **Trust building**: Consistent, gentle interactions
- **Basic training**: Foundation skills and confidence building

## Finding His Purpose

After months of rehabilitation, Max's gentle nature and intelligence caught the attention of Sarah, a trainer specializing in therapy animals. She saw potential in Max that others might have missed.

## Training for Service

Max's transformation was remarkable:
- **Advanced obedience training**: Perfect recall and impulse control
- **Specialized therapy training**: Learning to work with children
- **Certification process**: Rigorous testing and evaluation
- **Ongoing education**: Continuous skill development

## Making a Difference

Today, Max works at a local autism center where he:
- Provides comfort during therapy sessions
- Helps children develop social skills
- Offers emotional support during difficult moments
- Serves as a bridge for communication

## The Impact

Max's story demonstrates the incredible potential that exists in shelter animals. With patience, love, and proper training, even the most traumatized animals can become heroes in their communities.

His journey from a scared, abandoned dog to a certified therapy animal shows that every animal deserves a second chance. Max now touches the lives of dozens of children and families, proving that rescue animals often rescue us right back.

## Lessons Learned

- Never judge an animal by their past
- Patience and consistency can work miracles
- Every animal has unique potential
- The bond between humans and animals is powerful

Max's story continues to inspire our shelter staff and reminds us why we do this important work every day.
    `,
    author: "Michael Chen",
    publishedAt: "2024-01-10",
    category: "Success Stories",
    tags: ["success-story", "therapy-dog", "training"],
    readTime: 5,
    views: 3200,
    likes: 156,
    comments: 42,
    featured: true,
    imageUrl: "/images/blog/max-success-story.jpg"
  },
  {
    id: "4",
    title: "Essential Supplies for New Dog Owners",
    excerpt: "A comprehensive checklist of everything you'll need to welcome your new canine companion into your home.",
    content: `
# Essential Supplies for New Dog Owners

Bringing home a new dog is exciting, but proper preparation is essential for a smooth transition. Here's your complete checklist of supplies to have ready before your new companion arrives.

## Food and Water Essentials

### Food and Treats
- High-quality dog food appropriate for your dog's age and size
- Food and water bowls (stainless steel or ceramic preferred)
- Measuring cup for consistent portions
- Healthy training treats

### Feeding Schedule
- Establish regular meal times
- Follow feeding guidelines from the shelter or previous owner
- Gradually transition to new food if needed

## Comfort and Safety

### Bedding and Comfort
- Dog bed or crate pad
- Blankets for warmth and comfort
- Crate appropriate for your dog's size

### Safety Equipment
- Collar with ID tags
- Leash (6-foot standard leash recommended)
- Car safety harness or carrier
- Baby gates if needed

## Health and Grooming

### Grooming Supplies
- Dog brush appropriate for coat type
- Nail clippers
- Dog shampoo
- Toothbrush and dog toothpaste

### Health Monitoring
- First aid kit
- Thermometer
- Any prescribed medications

## Entertainment and Exercise

### Toys and Enrichment
- Variety of toys (chew toys, rope toys, balls)
- Puzzle toys for mental stimulation
- Kong or similar treat-dispensing toy

### Exercise Equipment
- Additional leashes for different activities
- Waste bags for cleanup
- Water bottle for walks

## Preparation Tips

- Set up a designated space before arrival
- Remove any hazardous items from reach
- Plan your first veterinary visit
- Research local dog parks and walking routes

Remember, every dog is different. Start with the basics and add items as you learn your dog's preferences and needs.
    `,
    author: "David Park",
    publishedAt: "2024-01-08",
    category: "Adoption Tips",
    tags: ["dogs", "supplies", "checklist"],
    readTime: 7,
    views: 1650,
    likes: 45,
    comments: 18,
    featured: false,
    imageUrl: "/images/blog/dog-supplies.jpg"
  },
  {
    id: "7",
    title: "Luna's Golden Years: A Senior Cat Success Story",
    excerpt: "How a 12-year-old cat found her perfect retirement home and proved that age is just a number.",
    content: `
# Luna's Golden Years: A Senior Cat Success Story

At 12 years old, Luna had spent most of her life in a loving home. When her elderly owner passed away, she found herself at our shelter, confused and grieving. Many potential adopters passed by her cage, drawn to the younger, more energetic cats. But Luna's story was far from over.

## A Difficult Beginning

Luna arrived at our shelter in a state of depression:
- She had lost her lifelong companion
- She was hesitant to eat or interact
- Potential adopters overlooked her due to her age
- She needed special care for mild arthritis

## The Perfect Match

After three months at the shelter, Margaret and Robert, a retired couple, visited our facility. They weren't looking for a kitten to chase around – they wanted a calm, gentle companion for their quiet home.

### Why Senior Pets Make Great Companions
- **Established personalities**: What you see is what you get
- **Lower energy needs**: Perfect for quieter households
- **Grateful nature**: Senior pets seem to understand they've been given a second chance
- **Immediate bonding**: Often more affectionate and appreciative

## Luna's New Life

The transformation was remarkable:
- Within days, Luna was purring and seeking attention
- She claimed the sunny spot by the living room window
- She became Margaret's constant companion during reading time
- Her arthritis improved with proper medication and a comfortable environment

## The Impact

Margaret and Robert say adopting Luna was one of the best decisions they've made:
- She provides companionship without being overwhelming
- Her gentle nature fits perfectly with their lifestyle
- She's brought joy and purpose to their retirement
- They've become advocates for senior pet adoption

## Lessons Learned

Luna's story teaches us that:
- Age is just a number when it comes to love
- Senior pets have so much to offer
- The right match is more important than age
- Every animal deserves a chance at happiness

Today, Luna spends her days basking in the sun, receiving gentle pets, and proving that the golden years can truly be golden when filled with love.

## Consider a Senior Pet

If you're thinking about adoption, don't overlook the senior animals. They may not have as many years ahead, but they'll fill those years with gratitude, love, and the kind of deep bond that only comes with wisdom and experience.
    `,
    author: "Michael Chen",
    publishedAt: "2024-01-20",
    category: "Success Stories",
    tags: ["senior-pets", "cats", "adoption"],
    readTime: 4,
    views: 2800,
    likes: 124,
    comments: 38,
    featured: true,
    imageUrl: "/images/blog/luna-senior-cat.jpg"
  }
]

export default function BlogPostPage() {
  const params = useParams()
  const router = useRouter()
  const [post, setPost] = useState<BlogPost | null>(null)
  const [loading, setLoading] = useState(true)
  const [liked, setLiked] = useState(false)
  const [likes, setLikes] = useState(0)

  useEffect(() => {
    const foundPost = BLOG_POSTS.find(p => p.id === params.id)
    if (foundPost) {
      setPost(foundPost)
      setLikes(foundPost.likes)
    }
    setLoading(false)
  }, [params.id])

  const handleLike = () => {
    if (!liked) {
      setLikes(prev => prev + 1)
      setLiked(true)
      toast.success("Thanks for liking this post!")
    }
  }

  const handleShare = (platform: string) => {
    if (!post) return
    
    const url = window.location.href
    const text = `Check out this article: ${post.title}`
    
    let shareUrl = ""
    
    switch (platform) {
      case "facebook":
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`
        break
      case "twitter":
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`
        break
      case "linkedin":
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`
        break
      case "copy":
        navigator.clipboard.writeText(url)
        toast.success("Link copied to clipboard!")
        return
    }
    
    if (shareUrl) {
      window.open(shareUrl, "_blank", "width=600,height=400")
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md mx-auto">
          <CardContent className="text-center py-12">
            <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Article Not Found</h3>
            <p className="text-gray-600 mb-6">
              The article you're looking for doesn't exist or has been removed.
            </p>
            <Link href="/blog">
              <Button>Back to Blog</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/blog" className="inline-flex items-center text-blue-600 hover:text-blue-800">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Blog
          </Link>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Article Header */}
          <Card className="mb-8">
            <CardContent className="p-8">
              <div className="mb-6">
                <Badge className="bg-blue-100 text-blue-800 mb-4">{post.category}</Badge>
                <h1 className="text-4xl font-bold text-gray-900 mb-4">{post.title}</h1>
                <p className="text-xl text-gray-600 mb-6">{post.excerpt}</p>
                
                <div className="flex items-center justify-between text-sm text-gray-500 mb-6">
                  <div className="flex items-center space-x-6">
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-2" />
                      {post.author}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      {formatDate(post.publishedAt)}
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2" />
                      {post.readTime} min read
                    </div>
                    <div className="flex items-center">
                      <Eye className="h-4 w-4 mr-2" />
                      {post.views} views
                    </div>
                  </div>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {post.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>

                {/* Social Actions */}
                <div className="flex items-center justify-between pt-6 border-t">
                  <div className="flex items-center space-x-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleLike}
                      className={liked ? "bg-red-50 border-red-200 text-red-600" : ""}
                    >
                      <Heart className={`h-4 w-4 mr-2 ${liked ? "fill-red-500 text-red-500" : ""}`} />
                      {likes}
                    </Button>
                    <div className="flex items-center text-sm text-gray-500">
                      <MessageCircle className="h-4 w-4 mr-1" />
                      {post.comments} comments
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500 mr-2">Share:</span>
                    <Button variant="outline" size="sm" onClick={() => handleShare("facebook")}>
                      <Facebook className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleShare("twitter")}>
                      <Twitter className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleShare("linkedin")}>
                      <Linkedin className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleShare("copy")}>
                      <LinkIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Article Content */}
          <Card>
            <CardContent className="p-8">
              <div className="prose prose-lg max-w-none">
                {post.content.split('\n').map((paragraph, index) => {
                  // Heading handling
                  if (paragraph.startsWith('# ')) {
                    return <h1 key={index} className="text-3xl font-bold mt-8 mb-4">{paragraph.slice(2)}</h1>
                  }
                  if (paragraph.startsWith('## ')) {
                    return <h2 key={index} className="text-2xl font-semibold mt-6 mb-3">{paragraph.slice(3)}</h2>
                  }
                  if (paragraph.startsWith('### ')) {
                    return <h3 key={index} className="text-xl font-semibold mt-4 mb-2">{paragraph.slice(4)}</h3>
                  }

                  // List items handling
                  if (paragraph.startsWith('- **')) {
                    const match = paragraph.match(/- \*\*(.*?)\*\*: (.*)/)
                    if (match) {
                      return (
                        <ul key={index} className="list-none pl-0 mb-4">
                          <li className="mb-2">
                            <strong>{match[1]}</strong>: {match[2]}
                          </li>
                        </ul>
                      )
                    }
                  }
                  if (paragraph.startsWith('- ')) {
                    // Group consecutive list items into a single ul
                    let currentIndex = index
                    const listItems = []
                    while (currentIndex < post.content.split('\n').length && 
                           post.content.split('\n')[currentIndex]?.startsWith('- ')) {
                      listItems.push(post.content.split('\n')[currentIndex].slice(2))
                      currentIndex++
                    }
                    return (
                      <ul key={index} className="list-none pl-0 mb-4">
                        {listItems.map((item, i) => (
                          <li key={i} className="mb-1">{item}</li>
                        ))}
                      </ul>
                    )
                  }

                  // Empty paragraph handling
                  if (paragraph.trim() === '') {
                    return <div key={index} className="h-4" aria-hidden="true" />
                  }

                  // Regular paragraph handling
                  return <p key={index} className="mb-4 leading-relaxed">{paragraph}</p>
                })}
              </div>
            </CardContent>
          </Card>

          {/* Related Articles */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Related Articles</CardTitle>
              <CardDescription>More articles you might enjoy</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                {BLOG_POSTS.filter(p => p.id !== post.id && p.category === post.category).slice(0, 2).map((relatedPost) => (
                  <Link key={relatedPost.id} href={`/blog/${relatedPost.id}`}>
                    <Card className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <Badge className="mb-2 text-xs">{relatedPost.category}</Badge>
                        <h4 className="font-semibold mb-2 hover:text-blue-600">{relatedPost.title}</h4>
                        <p className="text-sm text-gray-600 mb-3">{relatedPost.excerpt}</p>
                        <div className="flex items-center text-xs text-gray-500">
                          <Clock className="h-3 w-3 mr-1" />
                          {relatedPost.readTime} min read
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
