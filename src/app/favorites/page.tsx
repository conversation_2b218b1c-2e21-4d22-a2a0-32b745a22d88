"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { Heart, Search } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"

interface Pet {
  id: string
  name: string
  species: string
  breed: string
  age: number
  gender: string
  description: string
  photos: Array<{
    id: string
    url: string
    isPrimary: boolean
  }>
  organization: {
    name: string
    city: string
    state: string
  }
  _count: {
    applications: number
  }
}

interface Favorite {
  id: string
  pet: Pet
  createdAt: string
}

export default function FavoritesPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [favorites, setFavorites] = useState<Favorite[]>([])
  const [filteredFavorites, setFilteredFavorites] = useState<Favorite[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [speciesFilter, setSpeciesFilter] = useState("")

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
      return
    }
    if (status === "authenticated") {
      fetchFavorites()
    }
  }, [status, router])

  useEffect(() => {
    let filtered = favorites
    if (searchTerm) {
      filtered = filtered.filter(fav =>
        fav.pet.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fav.pet.breed.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fav.pet.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }
    if (speciesFilter) {
      filtered = filtered.filter(fav => fav.pet.species.toLowerCase() === speciesFilter.toLowerCase())
    }
    setFilteredFavorites(filtered)
  }, [favorites, searchTerm, speciesFilter])

  const fetchFavorites = async () => {
    try {
      const response = await fetch("/api/favorites")
      if (response.ok) {
        const data = await response.json()
        setFavorites(data.favorites || [])
      } else {
        toast.error("Failed to load favorites")
      }
    } catch (error) {
      console.error("Error fetching favorites:", error)
      toast.error("Failed to load favorites")
    } finally {
      setLoading(false)
    }
  }

  const removeFavorite = async (favoriteId: string, petName: string) => {
    try {
      const response = await fetch(`/api/favorites/${favoriteId}`, {
        method: "DELETE",
      })
      if (response.ok) {
        setFavorites(prev => prev.filter(fav => fav.id !== favoriteId))
        toast.success(`Removed ${petName} from favorites`)
      } else {
        toast.error("Failed to remove favorite")
      }
    } catch (error) {
      console.error("Error removing favorite:", error)
      toast.error("Failed to remove favorite")
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-8">
          <div className="h-12 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-96 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">My Favorite Pets</h1>

      {/* Search and Filter */}
      <div className="flex flex-wrap gap-4 mb-8">
        <div className="flex-1 min-w-[200px]">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search favorites..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        <div className="w-40">
          <select
            value={speciesFilter}
            onChange={(e) => setSpeciesFilter(e.target.value)}
            className="w-full p-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Species</option>
            <option value="dog">Dogs</option>
            <option value="cat">Cats</option>
            <option value="bird">Birds</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>

      {/* Favorites List */}
      {filteredFavorites.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Heart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {favorites.length === 0 ? "No Favorites Yet" : "No Matching Favorites"}
            </h3>
            <p className="text-gray-600 mb-6">
              {favorites.length === 0
                ? "Start browsing pets and save your favorites for easy access later."
                : "Try adjusting your search or filter criteria."
              }
            </p>
            {favorites.length === 0 && (
              <Link href="/pets">
                <Button>Browse Available Pets</Button>
              </Link>
            )}
          </CardContent>
        </Card>
      ) : (
        <div>
          {/* Results Count */}
          <div className="mb-6">
            <p className="text-gray-600">
              Showing {filteredFavorites.length} of {favorites.length} favorites
            </p>
          </div>

          {/* Favorites Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredFavorites.map((favorite) => (
              <Card key={favorite.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="relative aspect-square overflow-hidden">
                  {favorite.pet.photos.length > 0 ? (
                    <Image
                      src={favorite.pet.photos.find(p => p.isPrimary)?.url || favorite.pet.photos[0].url}
                      alt={favorite.pet.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-4xl">🐾</span>
                    </div>
                  )}
                  {/* Remove Favorite Button */}
                  <button
                    onClick={() => removeFavorite(favorite.id, favorite.pet.name)}
                    className="absolute top-2 right-2 p-2 bg-white rounded-full shadow-lg hover:bg-red-50 transition-colors"
                    aria-label="Remove from favorites"
                  >
                    <Heart className="h-5 w-5 text-red-500" fill="currentColor" />
                  </button>
                </div>

                <CardHeader>
                  <CardTitle className="text-xl">
                    <Link href={`/pets/${favorite.pet.id}`} className="hover:text-blue-600 transition-colors">
                      {favorite.pet.name}
                    </Link>
                  </CardTitle>
                  <CardDescription>
                    {favorite.pet.breed} • {favorite.pet.age} years old
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  <div className="flex flex-wrap gap-2 mb-4">
                    <Badge variant="secondary">{favorite.pet.species}</Badge>
                    <Badge variant="secondary">{favorite.pet.gender}</Badge>
                  </div>
                  <p className="text-gray-600 mb-4 line-clamp-2">
                    {favorite.pet.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {favorite.pet.organization.city}, {favorite.pet.organization.state}
                    </span>
                    <Link href={`/pets/${favorite.pet.id}`}>
                      <Button variant="outline" size="sm">View Details</Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
