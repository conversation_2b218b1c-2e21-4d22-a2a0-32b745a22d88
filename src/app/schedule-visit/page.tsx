"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { use<PERSON><PERSON><PERSON>, useSearch<PERSON>arams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Calendar, 
  Clock, 
  MapPin, 
  User, 
  Phone, 
  Mail,
  CheckCircle,
  AlertCircle,
  ArrowLeft
} from "lucide-react"
import Link from "next/link"
import { toast } from "react-hot-toast"

interface Pet {
  id: string
  name: string
  species: string
  breed: string
  photos: Array<{ url: string; isPrimary: boolean }>
  organization: {
    name: string
    address: string
    city: string
    state: string
    phone: string
    email: string
  }
}

interface VisitFormData {
  petId: string
  preferredDate: string
  preferredTime: string
  alternativeDate: string
  alternativeTime: string
  visitType: string
  numberOfVisitors: number
  specialRequests: string
  contactPhone: string
  contactEmail: string
}

const timeSlots = [
  "9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM",
  "1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM", "5:00 PM"
]

const visitTypes = [
  { value: "MEET_GREET", label: "Meet & Greet" },
  { value: "EXTENDED_VISIT", label: "Extended Visit (1 hour)" },
  { value: "FAMILY_VISIT", label: "Family Visit" },
  { value: "BEHAVIORAL_ASSESSMENT", label: "Behavioral Assessment" }
]

export default function ScheduleVisitPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const petId = searchParams.get("petId")
  
  const [pet, setPet] = useState<Pet | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [formData, setFormData] = useState<VisitFormData>({
    petId: petId || "",
    preferredDate: "",
    preferredTime: "",
    alternativeDate: "",
    alternativeTime: "",
    visitType: "MEET_GREET",
    numberOfVisitors: 1,
    specialRequests: "",
    contactPhone: "",
    contactEmail: ""
  })

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
      return
    }

    if (status === "authenticated") {
      if (petId) {
        fetchPet()
      } else {
        setLoading(false)
      }
      
      // Pre-fill contact info from session
      setFormData(prev => ({
        ...prev,
        contactEmail: session.user.email || "",
        contactPhone: session.user.phone || ""
      }))
    }
  }, [status, petId, session, router])

  const fetchPet = async () => {
    try {
      const response = await fetch(`/api/pets/${petId}`)
      if (response.ok) {
        const data = await response.json()
        setPet(data.pet)
      } else {
        toast.error("Pet not found")
        router.push("/pets")
      }
    } catch (error) {
      console.error("Error fetching pet:", error)
      toast.error("Failed to fetch pet information")
    } finally {
      setLoading(false)
    }
  }

  // Helper function to convert 12-hour time to 24-hour format
  const convertTo24Hour = (time12h: string) => {
    const [time, modifier] = time12h.split(' ')
    let [hours, minutes] = time.split(':')
    if (hours === '12') {
      hours = '00'
    }
    if (modifier === 'PM') {
      hours = (parseInt(hours, 10) + 12).toString()
    }
    return `${hours.padStart(2, '0')}:${minutes || '00'}`
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!session?.user) {
      toast.error("Please sign in to schedule a visit")
      return
    }

    if (!formData.preferredDate || !formData.preferredTime) {
      toast.error("Please select a preferred date and time")
      return
    }

    setSubmitting(true)
    
    try {
      // Combine date and time into a single datetime string
      const scheduledDateTime = new Date(`${formData.preferredDate}T${convertTo24Hour(formData.preferredTime)}:00`)
      
      // Determine duration based on visit type
      const getDuration = (visitType: string) => {
        switch (visitType) {
          case "EXTENDED_VISIT": return 60
          case "FAMILY_VISIT": return 90
          case "BEHAVIORAL_ASSESSMENT": return 120
          default: return 30 // MEET_GREET
        }
      }

      // Create appointment data matching the API schema
      const appointmentData = {
        type: "VISIT" as const,
        scheduledDate: scheduledDateTime.toISOString(),
        duration: getDuration(formData.visitType),
        location: pet ? `${pet.organization.name}, ${pet.organization.address}, ${pet.organization.city}, ${pet.organization.state}` : "TBD",
        notes: `Visit Type: ${formData.visitType}\nNumber of Visitors: ${formData.numberOfVisitors}\nContact Phone: ${formData.contactPhone}\nContact Email: ${formData.contactEmail}\nSpecial Requests: ${formData.specialRequests || 'None'}\nAlternative Date/Time: ${formData.alternativeDate && formData.alternativeTime ? `${formData.alternativeDate} at ${formData.alternativeTime}` : 'None'}`,
        petId: formData.petId || undefined
      }

      const response = await fetch("/api/appointments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(appointmentData),
      })

      if (response.ok) {
        toast.success("Visit scheduled successfully! We'll contact you to confirm.")
        router.push("/dashboard")
      } else {
        const error = await response.json()
        toast.error(error.error || "Failed to schedule visit")
      }
    } catch (error) {
      console.error("Error scheduling visit:", error)
      toast.error("Failed to schedule visit")
    } finally {
      setSubmitting(false)
    }
  }

  const handleInputChange = (field: keyof VisitFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getMinDate = () => {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    return tomorrow.toISOString().split('T')[0]
  }

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href={petId ? `/pets/${petId}` : "/pets"} className="inline-flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {petId ? "Back to Pet Details" : "Back to Pets"}
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Schedule a Visit
          </h1>
          <p className="text-gray-700 dark:text-gray-300">
            {pet ? `Schedule a visit to meet ${pet.name}` : "Schedule a visit to meet one of our pets"}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Pet Information */}
          {pet && (
            <div className="lg:col-span-1">
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-gray-100">Pet Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center mb-4">
                    <div className="w-32 h-32 bg-gray-200 dark:bg-gray-700 rounded-lg mx-auto mb-3 flex items-center justify-center">
                      {pet.photos.length > 0 ? (
                        <img
                          src={pet.photos.find(p => p.isPrimary)?.url || pet.photos[0].url}
                          alt={pet.name}
                          className="w-full h-full object-cover rounded-lg"
                        />
                      ) : (
                        <span className="text-4xl">🐾</span>
                      )}
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{pet.name}</h3>
                    <p className="text-gray-700 dark:text-gray-300">{pet.breed} • {pet.species}</p>
                  </div>

                  <div className="space-y-3 text-sm">
                    <div className="flex items-start">
                      <MapPin className="h-4 w-4 text-gray-500 dark:text-gray-400 mr-2 mt-0.5" />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">{pet.organization.name}</p>
                        <p className="text-gray-700 dark:text-gray-300">
                          {pet.organization.address}<br />
                          {pet.organization.city}, {pet.organization.state}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                      <span className="text-gray-700 dark:text-gray-300">{pet.organization.phone}</span>
                    </div>
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                      <span className="text-gray-700 dark:text-gray-300">{pet.organization.email}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Visit Form */}
          <div className={pet ? "lg:col-span-2" : "lg:col-span-3"}>
            <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
              <CardHeader>
                <CardTitle className="text-gray-900 dark:text-gray-100">Visit Details</CardTitle>
                <CardDescription className="text-gray-700 dark:text-gray-300">
                  Please provide your preferred visit times and any special requirements
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Visit Type */}
                  <div>
                    <Label htmlFor="visitType" className="text-gray-900 dark:text-gray-100">Visit Type</Label>
                    <select
                      id="visitType"
                      value={formData.visitType}
                      onChange={(e) => handleInputChange("visitType", e.target.value)}
                      className="w-full mt-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    >
                      {visitTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Preferred Date & Time */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="preferredDate" className="text-gray-900 dark:text-gray-100">Preferred Date *</Label>
                      <Input
                        id="preferredDate"
                        type="date"
                        min={getMinDate()}
                        value={formData.preferredDate}
                        onChange={(e) => handleInputChange("preferredDate", e.target.value)}
                        required
                        className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100"
                      />
                    </div>
                    <div>
                      <Label htmlFor="preferredTime" className="text-gray-900 dark:text-gray-100">Preferred Time *</Label>
                      <select
                        id="preferredTime"
                        value={formData.preferredTime}
                        onChange={(e) => handleInputChange("preferredTime", e.target.value)}
                        className="w-full mt-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                        required
                      >
                        <option value="">Select a time</option>
                        {timeSlots.map((time) => (
                          <option key={time} value={time}>
                            {time}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Alternative Date & Time */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="alternativeDate">Alternative Date</Label>
                      <Input
                        id="alternativeDate"
                        type="date"
                        min={getMinDate()}
                        value={formData.alternativeDate}
                        onChange={(e) => handleInputChange("alternativeDate", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="alternativeTime">Alternative Time</Label>
                      <select
                        id="alternativeTime"
                        value={formData.alternativeTime}
                        onChange={(e) => handleInputChange("alternativeTime", e.target.value)}
                        className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select a time</option>
                        {timeSlots.map((time) => (
                          <option key={time} value={time}>
                            {time}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Number of Visitors */}
                  <div>
                    <Label htmlFor="numberOfVisitors">Number of Visitors</Label>
                    <Input
                      id="numberOfVisitors"
                      type="number"
                      min="1"
                      max="6"
                      value={formData.numberOfVisitors}
                      onChange={(e) => handleInputChange("numberOfVisitors", parseInt(e.target.value) || 1)}
                    />
                  </div>

                  {/* Contact Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="contactEmail">Contact Email *</Label>
                      <Input
                        id="contactEmail"
                        type="email"
                        value={formData.contactEmail}
                        onChange={(e) => handleInputChange("contactEmail", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="contactPhone">Contact Phone *</Label>
                      <Input
                        id="contactPhone"
                        type="tel"
                        value={formData.contactPhone}
                        onChange={(e) => handleInputChange("contactPhone", e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  {/* Special Requests */}
                  <div>
                    <Label htmlFor="specialRequests">Special Requests or Questions</Label>
                    <Textarea
                      id="specialRequests"
                      value={formData.specialRequests}
                      onChange={(e) => handleInputChange("specialRequests", e.target.value)}
                      placeholder="Any special requirements, questions, or things we should know about your visit..."
                      rows={4}
                    />
                  </div>

                  {/* Important Information */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <AlertCircle className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                      <div className="text-sm text-blue-800">
                        <h4 className="font-semibold mb-2">Important Information:</h4>
                        <ul className="space-y-1">
                          <li>• Visits are subject to availability and confirmation</li>
                          <li>• Please arrive 10 minutes early for your appointment</li>
                          <li>• Bring a valid ID and any family members who will be involved in the adoption</li>
                          <li>• We'll contact you within 24 hours to confirm your visit</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end">
                    <Button type="submit" disabled={submitting} size="lg">
                      {submitting ? (
                        <span>
                          <Clock className="h-4 w-4 mr-2 animate-spin" />
                          Scheduling...
                        </span>
                      ) : (
                        <span>
                          <Calendar className="h-4 w-4 mr-2" />
                          Schedule Visit
                        </span>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}