"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Heart, Share2, MessageSquare } from "lucide-react"

interface SuccessStory {
  id: string
  petName: string
  adopterName: string
  adoptionDate: string
  story: string
  photos: Array<{
    id: string
    url: string
  }>
  likes: number
  comments: number
}

const SAMPLE_SUCCESS_STORIES: SuccessStory[] = [
  {
    id: "1",
    petName: "<PERSON>",
    adopterName: "<PERSON> & <PERSON>",
    adoptionDate: "2023-12-15",
    story: "<PERSON> was shy and anxious when we first met her, but with patience and love, she's blossomed into the most affectionate cat. She greets us at the door every day and has become best friends with our other cat. Adopting her was the best decision we've ever made!",
    photos: [
      {
        id: "luna1",
        url: "/images/success-stories/luna1.jpg"
      },
      {
        id: "luna2",
        url: "/images/success-stories/luna2.jpg"
      }
    ],
    likes: 156,
    comments: 23
  },
  {
    id: "2",
    pet<PERSON><PERSON>: "<PERSON>",
    adopter<PERSON><PERSON>: "David & Family",
    adoptionDate: "2023-11-20",
    story: "<PERSON> was our first family dog, and he's brought so much joy to our lives. He's amazing with our kids and has helped teach them responsibility and unconditional love. We can't imagine life without him now!",
    photos: [
      {
        id: "max1",
        url: "/images/success-stories/max1.jpg"
      }
    ],
    likes: 203,
    comments: 31
  }
]

export default function SuccessStoriesPage() {
  const [stories, setStories] = useState<SuccessStory[]>(SAMPLE_SUCCESS_STORIES)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStories = async () => {
      try {
        // In a real app, fetch from API
        setStories(SAMPLE_SUCCESS_STORIES)
      } catch (error) {
        console.error("Error fetching success stories:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchStories()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-pink-50 to-white py-12">
        <div className="max-w-6xl mx-auto px-4">
          <div className="animate-pulse space-y-8">
            {[1, 2, 3].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="h-48 bg-gray-200 rounded mb-4"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-50 to-white py-12">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Adoption Success Stories
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Read heartwarming stories from families who found their perfect companions through our adoption program.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {stories.map((story) => (
            <Card key={story.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="relative aspect-video rounded-lg overflow-hidden mb-6">
                  {story.photos[0] && (
                    <img
                      src={story.photos[0].url}
                      alt={`${story.petName} with their new family`}
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>

                <div className="space-y-4">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-1">
                      {story.petName}'s Story
                    </h3>
                    <p className="text-sm text-gray-600">
                      Adopted by {story.adopterName} on{" "}
                      {new Date(story.adoptionDate).toLocaleDateString("en-US", {
                        month: "long",
                        year: "numeric",
                      })}
                    </p>
                  </div>

                  <p className="text-gray-700">{story.story}</p>

                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center space-x-4">
                      <Button variant="ghost" size="sm" className="space-x-2">
                        <Heart className="h-4 w-4" />
                        <span>{story.likes}</span>
                      </Button>
                      <Button variant="ghost" size="sm" className="space-x-2">
                        <MessageSquare className="h-4 w-4" />
                        <span>{story.comments}</span>
                      </Button>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button size="lg" className="bg-green-600 hover:bg-green-700">
            Share Your Story
          </Button>
        </div>
      </div>
    </div>
  )
}
