"use client"

import { useState, useEffect } from "react"
import type { Metada<PERSON> } from "next"
import { motion, useScroll, useTransform, AnimatePresence } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Heart, Quote, ArrowRight, <PERSON>rk<PERSON> } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { cn } from "@/lib/utils"

// Sample adoption stories data
const adoptionStories = [
  {
    id: 1,
    petName: "Luna",
    petType: "Golden Retriever",
    adopter: "<PERSON> & <PERSON>",
    location: "Seattle, WA",
    adoptionDate: "March 2024",
    story: "<PERSON> came to us as a scared, abandoned puppy. Now she's the heart of our family, bringing joy to our children and teaching them about unconditional love. Every morning, she greets us with the biggest smile and reminds us that rescue dogs truly know they've been saved.",
    quote: "<PERSON> didn't just find a home with us - she completed our family. Her gentle spirit and boundless love have transformed our lives in ways we never imagined.",
    image: "/images/stories/luna.jpg",
    beforeImage: "/images/stories/luna-before.jpg",
    tags: ["Family Dog", "Great with Kids", "Therapy Dog"]
  },
  {
    id: 2,
    pet<PERSON>ame: "Whiskers",
    petType: "<PERSON>bby <PERSON>",
    adopter: "<PERSON> <PERSON>",
    location: "Austin, TX",
    adoptionDate: "January 2024",
    story: "Whiskers was a senior cat who had been returned to the shelter twice. At 8 years old, we thought he'd never find his forever home. <PERSON> saw past his age and gave him the loving retirement he deserved. Now he spends his days sunbathing and purring contentedly.",
    quote: "Age is just a number when it comes to love. Whiskers has brought such peace and companionship to my life. Senior pets have so much love to give.",
    image: "/images/stories/whiskers.jpg",
    beforeImage: "/images/stories/whiskers-before.jpg",
    tags: ["Senior Pet", "Lap Cat", "Quiet Companion"]
  },
  {
    id: 3,
    petName: "Buddy & Bella",
    petType: "Bonded Pair - Mixed Breed",
    adopter: "The Martinez Family",
    location: "Denver, CO",
    adoptionDate: "February 2024",
    story: "This bonded pair had been at the shelter for over a year. Many families wanted just one dog, but we knew they needed to stay together. The Martinez family opened their hearts and home to both, and now these best friends have the backyard adventures they always dreamed of.",
    quote: "We couldn't imagine separating them. Watching Buddy and Bella play together in our yard every day reminds us that some bonds are meant to last forever.",
    image: "/images/stories/buddy-bella.jpg",
    beforeImage: "/images/stories/buddy-bella-before.jpg",
    tags: ["Bonded Pair", "Active Dogs", "Best Friends"]
  },
  {
    id: 4,
    petName: "Shadow",
    petType: "Black Cat",
    adopter: "David Chen",
    location: "Portland, OR",
    adoptionDate: "December 2023",
    story: "Shadow waited 8 months for adoption - black cats often face longer shelter stays due to superstitions. David saw past the myths and found a loyal, affectionate companion who now follows him everywhere and has become his writing buddy.",
    quote: "Shadow is the most loving cat I've ever known. He sits by my desk while I work and seems to understand exactly when I need comfort. Black cats bring nothing but good luck.",
    image: "/images/stories/shadow.jpg",
    beforeImage: "/images/stories/shadow-before.jpg",
    tags: ["Black Cat", "Lap Cat", "Writing Companion"]
  },
  {
    id: 5,
    petName: "Rosie",
    petType: "Pit Bull Mix",
    adopter: "Jennifer & Tom Wilson",
    location: "Phoenix, AZ",
    adoptionDate: "April 2024",
    story: "Rosie was overlooked because of breed prejudice, but the Wilsons saw her gentle soul. She's now a certified therapy dog visiting hospitals and nursing homes, proving that love knows no breed. Her transformation from shelter dog to healer has inspired countless others.",
    quote: "Rosie has taught us that the size of a dog's heart isn't determined by their breed. She's touched so many lives and shown everyone that pit bulls are lovers, not fighters.",
    image: "/images/stories/rosie.jpg",
    beforeImage: "/images/stories/rosie-before.jpg",
    tags: ["Therapy Dog", "Gentle Giant", "Community Hero"]
  },
  {
    id: 6,
    petName: "Mittens",
    petType: "Special Needs Cat",
    adopter: "Rachel Thompson",
    location: "Boston, MA",
    adoptionDate: "May 2024",
    story: "Born with three legs, Mittens never let that slow her down. Rachel, a veterinary nurse, knew she could provide the special care Mittens needed. Now this little warrior climbs cat trees and chases toys just like any other cat, proving that different doesn't mean less capable.",
    quote: "Mittens has shown me that resilience comes in all forms. Her determination and joy for life inspire me every single day. She's perfect exactly as she is.",
    image: "/images/stories/mittens.jpg",
    beforeImage: "/images/stories/mittens-before.jpg",
    tags: ["Special Needs", "Resilient", "Inspiration"]
  }
]

export default function AdoptionStoriesPage() {
  const [selectedStory, setSelectedStory] = useState<number | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const { scrollYProgress } = useScroll()
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"])

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  return (
    <main className="min-h-screen bg-gradient-to-br from-orange-50 via-pink-50 to-purple-50 relative overflow-hidden" role="main" aria-label="Adoption Stories">
      {/* Animated paw print background */}
      <div className="absolute inset-0 opacity-5">
        <motion.div
          style={{ y: backgroundY }}
          className="absolute inset-0 bg-repeat"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 2 }}
        >
          {/* Paw prints pattern */}
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000' fill-opacity='0.1'%3E%3Cpath d='M30 25c-2.5 0-4.5-2-4.5-4.5S27.5 16 30 16s4.5 2 4.5 4.5S32.5 25 30 25zm-8-8c-1.5 0-2.5-1-2.5-2.5S20.5 12 22 12s2.5 1 2.5 2.5S23.5 17 22 17zm16 0c-1.5 0-2.5-1-2.5-2.5S36.5 12 38 12s2.5 1 2.5 2.5S39.5 17 38 17zm-16 8c-1.5 0-2.5-1-2.5-2.5S20.5 20 22 20s2.5 1 2.5 2.5S23.5 25 22 25zm16 0c-1.5 0-2.5-1-2.5-2.5S36.5 20 38 20s2.5 1 2.5 2.5S39.5 25 38 25z'/%3E%3C/g%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px'
          }} />
        </motion.div>
      </div>

      {/* Floating sparkles */}
      {isLoaded && (
        <div className="absolute inset-0 pointer-events-none">
          <FloatingSparkles />
        </div>
      )}

      {/* Hero Section */}
      <section className="relative py-20 px-4" aria-labelledby="hero-heading">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg" role="banner">
              <Heart className="h-5 w-5 text-red-500" aria-hidden="true" />
              <span className="text-sm font-medium text-gray-700">Stories of Love & Hope</span>
            </div>

            <h1 id="hero-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight">
              Adoption
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-600 block">
                Stories
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Every adoption is a love story waiting to be told. These heartwarming tales celebrate the magical bond between pets and their forever families.
            </p>

            <motion.div
              animate={{ y: [0, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="inline-block"
              aria-hidden="true"
            >
              <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                <Heart className="h-6 w-6 text-white" />
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Stories Grid */}
      <section className="relative py-16 px-4" aria-labelledby="stories-heading">
        <div className="container mx-auto">
          <h2 id="stories-heading" className="sr-only">Adoption Success Stories</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8" role="list" aria-label="Adoption stories collection">
            {adoptionStories.map((story, index) => (
              <StoryCard
                key={story.id}
                story={story}
                index={index}
                onClick={() => setSelectedStory(story.id)}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Bouncing Ball Animation */}
      <motion.div
        className="fixed bottom-8 right-8 z-10"
        animate={{
          y: [0, -20, 0],
          rotate: [0, 360]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full shadow-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-transform">
          <Heart className="h-8 w-8 text-white" />
        </div>
      </motion.div>

      {/* Story Modal */}
      <AnimatePresence>
        {selectedStory && (
          <StoryModal
            story={adoptionStories.find(s => s.id === selectedStory)!}
            onClose={() => setSelectedStory(null)}
          />
        )}
      </AnimatePresence>

      {/* Call to Action */}
      <section className="relative py-20 px-4">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <h2 className="text-3xl md:text-5xl font-bold text-gray-900">
              Ready to Write Your Own
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-600 block">
                Love Story?
              </span>
            </h2>

            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Every pet deserves a happy ending. Browse our available pets and start your own heartwarming adoption journey today.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/pets">
                <Button size="lg" className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white shadow-lg">
                  Find Your Perfect Pet
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/adoption-process">
                <Button size="lg" variant="outline" className="border-2 border-pink-500 text-pink-600 hover:bg-pink-50">
                  Learn About Adoption
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </main>
  )
}

// Story Card Component
interface StoryCardProps {
  story: typeof adoptionStories[0]
  index: number
  onClick: () => void
}

function StoryCard({ story, index, onClick }: StoryCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      whileHover={{ y: -5, scale: 1.02 }}
      className="cursor-pointer"
      onClick={onClick}
      role="listitem"
    >
      <Card
        className="overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-300"
        role="button"
        tabIndex={0}
        aria-label={`Read ${story.petName}'s adoption story`}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            onClick()
          }
        }}
      >
        <div className="relative aspect-[4/3] overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10" />
          <div className="absolute inset-0 bg-gradient-to-br from-pink-200 via-purple-200 to-blue-200 flex items-center justify-center">
            <div className="text-6xl opacity-50" aria-hidden="true">
              {story.petType.toLowerCase().includes('cat') ? '🐱' : '🐶'}
            </div>
          </div>
          <div className="absolute bottom-4 left-4 z-20">
            <h3 className="text-2xl font-bold text-white mb-1">{story.petName}</h3>
            <p className="text-white/90 text-sm">{story.petType}</p>
          </div>
        </div>

        <CardContent className="p-6 space-y-4">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Heart className="h-4 w-4 text-red-500" />
            <span>{story.adopter}</span>
            <span>•</span>
            <span>{story.location}</span>
          </div>

          <div className="relative">
            <Quote className="h-6 w-6 text-pink-500 mb-2" />
            <p className="text-gray-700 italic line-clamp-3">
              "{story.quote}"
            </p>
          </div>

          <div className="flex flex-wrap gap-2">
            {story.tags.map((tag) => (
              <span
                key={tag}
                className="px-3 py-1 bg-gradient-to-r from-pink-100 to-purple-100 text-pink-700 text-xs rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>

          <div className="flex items-center justify-between pt-2">
            <span className="text-sm text-gray-500">{story.adoptionDate}</span>
            <ArrowRight className="h-5 w-5 text-pink-500" />
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

// Story Modal Component
interface StoryModalProps {
  story: typeof adoptionStories[0]
  onClose: () => void
}

function StoryModal({ story, onClose }: StoryModalProps) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: "spring", damping: 25, stiffness: 300 }}
        className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="relative">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors"
            aria-label="Close modal"
          >
            <ArrowRight className="h-5 w-5 rotate-45" />
          </button>

          <div className="grid md:grid-cols-2 gap-0">
            <div className="relative aspect-square md:aspect-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-pink-200 via-purple-200 to-blue-200 flex items-center justify-center rounded-t-2xl md:rounded-l-2xl md:rounded-tr-none">
                <div className="text-8xl opacity-50">
                  {story.petType.toLowerCase().includes('cat') ? '🐱' : '🐶'}
                </div>
              </div>
            </div>

            <div className="p-8 space-y-6">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-2">{story.petName}</h2>
                <p className="text-xl text-gray-600">{story.petType}</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-gray-600">
                  <Heart className="h-5 w-5 text-red-500" />
                  <span className="font-medium">{story.adopter}</span>
                </div>
                <p className="text-gray-600">{story.location} • {story.adoptionDate}</p>
              </div>

              <div className="space-y-4">
                <div className="relative">
                  <Quote className="h-8 w-8 text-pink-500 mb-3" />
                  <blockquote className="text-lg text-gray-700 italic leading-relaxed">
                    "{story.quote}"
                  </blockquote>
                </div>

                <div className="prose prose-gray">
                  <p className="text-gray-700 leading-relaxed">{story.story}</p>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                {story.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-4 py-2 bg-gradient-to-r from-pink-100 to-purple-100 text-pink-700 text-sm rounded-full font-medium"
                  >
                    {tag}
                  </span>
                ))}
              </div>

              <div className="pt-4 border-t border-gray-200">
                <Link href="/pets">
                  <Button className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white">
                    Find Your Perfect Pet
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

// Floating Sparkles Component (Client-only to avoid hydration mismatch)
function FloatingSparkles() {
  const [sparkles, setSparkles] = useState<Array<{
    id: number
    x: number
    y: number
    delay: number
    duration: number
  }>>([])

  useEffect(() => {
    // Generate consistent positions only on client
    const sparkleData = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      delay: Math.random() * 2,
      duration: 3 + Math.random() * 2
    }))
    setSparkles(sparkleData)
  }, [])

  return (
    <>
      {sparkles.map((sparkle) => (
        <motion.div
          key={sparkle.id}
          className="absolute"
          initial={{
            x: sparkle.x,
            y: sparkle.y,
            opacity: 0
          }}
          animate={{
            y: [sparkle.y, sparkle.y - 20, sparkle.y],
            opacity: [0, 1, 0],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: sparkle.duration,
            repeat: Infinity,
            delay: sparkle.delay
          }}
        >
          <Sparkles className="h-4 w-4 text-yellow-400" />
        </motion.div>
      ))}
    </>
  )
}
