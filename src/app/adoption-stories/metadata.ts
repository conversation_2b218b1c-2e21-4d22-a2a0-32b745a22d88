import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Adoption Stories | PetAdopt - Heartwarming Tales of Love",
  description: "Read inspiring adoption stories from families who found their perfect companions. Discover heartwarming tales of rescued pets finding their forever homes and the joy they bring to their new families.",
  keywords: [
    "pet adoption stories",
    "rescue pet success stories", 
    "dog adoption testimonials",
    "cat adoption stories",
    "pet rescue stories",
    "adoption testimonials",
    "happy endings",
    "pet adoption inspiration",
    "rescue dog stories",
    "shelter cat stories"
  ],
  openGraph: {
    title: "Adoption Stories - Heartwarming Tales of Pet Adoption",
    description: "Read inspiring stories from families who found their perfect pet companions through adoption. Every story is a testament to the power of love and second chances.",
    type: "website",
    images: [
      {
        url: "/images/adoption-stories-og.jpg",
        width: 1200,
        height: 630,
        alt: "Happy families with their adopted pets"
      }
    ]
  },
  twitter: {
    card: "summary_large_image",
    title: "Adoption Stories - Heartwarming Pet Adoption Tales",
    description: "Discover inspiring stories of rescued pets finding their forever homes and the joy they bring to their families.",
  },
  alternates: {
    canonical: "/adoption-stories"
  }
}
