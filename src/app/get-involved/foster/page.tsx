"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Home,
  Heart,
  Clock,
  Users,
  PawPrint,
  Shield,
  Calendar,
  Phone,
  Mail,
  MapPin,
  CheckCircle,
  AlertCircle,
  Star,
  Baby,
  Dog,
  Cat
} from "lucide-react"
import { toast } from "react-hot-toast"

interface FosterForm {
  // Personal Information
  firstName: string
  lastName: string
  email: string
  phone: string
  dateOfBirth: string

  // Address Information
  address: string
  city: string
  state: string
  zipCode: string

  // Housing Information
  housingType: "house" | "apartment" | "condo" | "townhouse" | "other"
  ownRent: "own" | "rent"
  landlordPermission: boolean
  yardType: "fenced" | "unfenced" | "none"

  // Experience & Preferences
  petExperience: string
  currentPets: string
  fosterTypes: string[]
  agePreference: string[]
  sizePreference: string[]

  // Availability
  hoursAlone: string
  availability: string
  duration: string

  // Emergency Contact
  emergencyName: string
  emergencyPhone: string
  emergencyRelation: string

  // Additional Information
  motivation: string
  concerns: string
  additionalInfo: string

  // Agreements
  homeVisit: boolean
  backgroundCheck: boolean
  newsletter: boolean
}

const fosterTypes = [
  { id: "puppies", label: "Puppies (0-6 months)", icon: Baby, description: "Bottle feeding, socialization, basic training" },
  { id: "kittens", label: "Kittens (0-6 months)", icon: Baby, description: "Bottle feeding, litter training, socialization" },
  { id: "adult-dogs", label: "Adult Dogs", icon: Dog, description: "Behavioral training, exercise, companionship" },
  { id: "adult-cats", label: "Adult Cats", icon: Cat, description: "Socialization, medical recovery, companionship" },
  { id: "senior", label: "Senior Pets", icon: Heart, description: "Comfort care, medication management" },
  { id: "medical", label: "Medical Foster", icon: Shield, description: "Special medical needs, recovery care" },
  { id: "behavioral", label: "Behavioral Foster", icon: Star, description: "Training, socialization, confidence building" },
  { id: "emergency", label: "Emergency Foster", icon: AlertCircle, description: "Short-term emergency situations" }
]

const requirements = [
  "Must be 21 years or older",
  "Stable housing situation",
  "Ability to provide daily care",
  "Transportation for vet visits",
  "Home visit approval",
  "Background check clearance"
]

export default function FosterPage() {
  const [formData, setFormData] = useState<FosterForm>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    dateOfBirth: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    housingType: "house",
    ownRent: "own",
    landlordPermission: false,
    yardType: "none",
    petExperience: "",
    currentPets: "",
    fosterTypes: [],
    agePreference: [],
    sizePreference: [],
    hoursAlone: "",
    availability: "",
    duration: "",
    emergencyName: "",
    emergencyPhone: "",
    emergencyRelation: "",
    motivation: "",
    concerns: "",
    additionalInfo: "",
    homeVisit: false,
    backgroundCheck: false,
    newsletter: true
  })

  const [submitting, setSubmitting] = useState(false)
  const [step, setStep] = useState(1)

  const handleInputChange = (field: keyof FosterForm, value: string | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleFosterTypeToggle = (type: string) => {
    setFormData(prev => ({
      ...prev,
      fosterTypes: prev.fosterTypes.includes(type)
        ? prev.fosterTypes.filter(t => t !== type)
        : [...prev.fosterTypes, type]
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      const response = await fetch("/api/foster-applications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        toast.success("Foster application submitted successfully! We'll contact you within 48 hours.")
        // Reset form
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          phone: "",
          dateOfBirth: "",
          address: "",
          city: "",
          state: "",
          zipCode: "",
          housingType: "house",
          ownRent: "own",
          landlordPermission: false,
          yardType: "none",
          petExperience: "",
          currentPets: "",
          fosterTypes: [],
          agePreference: [],
          sizePreference: [],
          hoursAlone: "",
          availability: "",
          duration: "",
          emergencyName: "",
          emergencyPhone: "",
          emergencyRelation: "",
          motivation: "",
          concerns: "",
          additionalInfo: "",
          homeVisit: false,
          backgroundCheck: false,
          newsletter: true
        })
        setStep(1)
      } else {
        const error = await response.json()
        toast.error(error.error || "Failed to submit application")
      }
    } catch (error) {
      console.error("Error submitting application:", error)
      toast.error("Failed to submit application")
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Home className="h-12 w-12 text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              Become a Foster Family
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Open your heart and home to a pet in need. Foster families provide temporary care
            while pets await their forever homes, making the adoption process possible.
          </p>
        </div>

        <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Foster Types */}
          <div className="lg:col-span-3 mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">Foster Opportunities</CardTitle>
                <CardDescription>
                  Choose the type of fostering that fits your lifestyle and experience
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {fosterTypes.map((type) => {
                    const IconComponent = type.icon
                    const isSelected = formData.fosterTypes.includes(type.id)
                    return (
                      <button
                        key={type.id}
                        type="button"
                        onClick={() => handleFosterTypeToggle(type.id)}
                        className={`p-4 rounded-lg border text-left transition-all ${
                          isSelected
                            ? "border-blue-500 bg-blue-50 dark:bg-blue-900 shadow-md"
                            : "border-gray-300 hover:border-gray-400 hover:shadow-sm"
                        }`}
                      >
                        <div className="flex items-center mb-2">
                          <IconComponent className={`h-5 w-5 mr-2 ${
                            isSelected ? "text-blue-600" : "text-gray-600"
                          }`} />
                          <span className="font-medium text-sm">{type.label}</span>
                        </div>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {type.description}
                        </p>
                      </button>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Application Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">Foster Application</CardTitle>
                <CardDescription>
                  Step {step} of 4 - Please fill out all required information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Step 1: Personal Information */}
                  {step === 1 && (
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold">Personal Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="firstName">First Name *</Label>
                          <Input
                            id="firstName"
                            value={formData.firstName}
                            onChange={(e) => handleInputChange("firstName", e.target.value)}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="lastName">Last Name *</Label>
                          <Input
                            id="lastName"
                            value={formData.lastName}
                            onChange={(e) => handleInputChange("lastName", e.target.value)}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="email">Email Address *</Label>
                          <Input
                            id="email"
                            type="email"
                            value={formData.email}
                            onChange={(e) => handleInputChange("email", e.target.value)}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="phone">Phone Number *</Label>
                          <Input
                            id="phone"
                            type="tel"
                            value={formData.phone}
                            onChange={(e) => handleInputChange("phone", e.target.value)}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="dateOfBirth">Date of Birth *</Label>
                          <Input
                            id="dateOfBirth"
                            type="date"
                            value={formData.dateOfBirth}
                            onChange={(e) => handleInputChange("dateOfBirth", e.target.value)}
                            required
                          />
                        </div>
                      </div>

                      <h3 className="text-lg font-semibold">Address Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="md:col-span-2">
                          <Label htmlFor="address">Street Address *</Label>
                          <Input
                            id="address"
                            value={formData.address}
                            onChange={(e) => handleInputChange("address", e.target.value)}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="city">City *</Label>
                          <Input
                            id="city"
                            value={formData.city}
                            onChange={(e) => handleInputChange("city", e.target.value)}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="state">State *</Label>
                          <Input
                            id="state"
                            value={formData.state}
                            onChange={(e) => handleInputChange("state", e.target.value)}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="zipCode">ZIP Code *</Label>
                          <Input
                            id="zipCode"
                            value={formData.zipCode}
                            onChange={(e) => handleInputChange("zipCode", e.target.value)}
                            required
                          />
                        </div>
                      </div>

                      <Button
                        type="button"
                        onClick={() => setStep(2)}
                        className="w-full"
                        size="lg"
                      >
                        Continue to Housing Information
                      </Button>
                    </div>
                  )}

                  {/* Step 2: Housing Information */}
                  {step === 2 && (
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold">Housing Information</h3>

                      <div>
                        <Label>Housing Type *</Label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                          {["house", "apartment", "condo", "townhouse", "other"].map((type) => (
                            <button
                              key={type}
                              type="button"
                              onClick={() => handleInputChange("housingType", type as any)}
                              className={`p-3 rounded-lg border text-center transition-colors ${
                                formData.housingType === type
                                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900"
                                  : "border-gray-300 hover:border-gray-400"
                              }`}
                            >
                              <div className="font-medium capitalize">{type}</div>
                            </button>
                          ))}
                        </div>
                      </div>

                      <div>
                        <Label>Do you own or rent? *</Label>
                        <div className="grid grid-cols-2 gap-3 mt-2">
                          {["own", "rent"].map((type) => (
                            <button
                              key={type}
                              type="button"
                              onClick={() => handleInputChange("ownRent", type as any)}
                              className={`p-3 rounded-lg border text-center transition-colors ${
                                formData.ownRent === type
                                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900"
                                  : "border-gray-300 hover:border-gray-400"
                              }`}
                            >
                              <div className="font-medium capitalize">{type}</div>
                            </button>
                          ))}
                        </div>
                      </div>

                      {formData.ownRent === "rent" && (
                        <div>
                          <label className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={formData.landlordPermission}
                              onChange={(e) => handleInputChange("landlordPermission", e.target.checked)}
                              className="rounded border-gray-300"
                            />
                            <span>I have landlord permission to foster pets *</span>
                          </label>
                        </div>
                      )}

                      <div>
                        <Label>Yard Type</Label>
                        <div className="grid grid-cols-3 gap-3 mt-2">
                          {["fenced", "unfenced", "none"].map((type) => (
                            <button
                              key={type}
                              type="button"
                              onClick={() => handleInputChange("yardType", type as any)}
                              className={`p-3 rounded-lg border text-center transition-colors ${
                                formData.yardType === type
                                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900"
                                  : "border-gray-300 hover:border-gray-400"
                              }`}
                            >
                              <div className="font-medium capitalize">{type === "none" ? "No Yard" : type}</div>
                            </button>
                          ))}
                        </div>
                      </div>

                      <div className="flex space-x-4">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setStep(1)}
                          className="flex-1"
                        >
                          Back
                        </Button>
                        <Button
                          type="button"
                          onClick={() => setStep(3)}
                          className="flex-1"
                        >
                          Continue to Experience
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Step 3: Experience & Preferences */}
                  {step === 3 && (
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold">Experience & Preferences</h3>

                      <div>
                        <Label htmlFor="petExperience">Pet Experience *</Label>
                        <Textarea
                          id="petExperience"
                          placeholder="Describe your experience with pets..."
                          value={formData.petExperience}
                          onChange={(e) => handleInputChange("petExperience", e.target.value)}
                          required
                          rows={3}
                        />
                      </div>

                      <div>
                        <Label htmlFor="currentPets">Current Pets</Label>
                        <Textarea
                          id="currentPets"
                          placeholder="List any current pets (type, age, spayed/neutered status)..."
                          value={formData.currentPets}
                          onChange={(e) => handleInputChange("currentPets", e.target.value)}
                          rows={3}
                        />
                      </div>

                      <div>
                        <Label htmlFor="hoursAlone">Hours pets would be alone daily *</Label>
                        <Input
                          id="hoursAlone"
                          placeholder="e.g., 4-6 hours"
                          value={formData.hoursAlone}
                          onChange={(e) => handleInputChange("hoursAlone", e.target.value)}
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="availability">Availability *</Label>
                        <Textarea
                          id="availability"
                          placeholder="When are you available to start fostering? Any scheduling constraints?"
                          value={formData.availability}
                          onChange={(e) => handleInputChange("availability", e.target.value)}
                          required
                          rows={3}
                        />
                      </div>

                      <div>
                        <Label htmlFor="duration">Preferred foster duration *</Label>
                        <Input
                          id="duration"
                          placeholder="e.g., 2-8 weeks, flexible, long-term"
                          value={formData.duration}
                          onChange={(e) => handleInputChange("duration", e.target.value)}
                          required
                        />
                      </div>

                      <div className="flex space-x-4">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setStep(2)}
                          className="flex-1"
                        >
                          Back
                        </Button>
                        <Button
                          type="button"
                          onClick={() => setStep(4)}
                          className="flex-1"
                        >
                          Continue to Final Details
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Step 4: Emergency Contact & Final Details */}
                  {step === 4 && (
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold">Emergency Contact</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="emergencyName">Emergency Contact Name *</Label>
                          <Input
                            id="emergencyName"
                            value={formData.emergencyName}
                            onChange={(e) => handleInputChange("emergencyName", e.target.value)}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="emergencyPhone">Emergency Contact Phone *</Label>
                          <Input
                            id="emergencyPhone"
                            type="tel"
                            value={formData.emergencyPhone}
                            onChange={(e) => handleInputChange("emergencyPhone", e.target.value)}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="emergencyRelation">Relationship *</Label>
                          <Input
                            id="emergencyRelation"
                            placeholder="e.g., spouse, parent, friend"
                            value={formData.emergencyRelation}
                            onChange={(e) => handleInputChange("emergencyRelation", e.target.value)}
                            required
                          />
                        </div>
                      </div>

                      <h3 className="text-lg font-semibold">Additional Information</h3>

                      <div>
                        <Label htmlFor="motivation">Why do you want to foster? *</Label>
                        <Textarea
                          id="motivation"
                          placeholder="Share your motivation for fostering..."
                          value={formData.motivation}
                          onChange={(e) => handleInputChange("motivation", e.target.value)}
                          required
                          rows={3}
                        />
                      </div>

                      <div>
                        <Label htmlFor="concerns">Any concerns or questions?</Label>
                        <Textarea
                          id="concerns"
                          placeholder="Share any concerns or questions about fostering..."
                          value={formData.concerns}
                          onChange={(e) => handleInputChange("concerns", e.target.value)}
                          rows={3}
                        />
                      </div>

                      <div>
                        <Label htmlFor="additionalInfo">Additional Information</Label>
                        <Textarea
                          id="additionalInfo"
                          placeholder="Anything else you'd like us to know..."
                          value={formData.additionalInfo}
                          onChange={(e) => handleInputChange("additionalInfo", e.target.value)}
                          rows={3}
                        />
                      </div>

                      {/* Agreements */}
                      <div className="space-y-3">
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={formData.homeVisit}
                            onChange={(e) => handleInputChange("homeVisit", e.target.checked)}
                            className="rounded border-gray-300"
                            required
                          />
                          <span>I agree to a home visit as part of the approval process *</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={formData.backgroundCheck}
                            onChange={(e) => handleInputChange("backgroundCheck", e.target.checked)}
                            className="rounded border-gray-300"
                            required
                          />
                          <span>I consent to a background check *</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={formData.newsletter}
                            onChange={(e) => handleInputChange("newsletter", e.target.checked)}
                            className="rounded border-gray-300"
                          />
                          <span>Subscribe to foster updates and newsletters</span>
                        </label>
                      </div>

                      <div className="flex space-x-4">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setStep(3)}
                          className="flex-1"
                        >
                          Back
                        </Button>
                        <Button
                          type="submit"
                          disabled={submitting || !formData.homeVisit || !formData.backgroundCheck}
                          className="flex-1 bg-blue-600 hover:bg-blue-700"
                        >
                          {submitting ? "Submitting..." : "Submit Application"}
                        </Button>
                      </div>
                    </div>
                  )}
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar Information */}
          <div className="space-y-6">
            {/* Requirements */}
            <Card>
              <CardHeader>
                <CardTitle>Foster Requirements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {requirements.map((req, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{req}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Process Timeline */}
            <Card>
              <CardHeader>
                <CardTitle>Application Process</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                      <span className="text-sm font-bold text-blue-600">1</span>
                    </div>
                    <div>
                      <div className="font-medium">Submit Application</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Complete online form</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                      <span className="text-sm font-bold text-blue-600">2</span>
                    </div>
                    <div>
                      <div className="font-medium">Phone Interview</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Within 48 hours</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                      <span className="text-sm font-bold text-blue-600">3</span>
                    </div>
                    <div>
                      <div className="font-medium">Home Visit</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Schedule within 1 week</div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                      <span className="text-sm font-bold text-blue-600">4</span>
                    </div>
                    <div>
                      <div className="font-medium">Approval & Matching</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Find your perfect foster match</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Questions?</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-blue-600" />
                    <span className="text-sm">(*************</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-blue-600" />
                    <span className="text-sm"><EMAIL></span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <MapPin className="h-4 w-4 text-blue-600 mt-0.5" />
                    <span className="text-sm">123 Pet Street<br />Animal City, AC 12345</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}