"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Heart, 
  DollarSign, 
  Users, 
  Home, 
  Stethoscope,
  ShoppingBag,
  CreditCard,
  Check,
  Star,
  Gift,
  Target,
  TrendingUp
} from "lucide-react"
import { toast } from "react-hot-toast"

interface DonationForm {
  amount: string
  customAmount: string
  frequency: "one-time" | "monthly" | "yearly"
  firstName: string
  lastName: string
  email: string
  phone: string
  address: string
  city: string
  state: string
  zipCode: string
  paymentMethod: "card" | "paypal" | "bank"
  cardNumber: string
  expiryDate: string
  cvv: string
  message: string
  anonymous: boolean
  newsletter: boolean
}

const donationAmounts = [
  { amount: "25", impact: "Provides food for 1 pet for a week" },
  { amount: "50", impact: "Covers basic medical checkup for 1 pet" },
  { amount: "100", impact: "Sponsors spay/neuter surgery for 1 pet" },
  { amount: "250", impact: "Covers emergency medical care" },
  { amount: "500", impact: "Sponsors a pet's complete adoption journey" },
  { amount: "1000", impact: "Supports our shelter operations for a month" }
]

const impactStats = [
  { icon: Heart, number: "2,847", label: "Pets Rescued This Year" },
  { icon: Home, number: "2,156", label: "Successful Adoptions" },
  { icon: Stethoscope, number: "3,421", label: "Medical Treatments Provided" },
  { icon: Users, number: "847", label: "Volunteers Active" }
]

export default function DonatePage() {
  const [formData, setFormData] = useState<DonationForm>({
    amount: "",
    customAmount: "",
    frequency: "one-time",
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    paymentMethod: "card",
    cardNumber: "",
    expiryDate: "",
    cvv: "",
    message: "",
    anonymous: false,
    newsletter: true
  })

  const [submitting, setSubmitting] = useState(false)
  const [step, setStep] = useState(1)

  const handleInputChange = (field: keyof DonationForm, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleAmountSelect = (amount: string) => {
    setFormData(prev => ({
      ...prev,
      amount,
      customAmount: ""
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      const response = await fetch("/api/donations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        toast.success("Thank you for your generous donation! Your support makes a real difference.")
        // Reset form
        setFormData({
          amount: "",
          customAmount: "",
          frequency: "one-time",
          firstName: "",
          lastName: "",
          email: "",
          phone: "",
          address: "",
          city: "",
          state: "",
          zipCode: "",
          paymentMethod: "card",
          cardNumber: "",
          expiryDate: "",
          cvv: "",
          message: "",
          anonymous: false,
          newsletter: true
        })
        setStep(1)
      } else {
        const error = await response.json()
        toast.error(error.error || "Failed to process donation")
      }
    } catch (error) {
      console.error("Error processing donation:", error)
      toast.error("Failed to process donation")
    } finally {
      setSubmitting(false)
    }
  }

  const selectedAmount = formData.customAmount || formData.amount

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Heart className="h-12 w-12 text-red-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              Make a Difference
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Your donation helps us rescue, care for, and find loving homes for pets in need. 
            Every contribution, no matter the size, makes a real impact.
          </p>
        </div>

        {/* Impact Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          {impactStats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <Card key={index} className="text-center">
                <CardContent className="pt-6">
                  <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">
                    {stat.number}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {stat.label}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        <div className="max-w-4xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Donation Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">Make Your Donation</CardTitle>
                <CardDescription>
                  Choose your donation amount and frequency
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Step 1: Amount Selection */}
                  {step === 1 && (
                    <div className="space-y-6">
                      {/* Frequency Selection */}
                      <div>
                        <Label className="text-base font-semibold">Donation Frequency</Label>
                        <div className="grid grid-cols-3 gap-3 mt-2">
                          {["one-time", "monthly", "yearly"].map((freq) => (
                            <button
                              key={freq}
                              type="button"
                              onClick={() => handleInputChange("frequency", freq as any)}
                              className={`p-3 rounded-lg border text-center transition-colors ${
                                formData.frequency === freq
                                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300"
                                  : "border-gray-300 hover:border-gray-400"
                              }`}
                            >
                              <div className="font-medium capitalize">{freq.replace("-", " ")}</div>
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Amount Selection */}
                      <div>
                        <Label className="text-base font-semibold">Donation Amount</Label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                          {donationAmounts.map((donation) => (
                            <button
                              key={donation.amount}
                              type="button"
                              onClick={() => handleAmountSelect(donation.amount)}
                              className={`p-4 rounded-lg border text-left transition-colors ${
                                formData.amount === donation.amount
                                  ? "border-blue-500 bg-blue-50 dark:bg-blue-900"
                                  : "border-gray-300 hover:border-gray-400"
                              }`}
                            >
                              <div className="font-bold text-lg">${donation.amount}</div>
                              <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {donation.impact}
                              </div>
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Custom Amount */}
                      <div>
                        <Label htmlFor="customAmount">Custom Amount</Label>
                        <div className="relative mt-1">
                          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <Input
                            id="customAmount"
                            type="number"
                            placeholder="Enter custom amount"
                            value={formData.customAmount}
                            onChange={(e) => {
                              handleInputChange("customAmount", e.target.value)
                              handleInputChange("amount", "")
                            }}
                            className="pl-10"
                          />
                        </div>
                      </div>

                      <Button 
                        type="button" 
                        onClick={() => setStep(2)}
                        disabled={!selectedAmount}
                        className="w-full"
                        size="lg"
                      >
                        Continue to Payment
                      </Button>
                    </div>
                  )}

                  {/* Step 2: Personal Information & Payment */}
                  {step === 2 && (
                    <div className="space-y-6">
                      {/* Personal Information */}
                      <div>
                        <h3 className="text-lg font-semibold mb-4">Personal Information</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="firstName">First Name *</Label>
                            <Input
                              id="firstName"
                              value={formData.firstName}
                              onChange={(e) => handleInputChange("firstName", e.target.value)}
                              required
                            />
                          </div>
                          <div>
                            <Label htmlFor="lastName">Last Name *</Label>
                            <Input
                              id="lastName"
                              value={formData.lastName}
                              onChange={(e) => handleInputChange("lastName", e.target.value)}
                              required
                            />
                          </div>
                          <div>
                            <Label htmlFor="email">Email Address *</Label>
                            <Input
                              id="email"
                              type="email"
                              value={formData.email}
                              onChange={(e) => handleInputChange("email", e.target.value)}
                              required
                            />
                          </div>
                          <div>
                            <Label htmlFor="phone">Phone Number</Label>
                            <Input
                              id="phone"
                              type="tel"
                              value={formData.phone}
                              onChange={(e) => handleInputChange("phone", e.target.value)}
                            />
                          </div>
                        </div>
                      </div>

                      {/* Payment Method */}
                      <div>
                        <h3 className="text-lg font-semibold mb-4">Payment Method</h3>
                        <div className="grid grid-cols-3 gap-3 mb-4">
                          {[
                            { id: "card", label: "Credit Card", icon: CreditCard },
                            { id: "paypal", label: "PayPal", icon: DollarSign },
                            { id: "bank", label: "Bank Transfer", icon: Target }
                          ].map((method) => {
                            const IconComponent = method.icon
                            return (
                              <button
                                key={method.id}
                                type="button"
                                onClick={() => handleInputChange("paymentMethod", method.id as any)}
                                className={`p-3 rounded-lg border text-center transition-colors ${
                                  formData.paymentMethod === method.id
                                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900"
                                    : "border-gray-300 hover:border-gray-400"
                                }`}
                              >
                                <IconComponent className="h-6 w-6 mx-auto mb-2" />
                                <div className="text-sm font-medium">{method.label}</div>
                              </button>
                            )
                          })}
                        </div>

                        {formData.paymentMethod === "card" && (
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="md:col-span-2">
                              <Label htmlFor="cardNumber">Card Number *</Label>
                              <Input
                                id="cardNumber"
                                placeholder="1234 5678 9012 3456"
                                value={formData.cardNumber}
                                onChange={(e) => handleInputChange("cardNumber", e.target.value)}
                                required
                              />
                            </div>
                            <div>
                              <Label htmlFor="expiryDate">Expiry Date *</Label>
                              <Input
                                id="expiryDate"
                                placeholder="MM/YY"
                                value={formData.expiryDate}
                                onChange={(e) => handleInputChange("expiryDate", e.target.value)}
                                required
                              />
                            </div>
                            <div>
                              <Label htmlFor="cvv">CVV *</Label>
                              <Input
                                id="cvv"
                                placeholder="123"
                                value={formData.cvv}
                                onChange={(e) => handleInputChange("cvv", e.target.value)}
                                required
                              />
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Optional Message */}
                      <div>
                        <Label htmlFor="message">Message (Optional)</Label>
                        <Textarea
                          id="message"
                          placeholder="Share why you're supporting our mission..."
                          value={formData.message}
                          onChange={(e) => handleInputChange("message", e.target.value)}
                          rows={3}
                        />
                      </div>

                      {/* Checkboxes */}
                      <div className="space-y-3">
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={formData.anonymous}
                            onChange={(e) => handleInputChange("anonymous", e.target.checked)}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm">Make this donation anonymous</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={formData.newsletter}
                            onChange={(e) => handleInputChange("newsletter", e.target.checked)}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm">Subscribe to our newsletter for updates</span>
                        </label>
                      </div>

                      <div className="flex space-x-4">
                        <Button 
                          type="button" 
                          variant="outline"
                          onClick={() => setStep(1)}
                          className="flex-1"
                        >
                          Back
                        </Button>
                        <Button 
                          type="submit" 
                          disabled={submitting}
                          className="flex-1 bg-red-600 hover:bg-red-700"
                        >
                          {submitting ? "Processing..." : `Donate $${selectedAmount}`}
                        </Button>
                      </div>
                    </div>
                  )}
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Donation Summary & Impact */}
          <div className="space-y-6">
            {/* Donation Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Donation Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Amount:</span>
                    <span className="font-semibold">
                      ${selectedAmount || "0"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Frequency:</span>
                    <span className="font-semibold capitalize">
                      {formData.frequency.replace("-", " ")}
                    </span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between text-lg font-bold">
                      <span>Total:</span>
                      <span>${selectedAmount || "0"}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Impact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Your Impact</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <Check className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <div className="font-medium">100% of your donation goes directly to pet care</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        Administrative costs are covered by separate funding
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Check className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <div className="font-medium">Tax-deductible donation</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        You'll receive a receipt for tax purposes
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Check className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <div className="font-medium">Regular updates</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        See how your donation is making a difference
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Other Ways to Help */}
            <Card>
              <CardHeader>
                <CardTitle>Other Ways to Help</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <a href="/get-involved/volunteer">
                      <Users className="h-4 w-4 mr-2" />
                      Volunteer Your Time
                    </a>
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <a href="/get-involved/foster">
                      <Home className="h-4 w-4 mr-2" />
                      Foster a Pet
                    </a>
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <a href="/shop">
                      <ShoppingBag className="h-4 w-4 mr-2" />
                      Shop for Supplies
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
