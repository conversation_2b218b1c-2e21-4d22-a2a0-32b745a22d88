"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Calendar, 
  MapPin, 
  Clock, 
  Users, 
  Heart,
  PawPrint,
  Gift,
  Camera,
  Utensils,
  Music,
  Star,
  Filter,
  Search,
  ExternalLink
} from "lucide-react"

interface Event {
  id: string
  title: string
  description: string
  date: string
  time: string
  location: string
  address: string
  category: "adoption" | "fundraising" | "education" | "volunteer" | "social"
  capacity: number
  registered: number
  price: number
  image: string
  featured: boolean
  tags: string[]
}

const events: Event[] = [
  {
    id: "1",
    title: "Super Adoption Saturday",
    description: "Our biggest adoption event of the month! Meet dozens of adorable pets looking for their forever homes. All adoption fees reduced by 50%.",
    date: "2024-02-10",
    time: "10:00 AM - 4:00 PM",
    location: "Central Park Pavilion",
    address: "123 Park Avenue, City Center",
    category: "adoption",
    capacity: 200,
    registered: 87,
    price: 0,
    image: "/images/events/adoption-event.jpg",
    featured: true,
    tags: ["adoption", "dogs", "cats", "family-friendly"]
  },
  {
    id: "2",
    title: "Paws & Pasta Fundraising Dinner",
    description: "Join us for an elegant evening of fine dining to support our rescue mission. Silent auction, live music, and special guest speakers.",
    date: "2024-02-15",
    time: "6:00 PM - 10:00 PM",
    location: "Grand Ballroom Hotel",
    address: "456 Luxury Lane, Downtown",
    category: "fundraising",
    capacity: 150,
    registered: 92,
    price: 75,
    image: "/images/events/fundraising-dinner.jpg",
    featured: true,
    tags: ["fundraising", "dinner", "auction", "formal"]
  },
  {
    id: "3",
    title: "Pet First Aid Workshop",
    description: "Learn essential first aid skills for pets. Certified veterinarians will teach CPR, wound care, and emergency response techniques.",
    date: "2024-02-18",
    time: "2:00 PM - 5:00 PM",
    location: "Community Center",
    address: "789 Learning Street, Education District",
    category: "education",
    capacity: 30,
    registered: 18,
    price: 25,
    image: "/images/events/first-aid-workshop.jpg",
    featured: false,
    tags: ["education", "first-aid", "veterinary", "workshop"]
  },
  {
    id: "4",
    title: "Volunteer Orientation",
    description: "New volunteer orientation session. Learn about our programs, meet the team, and discover how you can make a difference.",
    date: "2024-02-22",
    time: "6:30 PM - 8:30 PM",
    location: "Shelter Main Building",
    address: "321 Rescue Road, Animal District",
    category: "volunteer",
    capacity: 25,
    registered: 12,
    price: 0,
    image: "/images/events/volunteer-orientation.jpg",
    featured: false,
    tags: ["volunteer", "orientation", "new-members"]
  },
  {
    id: "5",
    title: "Puppy Socialization Class",
    description: "Help socialize our rescue puppies! Play, train, and prepare young dogs for their future families. Perfect for experienced dog handlers.",
    date: "2024-02-25",
    time: "10:00 AM - 12:00 PM",
    location: "Training Facility",
    address: "654 Training Way, Pet Campus",
    category: "volunteer",
    capacity: 15,
    registered: 8,
    price: 0,
    image: "/images/events/puppy-class.jpg",
    featured: false,
    tags: ["volunteer", "puppies", "training", "socialization"]
  },
  {
    id: "6",
    title: "Pet Photography Workshop",
    description: "Learn professional pet photography techniques to help our animals get adopted faster. Bring your camera and practice with our photogenic pets!",
    date: "2024-03-02",
    time: "1:00 PM - 4:00 PM",
    location: "Photography Studio",
    address: "987 Creative Avenue, Arts District",
    category: "education",
    capacity: 20,
    registered: 14,
    price: 35,
    image: "/images/events/photography-workshop.jpg",
    featured: false,
    tags: ["education", "photography", "volunteer", "creative"]
  }
]

const categories = [
  { id: "all", label: "All Events", icon: Calendar },
  { id: "adoption", label: "Adoption Events", icon: Heart },
  { id: "fundraising", label: "Fundraising", icon: Gift },
  { id: "education", label: "Education", icon: Star },
  { id: "volunteer", label: "Volunteer", icon: Users },
  { id: "social", label: "Social", icon: Music }
]

export default function EventsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [registeredEvents, setRegisteredEvents] = useState<string[]>([])

  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === "all" || event.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const handleRegister = (eventId: string) => {
    setRegisteredEvents(prev => 
      prev.includes(eventId) 
        ? prev.filter(id => id !== eventId)
        : [...prev, eventId]
    )
  }

  const getCategoryIcon = (category: string) => {
    const categoryData = categories.find(cat => cat.id === category)
    return categoryData?.icon || Calendar
  }

  const getCategoryColor = (category: string) => {
    const colors = {
      adoption: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      fundraising: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      education: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      volunteer: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
      social: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
    }
    return colors[category as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Calendar className="h-12 w-12 text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              Upcoming Events
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Join our community events and make a difference in the lives of pets in need. 
            From adoption events to educational workshops, there's something for everyone.
          </p>
        </div>

        {/* Search and Filter */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search events..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2 overflow-x-auto pb-2">
              {categories.map((category) => {
                const IconComponent = category.icon
                return (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    onClick={() => setSelectedCategory(category.id)}
                    className="whitespace-nowrap"
                  >
                    <IconComponent className="h-4 w-4 mr-2" />
                    {category.label}
                  </Button>
                )
              })}
            </div>
          </div>
        </div>

        {/* Featured Events */}
        {selectedCategory === "all" && (
          <div className="max-w-6xl mx-auto mb-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">Featured Events</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {events.filter(event => event.featured).map((event) => {
                const CategoryIcon = getCategoryIcon(event.category)
                const isRegistered = registeredEvents.includes(event.id)
                
                return (
                  <Card key={event.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="relative">
                      <div className="h-48 bg-gradient-to-r from-blue-400 to-purple-500"></div>
                      <div className="absolute top-4 left-4">
                        <Badge className={getCategoryColor(event.category)}>
                          <CategoryIcon className="h-3 w-3 mr-1" />
                          {event.category}
                        </Badge>
                      </div>
                      {event.featured && (
                        <div className="absolute top-4 right-4">
                          <Badge className="bg-yellow-500 text-yellow-900">
                            <Star className="h-3 w-3 mr-1" />
                            Featured
                          </Badge>
                        </div>
                      )}
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                        {event.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
                        {event.description}
                      </p>
                      
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <Calendar className="h-4 w-4 mr-2" />
                          {new Date(event.date).toLocaleDateString()} at {event.time}
                        </div>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <MapPin className="h-4 w-4 mr-2" />
                          {event.location}
                        </div>
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <Users className="h-4 w-4 mr-2" />
                          {event.registered}/{event.capacity} registered
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                          {event.price === 0 ? "Free" : `$${event.price}`}
                        </div>
                        <Button
                          onClick={() => handleRegister(event.id)}
                          variant={isRegistered ? "outline" : "default"}
                          disabled={event.registered >= event.capacity}
                        >
                          {isRegistered ? "Registered" : 
                           event.registered >= event.capacity ? "Full" : "Register"}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        )}

        {/* All Events */}
        <div className="max-w-6xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            {selectedCategory === "all" ? "All Events" : `${categories.find(c => c.id === selectedCategory)?.label} Events`}
          </h2>
          
          {filteredEvents.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  No events found
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Try adjusting your search or filter criteria.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredEvents.map((event) => {
                const CategoryIcon = getCategoryIcon(event.category)
                const isRegistered = registeredEvents.includes(event.id)
                
                return (
                  <Card key={event.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="relative">
                      <div className="h-32 bg-gradient-to-r from-blue-400 to-purple-500"></div>
                      <div className="absolute top-3 left-3">
                        <Badge className={getCategoryColor(event.category)}>
                          <CategoryIcon className="h-3 w-3 mr-1" />
                          {event.category}
                        </Badge>
                      </div>
                      {event.featured && (
                        <div className="absolute top-3 right-3">
                          <Badge className="bg-yellow-500 text-yellow-900">
                            <Star className="h-3 w-3 mr-1" />
                            Featured
                          </Badge>
                        </div>
                      )}
                    </div>
                    <CardContent className="p-4">
                      <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">
                        {event.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 mb-3 text-sm line-clamp-2">
                        {event.description}
                      </p>
                      
                      <div className="space-y-1 mb-3">
                        <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                          <Calendar className="h-3 w-3 mr-2" />
                          {new Date(event.date).toLocaleDateString()}
                        </div>
                        <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                          <Clock className="h-3 w-3 mr-2" />
                          {event.time}
                        </div>
                        <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                          <MapPin className="h-3 w-3 mr-2" />
                          {event.location}
                        </div>
                        <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                          <Users className="h-3 w-3 mr-2" />
                          {event.registered}/{event.capacity} spots
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="font-bold text-gray-900 dark:text-gray-100">
                          {event.price === 0 ? "Free" : `$${event.price}`}
                        </div>
                        <Button
                          size="sm"
                          onClick={() => handleRegister(event.id)}
                          variant={isRegistered ? "outline" : "default"}
                          disabled={event.registered >= event.capacity}
                        >
                          {isRegistered ? "✓" : 
                           event.registered >= event.capacity ? "Full" : "Register"}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>

        {/* Call to Action */}
        <div className="max-w-4xl mx-auto mt-16">
          <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="text-center py-12">
              <PawPrint className="h-12 w-12 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-4">
                Want to Host an Event?
              </h2>
              <p className="text-lg mb-6 opacity-90">
                Partner with us to organize community events that help pets find homes and raise awareness.
              </p>
              <Button variant="secondary" size="lg">
                <ExternalLink className="h-4 w-4 mr-2" />
                Contact Event Team
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
