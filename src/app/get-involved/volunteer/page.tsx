"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Heart, 
  Users, 
  Clock, 
  MapPin, 
  Phone, 
  Mail,
  CheckCircle,
  Star,
  Calendar,
  Award,
  Smile
} from "lucide-react"
import { toast } from "react-hot-toast"

interface VolunteerForm {
  // Personal Information
  firstName: string
  lastName: string
  email: string
  phone: string
  address: string
  city: string
  state: string
  zipCode: string
  dateOfBirth: string
  
  // Volunteer Preferences
  interests: string[]
  availability: string[]
  experience: string
  skills: string
  transportation: string
  
  // Background
  previousVolunteer: string
  motivation: string
  references: string
  
  // Emergency Contact
  emergencyName: string
  emergencyPhone: string
  emergencyRelationship: string
}

const volunteerOpportunities = [
  {
    id: "animal-care",
    title: "Animal Care Assistant",
    description: "Help with feeding, grooming, and socializing animals",
    icon: Heart,
    timeCommitment: "4-8 hours/week"
  },
  {
    id: "adoption-events",
    title: "Adoption Event Coordinator",
    description: "Organize and staff adoption events in the community",
    icon: Users,
    timeCommitment: "Weekends"
  },
  {
    id: "transport",
    title: "Pet Transportation",
    description: "Transport animals to vet appointments and events",
    icon: MapPin,
    timeCommitment: "Flexible"
  },
  {
    id: "foster",
    title: "Foster Care Provider",
    description: "Provide temporary homes for animals in need",
    icon: Smile,
    timeCommitment: "Ongoing"
  },
  {
    id: "admin",
    title: "Administrative Support",
    description: "Help with paperwork, data entry, and office tasks",
    icon: CheckCircle,
    timeCommitment: "2-4 hours/week"
  },
  {
    id: "fundraising",
    title: "Fundraising & Events",
    description: "Organize fundraising events and campaigns",
    icon: Star,
    timeCommitment: "Project-based"
  }
]

export default function VolunteerPage() {
  const [formData, setFormData] = useState<VolunteerForm>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    dateOfBirth: "",
    interests: [],
    availability: [],
    experience: "",
    skills: "",
    transportation: "",
    previousVolunteer: "",
    motivation: "",
    references: "",
    emergencyName: "",
    emergencyPhone: "",
    emergencyRelationship: ""
  })

  const [submitting, setSubmitting] = useState(false)
  const [step, setStep] = useState(1)

  const handleInputChange = (field: keyof VolunteerForm, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleInterestChange = (interest: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      interests: checked 
        ? [...prev.interests, interest]
        : prev.interests.filter(i => i !== interest)
    }))
  }

  const handleAvailabilityChange = (day: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      availability: checked 
        ? [...prev.availability, day]
        : prev.availability.filter(d => d !== day)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      const response = await fetch("/api/volunteers/apply", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        toast.success("Volunteer application submitted successfully! We'll be in touch soon.")
        // Reset form
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          phone: "",
          address: "",
          city: "",
          state: "",
          zipCode: "",
          dateOfBirth: "",
          interests: [],
          availability: [],
          experience: "",
          skills: "",
          transportation: "",
          previousVolunteer: "",
          motivation: "",
          references: "",
          emergencyName: "",
          emergencyPhone: "",
          emergencyRelationship: ""
        })
        setStep(1)
      } else {
        const error = await response.json()
        toast.error(error.error || "Failed to submit application")
      }
    } catch (error) {
      console.error("Error submitting application:", error)
      toast.error("Failed to submit application")
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Heart className="h-12 w-12 text-red-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              Become a Volunteer
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Join our amazing team of volunteers and make a real difference in the lives of 
            animals in need. Every hour you give helps save lives and find loving homes.
          </p>
        </div>

        {/* Volunteer Opportunities */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-center text-gray-900 dark:text-gray-100 mb-8">
            Volunteer Opportunities
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {volunteerOpportunities.map((opportunity) => {
              const IconComponent = opportunity.icon
              return (
                <Card key={opportunity.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <IconComponent className="h-8 w-8 text-blue-600" />
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        {opportunity.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {opportunity.description}
                      </p>
                      <div className="flex items-center justify-center text-sm text-blue-600">
                        <Clock className="h-4 w-4 mr-1" />
                        {opportunity.timeCommitment}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Application Form */}
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl text-center">Volunteer Application</CardTitle>
              <CardDescription className="text-center">
                Tell us about yourself and how you'd like to help
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Personal Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Personal Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name *</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => handleInputChange("firstName", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name *</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => handleInputChange("lastName", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">Phone Number *</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleInputChange("phone", e.target.value)}
                        required
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor="address">Address *</Label>
                      <Input
                        id="address"
                        value={formData.address}
                        onChange={(e) => handleInputChange("address", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="city">City *</Label>
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={(e) => handleInputChange("city", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="state">State *</Label>
                      <Input
                        id="state"
                        value={formData.state}
                        onChange={(e) => handleInputChange("state", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="zipCode">ZIP Code *</Label>
                      <Input
                        id="zipCode"
                        value={formData.zipCode}
                        onChange={(e) => handleInputChange("zipCode", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="dateOfBirth">Date of Birth *</Label>
                      <Input
                        id="dateOfBirth"
                        type="date"
                        value={formData.dateOfBirth}
                        onChange={(e) => handleInputChange("dateOfBirth", e.target.value)}
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Volunteer Interests */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Areas of Interest
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {volunteerOpportunities.map((opportunity) => (
                      <label key={opportunity.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={formData.interests.includes(opportunity.id)}
                          onChange={(e) => handleInterestChange(opportunity.id, e.target.checked)}
                          className="rounded border-gray-300"
                        />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {opportunity.title}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Availability */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Availability
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"].map((day) => (
                      <label key={day} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={formData.availability.includes(day)}
                          onChange={(e) => handleAvailabilityChange(day, e.target.checked)}
                          className="rounded border-gray-300"
                        />
                        <span className="text-sm text-gray-700 dark:text-gray-300">{day}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Experience and Skills */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="experience">Previous Animal Experience</Label>
                    <Textarea
                      id="experience"
                      value={formData.experience}
                      onChange={(e) => handleInputChange("experience", e.target.value)}
                      placeholder="Describe any experience with animals..."
                      rows={4}
                    />
                  </div>
                  <div>
                    <Label htmlFor="skills">Special Skills or Qualifications</Label>
                    <Textarea
                      id="skills"
                      value={formData.skills}
                      onChange={(e) => handleInputChange("skills", e.target.value)}
                      placeholder="Any relevant skills, certifications, or qualifications..."
                      rows={4}
                    />
                  </div>
                </div>

                {/* Transportation */}
                <div>
                  <Label htmlFor="transportation">Do you have reliable transportation?</Label>
                  <select
                    id="transportation"
                    value={formData.transportation}
                    onChange={(e) => handleInputChange("transportation", e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select an option</option>
                    <option value="yes">Yes, I have my own vehicle</option>
                    <option value="public">I use public transportation</option>
                    <option value="no">No, I would need transportation assistance</option>
                  </select>
                </div>

                {/* Background Questions */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="previousVolunteer">Have you volunteered with animals before?</Label>
                    <Textarea
                      id="previousVolunteer"
                      value={formData.previousVolunteer}
                      onChange={(e) => handleInputChange("previousVolunteer", e.target.value)}
                      placeholder="Tell us about your previous volunteer experience..."
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="motivation">Why do you want to volunteer with us?</Label>
                    <Textarea
                      id="motivation"
                      value={formData.motivation}
                      onChange={(e) => handleInputChange("motivation", e.target.value)}
                      placeholder="What motivates you to help animals?"
                      rows={3}
                    />
                  </div>
                </div>

                {/* Emergency Contact */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Emergency Contact
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="emergencyName">Name *</Label>
                      <Input
                        id="emergencyName"
                        value={formData.emergencyName}
                        onChange={(e) => handleInputChange("emergencyName", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="emergencyPhone">Phone Number *</Label>
                      <Input
                        id="emergencyPhone"
                        type="tel"
                        value={formData.emergencyPhone}
                        onChange={(e) => handleInputChange("emergencyPhone", e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="emergencyRelationship">Relationship *</Label>
                      <Input
                        id="emergencyRelationship"
                        value={formData.emergencyRelationship}
                        onChange={(e) => handleInputChange("emergencyRelationship", e.target.value)}
                        placeholder="e.g., Spouse, Parent, Friend"
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* References */}
                <div>
                  <Label htmlFor="references">References (Optional)</Label>
                  <Textarea
                    id="references"
                    value={formData.references}
                    onChange={(e) => handleInputChange("references", e.target.value)}
                    placeholder="Please provide 2-3 references with names and phone numbers..."
                    rows={4}
                  />
                </div>

                {/* Submit Button */}
                <div className="text-center">
                  <Button 
                    type="submit" 
                    disabled={submitting}
                    size="lg"
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    {submitting ? (
                      <>
                        <Clock className="h-4 w-4 mr-2 animate-spin" />
                        Submitting Application...
                      </>
                    ) : (
                      <>
                        <Heart className="h-4 w-4 mr-2" />
                        Submit Volunteer Application
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Next Steps */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>What Happens Next?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <CheckCircle className="h-6 w-6 text-blue-600" />
                  </div>
                  <h4 className="font-semibold mb-2">Application Review</h4>
                  <p className="text-sm text-gray-600">We'll review your application within 3-5 business days</p>
                </div>
                <div className="text-center">
                  <div className="bg-green-100 dark:bg-green-900 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <Phone className="h-6 w-6 text-green-600" />
                  </div>
                  <h4 className="font-semibold mb-2">Interview</h4>
                  <p className="text-sm text-gray-600">We'll schedule a brief phone or in-person interview</p>
                </div>
                <div className="text-center">
                  <div className="bg-purple-100 dark:bg-purple-900 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                    <Award className="h-6 w-6 text-purple-600" />
                  </div>
                  <h4 className="font-semibold mb-2">Orientation</h4>
                  <p className="text-sm text-gray-600">Attend our volunteer orientation and training session</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
