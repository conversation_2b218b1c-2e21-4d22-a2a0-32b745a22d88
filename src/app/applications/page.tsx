"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRout<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Search, 
  Filter, 
  FileText, 
  Calendar, 
  User, 
  Heart,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Download
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { toast } from "react-hot-toast"

interface Application {
  id: string
  status: string
  submittedAt: string
  updatedAt: string
  pet: {
    id: string
    name: string
    species: string
    breed: string
    photos: Array<{ url: string; isPrimary: boolean }>
    organization: {
      name: string
      city: string
      state: string
    }
  }
  user: {
    name: string
    email: string
  }
}

const statusColors = {
  DRAFT: "bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200",
  SUBMITTED: "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200",
  UNDER_REVIEW: "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200",
  REFERENCE_CHECK: "bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200",
  HOME_VISIT_SCHEDULED: "bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200",
  HOME_VISIT_COMPLETED: "bg-cyan-100 dark:bg-cyan-900/30 text-cyan-800 dark:text-cyan-200",
  APPROVED: "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200",
  REJECTED: "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200",
  WITHDRAWN: "bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200",
  WAITLISTED: "bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200"
}

const statusIcons = {
  DRAFT: Clock,
  SUBMITTED: FileText,
  UNDER_REVIEW: AlertCircle,
  REFERENCE_CHECK: User,
  HOME_VISIT_SCHEDULED: Calendar,
  HOME_VISIT_COMPLETED: CheckCircle,
  APPROVED: CheckCircle,
  REJECTED: XCircle,
  WITHDRAWN: XCircle,
  WAITLISTED: Clock
}

export default function ApplicationsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [applications, setApplications] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("ALL")

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin")
      return
    }

    if (status === "authenticated") {
      fetchApplications()
    }
  }, [status, router])

  const fetchApplications = async () => {
    try {
      const response = await fetch("/api/applications")
      if (response.ok) {
        const data = await response.json()
        setApplications(data.applications || [])
      } else {
        toast.error("Failed to fetch applications")
      }
    } catch (error) {
      console.error("Error fetching applications:", error)
      toast.error("Failed to fetch applications")
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatStatus = (status: string) => {
    return status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const filteredApplications = applications.filter(app => {
    const matchesSearch = 
      app.pet?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.pet?.species?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.pet?.breed?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.user?.name?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === "ALL" || app.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            My Applications
          </h1>
          <p className="text-gray-600">
            Track and manage your pet adoption applications
          </p>
        </div>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by pet name, species, breed, or applicant..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="ALL">All Status</option>
                  <option value="SUBMITTED">Submitted</option>
                  <option value="UNDER_REVIEW">Under Review</option>
                  <option value="REFERENCE_CHECK">Reference Check</option>
                  <option value="HOME_VISIT_SCHEDULED">Home Visit Scheduled</option>
                  <option value="APPROVED">Approved</option>
                  <option value="REJECTED">Rejected</option>
                  <option value="WAITLISTED">Waitlisted</option>
                </select>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Applications List */}
        {filteredApplications.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {applications.length === 0 ? "No Applications Yet" : "No Matching Applications"}
              </h3>
              <p className="text-gray-600 mb-6">
                {applications.length === 0 
                  ? "You haven't submitted any adoption applications yet."
                  : "Try adjusting your search or filter criteria."
                }
              </p>
              {applications.length === 0 && (
                <Link href="/pets">
                  <Button>
                    <Heart className="h-4 w-4 mr-2" />
                    Browse Available Pets
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {filteredApplications.map((application) => {
              const StatusIcon = statusIcons[application.status as keyof typeof statusIcons] || FileText
              
              return (
                <Card key={application.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <CardContent className="p-0">
                    <div className="flex flex-col lg:flex-row">
                      {/* Pet Image */}
                      <div className="lg:w-48 h-48 lg:h-auto relative">
                        {application.pet?.photos?.length > 0 ? (
                          <Image
                            src={application.pet.photos.find(p => p.isPrimary)?.url || application.pet.photos[0].url}
                            alt={application.pet?.name || 'Pet'}
                            fill
                            className="object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                            <span className="text-4xl">🐾</span>
                          </div>
                        )}
                      </div>

                      {/* Application Details */}
                      <div className="flex-1 p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div>
                            <h3 className="text-xl font-bold text-gray-900 mb-1">
                              {application.pet?.name || 'Unknown Pet'}
                            </h3>
                            <p className="text-gray-600 mb-2">
                              {application.pet?.breed || 'Unknown'} • {application.pet?.species || 'Unknown'}
                            </p>
                            <p className="text-sm text-gray-500">
                              {application.pet?.organization?.name || 'Unknown Organization'} • {application.pet?.organization?.city || 'Unknown'}, {application.pet?.organization?.state || 'Unknown'}
                            </p>
                          </div>
                          <Badge className={statusColors[application.status as keyof typeof statusColors] || "bg-gray-100 text-gray-800"}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {formatStatus(application.status)}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                          <div>
                            <span className="text-gray-500">Submitted:</span>
                            <p className="font-medium">{formatDate(application.submittedAt)}</p>
                          </div>
                          <div>
                            <span className="text-gray-500">Last Updated:</span>
                            <p className="font-medium">{formatDate(application.updatedAt)}</p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="text-sm text-gray-500">
                            Application #{application.id.slice(-8)}
                          </div>
                          <div className="flex gap-2">
                            <Link href={`/dashboard/applications/${application.id}`}>
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </Button>
                            </Link>
                            {application.pet?.id && (
                              <Link href={`/pets/${application.pet.id}`}>
                                <Button variant="outline" size="sm">
                                  View Pet
                                </Button>
                              </Link>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}

        {/* Summary Stats */}
        {applications.length > 0 && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Application Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{applications.length}</p>
                  <p className="text-sm text-gray-600">Total Applications</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-yellow-600">
                    {applications.filter(app => ['SUBMITTED', 'UNDER_REVIEW', 'REFERENCE_CHECK', 'HOME_VISIT_SCHEDULED'].includes(app.status)).length}
                  </p>
                  <p className="text-sm text-gray-600">In Progress</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    {applications.filter(app => app.status === 'APPROVED').length}
                  </p>
                  <p className="text-sm text-gray-600">Approved</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-600">
                    {applications.filter(app => ['REJECTED', 'WITHDRAWN'].includes(app.status)).length}
                  </p>
                  <p className="text-sm text-gray-600">Closed</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
