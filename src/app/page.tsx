"use client"

import { useState, useEffect } from "react"
import <PERSON> from "next/link"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Heart, Users, Home as HomeIcon, Award, ArrowRight, Dog, Cat, Rabbit, Star, Quote, Play, Pause, ChevronLeft, ChevronRight, Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"

// Joyful pet carousel data
const heroSlides = [
  {
    id: 1,
    image: "/images/hero/happy-dog-1.jpg",
    alt: "Joyful golden retriever playing in a sunny meadow",
    title: "Your New Best Friend Awaits",
    subtitle: "Meet Luna, a playful golden retriever ready to fill your home with love and laughter",
    animation: "wagging",
    color: "from-amber-400 to-orange-500"
  },
  {
    id: 2,
    image: "/images/hero/curious-cat-1.jpg",
    alt: "Curious tabby cat with bright green eyes",
    title: "A Purr-fect Companion",
    subtitle: "Whis<PERSON> is looking for a cozy lap and warm heart to call home",
    animation: "pawSwipe",
    color: "from-purple-400 to-pink-500"
  },
  {
    id: 3,
    image: "/images/hero/playful-puppy-1.jpg",
    alt: "Energetic puppy with a tennis ball",
    title: "Endless Joy Awaits",
    subtitle: "Buddy brings boundless energy and unconditional love to every day",
    animation: "bouncing",
    color: "from-blue-400 to-cyan-500"
  },
  {
    id: 4,
    image: "/images/hero/gentle-senior-1.jpg",
    alt: "Gentle senior dog with wise, loving eyes",
    title: "Wisdom & Love Combined",
    subtitle: "Max offers the perfect blend of calm companionship and gentle affection",
    animation: "gentle",
    color: "from-green-400 to-emerald-500"
  }
]

export default function HomePage() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)
  const [isLoaded, setIsLoaded] = useState(false)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  useEffect(() => {
    if (!isPlaying) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [isPlaying])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault()
          prevSlide()
          break
        case 'ArrowRight':
          e.preventDefault()
          nextSlide()
          break
        case ' ':
          e.preventDefault()
          setIsPlaying(!isPlaying)
          break
        case 'Home':
          e.preventDefault()
          setCurrentSlide(0)
          break
        case 'End':
          e.preventDefault()
          setCurrentSlide(heroSlides.length - 1)
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isPlaying])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length)
  }

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe) {
      nextSlide()
    } else if (isRightSwipe) {
      prevSlide()
    }
  }
  return (
    <div className="flex flex-col">
      {/* Radiant Hero Carousel */}
      <section
        className="relative h-screen overflow-hidden"
        role="banner"
        aria-label="Pet adoption hero carousel"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Background Carousel */}
        <div className="absolute inset-0">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentSlide}
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 1, ease: "easeInOut" }}
              className="absolute inset-0"
            >
              {/* Placeholder gradient background for now */}
              <div className={cn(
                "absolute inset-0 bg-gradient-to-br",
                heroSlides[currentSlide].color,
                "opacity-20"
              )} />

              {/* Pet emoji as placeholder */}
              <div className="absolute inset-0 flex items-center justify-center">
                <motion.div
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 0.1 }}
                  transition={{ duration: 1, delay: 0.3 }}
                  className="text-[20rem] select-none"
                >
                  {currentSlide === 0 && "🐕"}
                  {currentSlide === 1 && "🐱"}
                  {currentSlide === 2 && "🐶"}
                  {currentSlide === 3 && "🐕‍🦺"}
                </motion.div>
              </div>

              {/* Overlay for text readability */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Floating Hearts Animation */}
        {isLoaded && (
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            <FloatingHearts />
          </div>
        )}

        {/* Content Overlay */}
        <div className="relative z-20 h-full flex items-center">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentSlide}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -30 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                  className="space-y-8"
                >
                  {/* Badge */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-md rounded-full px-6 py-3 border border-white/30"
                  >
                    <Heart className="h-5 w-5 text-white" />
                    <span className="text-white font-medium">Find Your Forever Friend</span>
                  </motion.div>

                  {/* Main Title */}
                  <motion.h1
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.4 }}
                    className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-white leading-tight px-4"
                    style={{
                      fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
                      textShadow: "0 4px 20px rgba(0,0,0,0.5)"
                    }}
                    role="heading"
                    aria-level={1}
                  >
                    {heroSlides[currentSlide].title}
                  </motion.h1>

                  {/* Subtitle */}
                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                    className="text-lg sm:text-xl md:text-2xl text-white/95 max-w-4xl mx-auto leading-relaxed px-4"
                    style={{ textShadow: "0 2px 10px rgba(0,0,0,0.4)" }}
                  >
                    {heroSlides[currentSlide].subtitle}
                  </motion.p>

                  {/* CTA Button */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.8 }}
                    className="pt-4"
                  >
                    <Link href="/pets">
                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Button
                          size="lg"
                          className={cn(
                            "relative overflow-hidden bg-white text-gray-900 hover:bg-white/90 text-lg px-12 py-6 rounded-full shadow-2xl border-0",
                            "before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent",
                            "before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700"
                          )}
                        >
                          <motion.div
                            animate={{
                              boxShadow: [
                                "0 0 20px rgba(255,255,255,0.5)",
                                "0 0 40px rgba(255,255,255,0.8)",
                                "0 0 20px rgba(255,255,255,0.5)"
                              ]
                            }}
                            transition={{ duration: 2, repeat: Infinity }}
                            className="flex items-center gap-3"
                          >
                            <Sparkles className="h-6 w-6" />
                            <span className="font-bold">Meet Our Pets</span>
                            <ArrowRight className="h-6 w-6" />
                          </motion.div>
                        </Button>
                      </motion.div>
                    </Link>
                  </motion.div>
                </motion.div>
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Carousel Controls */}
        <div className="absolute bottom-8 left-1/2 -translate-x-1/2 z-30">
          <div className="flex items-center gap-6">
            {/* Previous Button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={prevSlide}
              className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center border border-white/30 hover:bg-white/30 transition-colors"
              aria-label="Previous slide"
            >
              <ChevronLeft className="h-6 w-6 text-white" />
            </motion.button>

            {/* Slide Indicators */}
            <div className="flex gap-3" role="tablist" aria-label="Carousel slides">
              {heroSlides.map((_, index) => (
                <motion.button
                  key={index}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setCurrentSlide(index)}
                  className={cn(
                    "w-3 h-3 rounded-full transition-all duration-300",
                    index === currentSlide
                      ? "bg-white shadow-lg"
                      : "bg-white/40 hover:bg-white/60"
                  )}
                  role="tab"
                  aria-selected={index === currentSlide}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>

            {/* Next Button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={nextSlide}
              className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center border border-white/30 hover:bg-white/30 transition-colors"
              aria-label="Next slide"
            >
              <ChevronRight className="h-6 w-6 text-white" />
            </motion.button>

            {/* Play/Pause Button */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsPlaying(!isPlaying)}
              className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center border border-white/30 hover:bg-white/30 transition-colors ml-4"
              aria-label={isPlaying ? "Pause carousel" : "Play carousel"}
            >
              {isPlaying ? (
                <Pause className="h-5 w-5 text-white" />
              ) : (
                <Play className="h-5 w-5 text-white ml-0.5" />
              )}
            </motion.button>
          </div>
        </div>

        {/* Pet Animation Overlay */}
        <PetAnimations currentSlide={currentSlide} />
      </section>

      {/* Pet Categories */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Find Your New Best Friend
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Browse our available pets by category and find the perfect match for your family.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Link href="/pets?species=dog" className="group">
              <Card className="h-full transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-4 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Dog className="h-8 w-8 text-blue-600" />
                  </div>
                  <CardTitle className="text-2xl">Dogs</CardTitle>
                  <CardDescription>
                    Loyal companions ready for walks, play, and endless love.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">85</div>
                    <div className="text-sm text-gray-600">Available for adoption</div>
                  </div>
                </CardContent>
              </Card>
            </Link>

            <Link href="/pets?species=cat" className="group">
              <Card className="h-full transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-4 bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Cat className="h-8 w-8 text-purple-600" />
                  </div>
                  <CardTitle className="text-2xl">Cats</CardTitle>
                  <CardDescription>
                    Independent and affectionate feline friends seeking homes.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600 mb-2">62</div>
                    <div className="text-sm text-gray-600">Available for adoption</div>
                  </div>
                </CardContent>
              </Card>
            </Link>

            <Link href="/pets?species=other" className="group">
              <Card className="h-full transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-4 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                    <Rabbit className="h-8 w-8 text-green-600" />
                  </div>
                  <CardTitle className="text-2xl">Other Pets</CardTitle>
                  <CardDescription>
                    Rabbits, birds, and other wonderful animals looking for love.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">18</div>
                    <div className="text-sm text-gray-600">Available for adoption</div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              How Adoption Works
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our simple process makes it easy to find and adopt your new companion.
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="mx-auto mb-4 p-4 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center">
                <span className="text-2xl font-bold text-blue-600">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Browse Pets</h3>
              <p className="text-gray-600">
                Search our database of available pets and find ones that match your lifestyle.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-4 p-4 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                <span className="text-2xl font-bold text-green-600">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Apply Online</h3>
              <p className="text-gray-600">
                Fill out our comprehensive adoption application with your information and preferences.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-4 p-4 bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center">
                <span className="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Meet & Greet</h3>
              <p className="text-gray-600">
                Schedule a visit to meet your potential new family member in person.
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-4 p-4 bg-red-100 rounded-full w-16 h-16 flex items-center justify-center">
                <span className="text-2xl font-bold text-red-600">4</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Take Home</h3>
              <p className="text-gray-600">
                Complete the adoption process and welcome your new companion home!
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Success Stories
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Read about the amazing transformations and happy endings that make our work worthwhile.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <Card className="overflow-hidden">
              <div className="aspect-video bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                <span className="text-4xl">🐕</span>
              </div>
              <CardHeader>
                <CardTitle className="text-lg">Max's Journey to Therapy Dog</CardTitle>
                <CardDescription>
                  From scared shelter dog to certified therapy animal helping children with autism.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Max arrived at our shelter traumatized and fearful. Today, he works at a local autism center,
                  providing comfort and helping children develop social skills.
                </p>
                <Link href="/blog/3">
                  <Button variant="outline" size="sm" className="w-full">
                    Read Max's Story
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <div className="aspect-video bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center">
                <span className="text-4xl">🐱</span>
              </div>
              <CardHeader>
                <CardTitle className="text-lg">Luna's Second Chance</CardTitle>
                <CardDescription>
                  A senior cat finds her perfect retirement home with a loving elderly couple.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  At 12 years old, Luna was overlooked by many adopters. But the perfect family saw past her age
                  and gave her the loving home she deserved.
                </p>
                <Link href="/blog">
                  <Button variant="outline" size="sm" className="w-full">
                    Read Luna's Story
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <div className="aspect-video bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center">
                <span className="text-4xl">🐰</span>
              </div>
              <CardHeader>
                <CardTitle className="text-lg">Buddy's Big Family</CardTitle>
                <CardDescription>
                  A bonded pair of rabbits finds their forever home with a family of five.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Buddy and his companion Clover needed to be adopted together. The Johnson family
                  opened their hearts and home to both bunnies.
                </p>
                <Link href="/blog">
                  <Button variant="outline" size="sm" className="w-full">
                    Read Buddy's Story
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>

          <div className="text-center">
            <Link href="/blog?category=Success Stories">
              <Button variant="outline" size="lg">
                View All Success Stories
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              What Our Adopters Say
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Hear from families who have found their perfect companions through our adoption program.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="relative">
              <CardContent className="pt-6">
                <Quote className="h-8 w-8 text-blue-500 mb-4" />
                <p className="text-gray-700 mb-4">
                  "Adopting Bella was the best decision we ever made. She's brought so much joy and laughter
                  to our home. The adoption process was smooth and the staff was incredibly helpful."
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-blue-600 font-semibold">SM</span>
                  </div>
                  <div>
                    <div className="font-semibold">Sarah Martinez</div>
                    <div className="text-sm text-gray-600">Adopted Bella (Golden Retriever)</div>
                  </div>
                </div>
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="relative">
              <CardContent className="pt-6">
                <Quote className="h-8 w-8 text-purple-500 mb-4" />
                <p className="text-gray-700 mb-4">
                  "The team helped us find the perfect cat for our family. Whiskers has been amazing with
                  our kids and fits right into our routine. Couldn't be happier!"
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-purple-600 font-semibold">MJ</span>
                  </div>
                  <div>
                    <div className="font-semibold">Michael Johnson</div>
                    <div className="text-sm text-gray-600">Adopted Whiskers (Maine Coon)</div>
                  </div>
                </div>
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="relative">
              <CardContent className="pt-6">
                <Quote className="h-8 w-8 text-green-500 mb-4" />
                <p className="text-gray-700 mb-4">
                  "As a first-time pet owner, I was nervous about adoption. The staff provided excellent
                  guidance and support. Charlie and I are inseparable now!"
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-green-600 font-semibold">EL</span>
                  </div>
                  <div>
                    <div className="font-semibold">Emily Lee</div>
                    <div className="text-sm text-gray-600">Adopted Charlie (Beagle Mix)</div>
                  </div>
                </div>
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="relative">
              <CardContent className="pt-6">
                <Quote className="h-8 w-8 text-red-500 mb-4" />
                <p className="text-gray-700 mb-4">
                  "We adopted two senior cats and they've brought such peace to our home. The adoption
                  team made sure we were prepared for their special needs."
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-red-600 font-semibold">DT</span>
                  </div>
                  <div>
                    <div className="font-semibold">David Thompson</div>
                    <div className="text-sm text-gray-600">Adopted Milo & Ginger (Senior Cats)</div>
                  </div>
                </div>
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="relative">
              <CardContent className="pt-6">
                <Quote className="h-8 w-8 text-indigo-500 mb-4" />
                <p className="text-gray-700 mb-4">
                  "The foster-to-adopt program was perfect for us. We got to know Ruby before committing,
                  and now she's officially part of our family!"
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-indigo-600 font-semibold">LW</span>
                  </div>
                  <div>
                    <div className="font-semibold">Lisa Wang</div>
                    <div className="text-sm text-gray-600">Adopted Ruby (Border Collie)</div>
                  </div>
                </div>
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="relative">
              <CardContent className="pt-6">
                <Quote className="h-8 w-8 text-pink-500 mb-4" />
                <p className="text-gray-700 mb-4">
                  "Adopting from this shelter was an amazing experience. The staff truly cares about
                  matching pets with the right families. Oscar is living his best life!"
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-pink-600 font-semibold">JR</span>
                  </div>
                  <div>
                    <div className="font-semibold">Jennifer Rodriguez</div>
                    <div className="text-sm text-gray-600">Adopted Oscar (German Shepherd)</div>
                  </div>
                </div>
                <div className="flex mt-3">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Ways to Help */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Ways to Help
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Not ready to adopt? There are many ways you can help animals in need.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-4 bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center">
                  <HomeIcon className="h-8 w-8 text-yellow-600" />
                </div>
                <CardTitle>Foster</CardTitle>
                <CardDescription>
                  Provide temporary homes for pets in need of extra care and attention.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/foster">
                  <Button variant="outline" className="w-full">
                    Learn About Fostering
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-4 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                  <Users className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle>Volunteer</CardTitle>
                <CardDescription>
                  Help with daily care, events, transport, and other essential activities.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/volunteer">
                  <Button variant="outline" className="w-full">
                    Volunteer With Us
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 p-4 bg-red-100 rounded-full w-16 h-16 flex items-center justify-center">
                  <Heart className="h-8 w-8 text-red-600" />
                </div>
                <CardTitle>Donate</CardTitle>
                <CardDescription>
                  Support our mission with financial contributions for food, medical care, and shelter.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/donate">
                  <Button variant="outline" className="w-full">
                    Make a Donation
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Ready to Change a Life?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Every pet deserves a loving home. Start your adoption journey today and discover the joy of unconditional love.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/pets">
              <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                Browse Available Pets
              </Button>
            </Link>
            <Link href="/auth/signup">
              <Button size="lg" variant="outline" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-blue-600">
                Create Account
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}

// Floating Hearts Animation Component
function FloatingHearts() {
  const [hearts, setHearts] = useState<Array<{
    id: number
    x: number
    y: number
    delay: number
    duration: number
    size: number
  }>>([])

  useEffect(() => {
    const heartData = Array.from({ length: 12 }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: window.innerHeight + 50,
      delay: Math.random() * 4,
      duration: 8 + Math.random() * 4,
      size: 20 + Math.random() * 20
    }))
    setHearts(heartData)
  }, [])

  return (
    <>
      {hearts.map((heart) => (
        <motion.div
          key={heart.id}
          className="absolute pointer-events-none"
          initial={{
            x: heart.x,
            y: heart.y,
            opacity: 0,
            scale: 0
          }}
          animate={{
            y: -100,
            opacity: [0, 0.6, 0.8, 0.6, 0],
            scale: [0, 1, 1.2, 1, 0],
            rotate: [0, 10, -10, 5, 0]
          }}
          transition={{
            duration: heart.duration,
            repeat: Infinity,
            delay: heart.delay,
            ease: "easeOut"
          }}
          style={{ fontSize: heart.size }}
        >
          💕
        </motion.div>
      ))}
    </>
  )
}

// Pet Animations Component
interface PetAnimationsProps {
  currentSlide: number
}

function PetAnimations({ currentSlide }: PetAnimationsProps) {
  const getAnimation = () => {
    switch (currentSlide) {
      case 0: // Wagging tail
        return (
          <motion.div
            className="absolute bottom-20 right-20 text-6xl"
            animate={{
              rotate: [0, 15, -15, 0],
              scale: [1, 1.1, 1]
            }}
            transition={{
              duration: 0.8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            🐕
          </motion.div>
        )
      case 1: // Curious paw swipe
        return (
          <motion.div
            className="absolute top-1/3 right-16 text-5xl"
            animate={{
              x: [0, 20, 0],
              rotate: [0, -10, 10, 0]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            🐾
          </motion.div>
        )
      case 2: // Bouncing
        return (
          <motion.div
            className="absolute bottom-32 left-20 text-5xl"
            animate={{
              y: [0, -30, 0],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            🎾
          </motion.div>
        )
      case 3: // Gentle movement
        return (
          <motion.div
            className="absolute top-1/4 left-16 text-4xl"
            animate={{
              y: [0, -10, 0],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            ✨
          </motion.div>
        )
      default:
        return null
    }
  }

  return (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          {getAnimation()}
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
