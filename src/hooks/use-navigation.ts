"use client"

import { useState, useEffect, useCallback } from "react"
import { usePathname } from "next/navigation"

interface NavigationState {
  isMobileMenuOpen: boolean
  activeDropdown: string | null
  isSearchFocused: boolean
  scrolled: boolean
}

export function useNavigation() {
  const pathname = usePathname()
  const [state, setState] = useState<NavigationState>({
    isMobileMenuOpen: false,
    activeDropdown: null,
    isSearchFocused: false,
    scrolled: false
  })

  // Close mobile menu when route changes
  useEffect(() => {
    setState(prev => ({
      ...prev,
      isMobileMenuOpen: false,
      activeDropdown: null
    }))
  }, [pathname])

  // Handle scroll for header styling
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY
      setState(prev => ({
        ...prev,
        scrolled: scrollPosition > 10
      }))
    }

    window.addEventListener("scroll", handleScroll, { passive: true })
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Close mobile menu on escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        setState(prev => ({
          ...prev,
          isMobileMenuOpen: false,
          activeDropdown: null
        }))
      }
    }

    if (state.isMobileMenuOpen) {
      document.addEventListener("keydown", handleKeyDown)
      // Prevent body scroll when mobile menu is open
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "unset"
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown)
      document.body.style.overflow = "unset"
    }
  }, [state.isMobileMenuOpen])

  const toggleMobileMenu = useCallback(() => {
    setState(prev => ({
      ...prev,
      isMobileMenuOpen: !prev.isMobileMenuOpen,
      activeDropdown: null
    }))
  }, [])

  const closeMobileMenu = useCallback(() => {
    setState(prev => ({
      ...prev,
      isMobileMenuOpen: false
    }))
  }, [])

  const setActiveDropdown = useCallback((dropdown: string | null) => {
    setState(prev => ({
      ...prev,
      activeDropdown: dropdown
    }))
  }, [])

  const setSearchFocused = useCallback((focused: boolean) => {
    setState(prev => ({
      ...prev,
      isSearchFocused: focused
    }))
  }, [])

  return {
    ...state,
    toggleMobileMenu,
    closeMobileMenu,
    setActiveDropdown,
    setSearchFocused
  }
}
