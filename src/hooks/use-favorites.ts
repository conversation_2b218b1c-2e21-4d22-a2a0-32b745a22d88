"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"

interface UseFavoritesReturn {
  favorites: Set<string>
  loading: boolean
  toggleFavorite: (petId: string) => Promise<boolean>
  isFavorited: (petId: string) => boolean
}

export function useFavorites(): UseFavoritesReturn {
  const { data: session } = useSession()
  const [favorites, setFavorites] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(false)

  // Fetch user's favorites when session is available
  useEffect(() => {
    if (session?.user) {
      fetchFavorites()
    } else {
      setFavorites(new Set())
    }
  }, [session])

  const fetchFavorites = async () => {
    if (!session?.user) return

    setLoading(true)
    try {
      const response = await fetch('/api/favorites')
      if (response.ok) {
        const data = await response.json()
        const favoriteIds = new Set(data.favorites.map((fav: any) => fav.pet.id))
        setFavorites(favoriteIds)
      }
    } catch (error) {
      console.error('Error fetching favorites:', error)
    } finally {
      setLoading(false)
    }
  }

  const toggleFavorite = async (petId: string): Promise<boolean> => {
    if (!session?.user) {
      return false
    }

    const isCurrentlyFavorited = favorites.has(petId)
    
    try {
      const response = await fetch(`/api/pets/${petId}/favorite`, {
        method: isCurrentlyFavorited ? 'DELETE' : 'POST',
      })

      if (response.ok) {
        setFavorites(prev => {
          const newFavorites = new Set(prev)
          if (isCurrentlyFavorited) {
            newFavorites.delete(petId)
          } else {
            newFavorites.add(petId)
          }
          return newFavorites
        })
        return !isCurrentlyFavorited
      }
      return isCurrentlyFavorited
    } catch (error) {
      console.error('Error toggling favorite:', error)
      return isCurrentlyFavorited
    }
  }

  const isFavorited = (petId: string): boolean => {
    return favorites.has(petId)
  }

  return {
    favorites,
    loading,
    toggleFavorite,
    isFavorited,
  }
}
