"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Heart, Quote } from "lucide-react"

interface AdoptionStory {
  id: number
  petName: string
  petEmoji: string
  adopter: string
  story: string
  timeAgo: string
  location: string
}

const adoptionStories: AdoptionStory[] = [
  {
    id: 1,
    pet<PERSON><PERSON>: "<PERSON>",
    pet<PERSON><PERSON>ji: "🐱",
    adopter: "<PERSON>",
    story: "<PERSON> found her forever home and now rules the house with her gentle purrs and playful spirit!",
    timeAgo: "2 days ago",
    location: "San Francisco, CA"
  },
  {
    id: 2,
    petName: "<PERSON>",
    pet<PERSON><PERSON>ji: "🐕",
    adopter: "The Johnson Family",
    story: "<PERSON> is living his best life with three kids who adore him. Daily walks and endless belly rubs!",
    timeAgo: "1 week ago",
    location: "Austin, TX"
  },
  {
    id: 3,
    petName: "Whiskers",
    petEmoji: "🐰",
    adopter: "<PERSON> <PERSON> <PERSON>",
    story: "<PERSON>his<PERSON> has transformed from a shy bunny to the most confident little explorer in his new garden home.",
    timeAgo: "3 days ago",
    location: "Portland, OR"
  },
  {
    id: 4,
    pet<PERSON><PERSON>: "<PERSON>",
    pet<PERSON><PERSON><PERSON>: "🐕",
    adopter: "<PERSON>",
    story: "<PERSON> and <PERSON> are inseparable hiking buddies now. She's brought so much joy and adventure to my life!",
    timeAgo: "5 days ago",
    location: "Denver, CO"
  },
  {
    id: 5,
    petName: "Mittens",
    petEmoji: "🐱",
    adopter: "The Chen Family",
    story: "Mittens has become the perfect therapy cat for our elderly grandmother. Pure magic and love!",
    timeAgo: "1 week ago",
    location: "Seattle, WA"
  }
]

export function AdoptionStoriesCarousel() {
  const [currentStory, setCurrentStory] = useState(0)
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const interval = setInterval(() => {
      setIsVisible(false)
      setTimeout(() => {
        setCurrentStory((prev) => (prev + 1) % adoptionStories.length)
        setIsVisible(true)
      }, 300)
    }, 6000) // Change story every 6 seconds

    return () => clearInterval(interval)
  }, [])

  const story = adoptionStories[currentStory]

  return (
    <div className="w-full max-w-sm mx-auto">
      <motion.div
        className="bg-gradient-to-br from-pink-50 to-purple-50 rounded-2xl p-6 border border-pink-100 shadow-sm"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* Header */}
        <div className="flex items-center space-x-2 mb-4">
          <motion.div
            animate={{ rotate: [0, 10, -10, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          >
            <Heart className="h-5 w-5 text-pink-500" />
          </motion.div>
          <h3 className="text-sm font-semibold text-gray-700">Happy Tails</h3>
          <motion.div
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          >
            ✨
          </motion.div>
        </div>

        {/* Story Content */}
        <AnimatePresence mode="wait">
          {isVisible && (
            <motion.div
              key={story.id}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.4 }}
              className="space-y-3"
            >
              {/* Pet and Adopter Info */}
              <div className="flex items-center space-x-3">
                <motion.div
                  className="text-2xl"
                  animate={{ 
                    rotate: [0, 5, -5, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ 
                    duration: 3, 
                    repeat: Infinity, 
                    ease: "easeInOut" 
                  }}
                >
                  {story.petEmoji}
                </motion.div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-gray-800 text-sm">
                      {story.petName}
                    </span>
                    <span className="text-xs text-pink-600">
                      with {story.adopter}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 text-xs text-gray-500">
                    <span>{story.location}</span>
                    <span>•</span>
                    <span>{story.timeAgo}</span>
                  </div>
                </div>
              </div>

              {/* Story Text */}
              <div className="relative">
                <Quote className="absolute -top-1 -left-1 h-4 w-4 text-pink-300" />
                <p className="text-sm text-gray-600 leading-relaxed pl-4 italic">
                  {story.story}
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Progress Indicators */}
        <div className="flex justify-center space-x-2 mt-4">
          {adoptionStories.map((_, index) => (
            <motion.button
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentStory 
                  ? "bg-pink-500 scale-125" 
                  : "bg-pink-200 hover:bg-pink-300"
              }`}
              onClick={() => {
                setIsVisible(false)
                setTimeout(() => {
                  setCurrentStory(index)
                  setIsVisible(true)
                }, 300)
              }}
              whileHover={{ scale: 1.3 }}
              whileTap={{ scale: 0.9 }}
              aria-label={`View story ${index + 1}`}
            />
          ))}
        </div>

        {/* Floating hearts decoration */}
        <div className="absolute top-2 right-2 pointer-events-none">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute text-pink-300 text-xs"
              style={{
                right: `${i * 8}px`,
                top: `${i * 6}px`,
              }}
              animate={{
                y: [0, -5, 0],
                opacity: [0.3, 0.7, 0.3],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.5,
                ease: "easeInOut",
              }}
            >
              💕
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Call to Action */}
      <motion.div
        className="text-center mt-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8 }}
      >
        <p className="text-xs text-gray-500">
          Join thousands of happy families! 🏠✨
        </p>
      </motion.div>
    </div>
  )
}
