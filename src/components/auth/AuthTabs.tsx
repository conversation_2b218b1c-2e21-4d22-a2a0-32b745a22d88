"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Heart, UserPlus, LogIn } from "lucide-react"
import { cn } from "@/lib/utils"

interface AuthTabsProps {
  activeTab: "signin" | "signup"
  onTabChange: (tab: "signin" | "signup") => void
  children: React.ReactNode
}

// Animated background for active tab
const TabBackground = () => (
  <motion.div
    className="absolute inset-0 bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500 rounded-xl shadow-lg"
    layoutId="activeTab"
    transition={{
      type: "spring",
      stiffness: 300,
      damping: 30,
    }}
  />
)

// Floating paw prints that follow the active tab
const FloatingPaws = ({ position }: { position: "left" | "right" }) => (
  <div className="absolute inset-0 pointer-events-none overflow-hidden">
    {[...Array(3)].map((_, i) => (
      <motion.div
        key={i}
        className="absolute text-pink-300 text-xs"
        style={{
          [position]: `${10 + i * 15}px`,
          top: `${20 + i * 10}px`,
        }}
        animate={{
          y: [0, -5, 0],
          opacity: [0.3, 0.8, 0.3],
          rotate: [0, 10, -10, 0],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          delay: i * 0.3,
          ease: "easeInOut",
        }}
      >
        🐾
      </motion.div>
    ))}
  </div>
)

export function AuthTabs({ activeTab, onTabChange, children }: AuthTabsProps) {
  const [hoveredTab, setHoveredTab] = useState<string | null>(null)

  const tabs = [
    {
      id: "signin",
      label: "Paws In",
      icon: LogIn,
      description: "Welcome back, friend!",
    },
    {
      id: "signup",
      label: "Join the Pack",
      icon: UserPlus,
      description: "Start your journey!",
    },
  ] as const

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="relative">
        {/* Background container */}
        <div className="bg-gray-100/80 backdrop-blur-sm rounded-2xl p-2 relative overflow-hidden">
          {/* Floating paws decoration */}
          <FloatingPaws position={activeTab === "signin" ? "left" : "right"} />
          
          <div className="relative flex space-x-2">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const isActive = activeTab === tab.id
              const isHovered = hoveredTab === tab.id

              return (
                <motion.button
                  key={tab.id}
                  className={cn(
                    "relative flex-1 px-6 py-4 rounded-xl font-semibold text-sm transition-all duration-300",
                    "focus:outline-none focus:ring-2 focus:ring-pink-300 focus:ring-offset-2",
                    isActive
                      ? "text-white shadow-lg"
                      : "text-gray-600 hover:text-gray-800"
                  )}
                  onClick={() => onTabChange(tab.id)}
                  onMouseEnter={() => setHoveredTab(tab.id)}
                  onMouseLeave={() => setHoveredTab(null)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  aria-pressed={isActive}
                  role="tab"
                >
                  {/* Active tab background */}
                  {isActive && <TabBackground />}
                  
                  {/* Tab content */}
                  <div className="relative z-10 flex flex-col items-center space-y-1">
                    <div className="flex items-center space-x-2">
                      <motion.div
                        animate={isActive ? {
                          rotate: [0, 10, -10, 0],
                          scale: [1, 1.1, 1],
                        } : {}}
                        transition={{
                          duration: 2,
                          repeat: isActive ? Infinity : 0,
                          ease: "easeInOut",
                        }}
                      >
                        <Icon className="h-5 w-5" />
                      </motion.div>
                      <span>{tab.label}</span>
                    </div>
                    
                    {/* Tab description */}
                    <motion.span
                      className={cn(
                        "text-xs transition-opacity duration-300",
                        isActive ? "text-white/80" : "text-gray-500"
                      )}
                      animate={isHovered || isActive ? { opacity: 1 } : { opacity: 0.7 }}
                    >
                      {tab.description}
                    </motion.span>
                  </div>

                  {/* Hover sparkles */}
                  <AnimatePresence>
                    {isHovered && !isActive && (
                      <div className="absolute inset-0 pointer-events-none">
                        {[...Array(4)].map((_, i) => (
                          <motion.div
                            key={i}
                            className="absolute text-pink-400 text-xs"
                            style={{
                              left: `${20 + i * 20}%`,
                              top: `${30 + (i % 2) * 40}%`,
                            }}
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{
                              opacity: [0, 1, 0],
                              scale: [0, 1, 0],
                              rotate: [0, 180, 360],
                            }}
                            exit={{ opacity: 0, scale: 0 }}
                            transition={{
                              duration: 1,
                              delay: i * 0.1,
                            }}
                          >
                            ✨
                          </motion.div>
                        ))}
                      </div>
                    )}
                  </AnimatePresence>
                </motion.button>
              )
            })}
          </div>
        </div>

        {/* Decorative hearts */}
        <div className="absolute -top-2 -right-2 pointer-events-none">
          <motion.div
            animate={{
              rotate: 360,
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            className="text-pink-400"
          >
            💕
          </motion.div>
        </div>
        
        <div className="absolute -bottom-2 -left-2 pointer-events-none">
          <motion.div
            animate={{
              rotate: -360,
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            className="text-purple-400"
          >
            🌟
          </motion.div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="relative">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: activeTab === "signin" ? -20 : 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: activeTab === "signin" ? 20 : -20 }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 30,
            }}
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Progress indicator */}
      <div className="flex justify-center space-x-2">
        {tabs.map((tab) => (
          <motion.div
            key={tab.id}
            className={cn(
              "w-2 h-2 rounded-full transition-all duration-300",
              activeTab === tab.id ? "bg-pink-500 scale-125" : "bg-gray-300"
            )}
            whileHover={{ scale: 1.3 }}
          />
        ))}
      </div>
    </div>
  )
}
