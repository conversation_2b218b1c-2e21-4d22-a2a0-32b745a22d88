"use client"

import { useState } from "react"
import { signIn } from "next-auth/react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { toast } from "react-hot-toast"
import { MagicalAuthLayout } from "./MagicalAuthLayout"
import { AuthTabs } from "./AuthTabs"
import { PetInputField } from "./PetInputField"
import { SparklingButton } from "./SparklingButton"
import { AdoptionStoriesCarousel } from "./AdoptionStoriesCarousel"

interface FormData {
  name?: string
  email: string
  password: string
  confirmPassword?: string
}

interface FormErrors {
  name?: string
  email?: string
  password?: string
  confirmPassword?: string
}

export function SpellbindingAuth() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState<"signin" | "signup">("signin")
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<FormData>({
    email: "",
    password: "",
  })
  const [errors, setErrors] = useState<FormErrors>({})

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required"
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email"
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "Password is required"
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters"
    }

    // Sign up specific validations
    if (activeTab === "signup") {
      if (!formData.name) {
        newErrors.name = "Name is required"
      }

      if (!formData.confirmPassword) {
        newErrors.confirmPassword = "Please confirm your password"
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = "Passwords don't match"
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error("Please fix the errors below")
      return
    }

    setLoading(true)

    try {
      if (activeTab === "signin") {
        // Sign in
        const result = await signIn("credentials", {
          email: formData.email,
          password: formData.password,
          redirect: false,
        })

        if (result?.error) {
          toast.error("Invalid email or password")
        } else {
          toast.success("Welcome back! 🎉")
          router.push("/dashboard")
        }
      } else {
        // Sign up
        const response = await fetch("/api/auth/signup", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            name: formData.name,
            email: formData.email,
            password: formData.password,
          }),
        })

        if (response.ok) {
          toast.success("Account created! Welcome to the pack! 🐾")
          // Auto sign in after successful signup
          await signIn("credentials", {
            email: formData.email,
            password: formData.password,
            redirect: false,
          })
          router.push("/dashboard")
        } else {
          const data = await response.json()
          toast.error(data.error || "Something went wrong")
        }
      }
    } catch (error) {
      toast.error("Something went wrong. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof FormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const handleTabChange = (tab: "signin" | "signup") => {
    setActiveTab(tab)
    setErrors({})
    // Reset form data when switching tabs
    setFormData({
      email: "",
      password: "",
      ...(tab === "signup" && { name: "", confirmPassword: "" }),
    })
  }

  return (
    <MagicalAuthLayout
      title={activeTab === "signin" ? "Welcome Back!" : "Join Our Pack!"}
      subtitle={
        activeTab === "signin"
          ? "Sign in to continue your pet adoption journey"
          : "Create an account to start finding your perfect companion"
      }
    >
      <div className="grid lg:grid-cols-2 gap-8 items-start">
        {/* Main Form */}
        <div className="space-y-6">
          <AuthTabs
            activeTab={activeTab}
            onTabChange={handleTabChange}
          >
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Name field for signup */}
              {activeTab === "signup" && (
                <PetInputField
                  label="Full Name"
                  icon="user"
                  placeholder="Enter your full name"
                  value={formData.name || ""}
                  onChange={handleInputChange("name")}
                  error={errors.name}
                  required
                />
              )}

              {/* Email field */}
              <PetInputField
                label="Email Address"
                icon="mail"
                type="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleInputChange("email")}
                error={errors.email}
                required
              />

              {/* Password field */}
              <PetInputField
                label="Password"
                icon="lock"
                type="password"
                placeholder="Enter your password"
                value={formData.password}
                onChange={handleInputChange("password")}
                error={errors.password}
                showPasswordToggle
                required
              />

              {/* Confirm password for signup */}
              {activeTab === "signup" && (
                <PetInputField
                  label="Confirm Password"
                  icon="lock"
                  type="password"
                  placeholder="Confirm your password"
                  value={formData.confirmPassword || ""}
                  onChange={handleInputChange("confirmPassword")}
                  error={errors.confirmPassword}
                  required
                />
              )}

              {/* Submit button */}
              <SparklingButton
                type="submit"
                variant="primary"
                size="lg"
                loading={loading}
                className="w-full"
                sparkleOnHover
              >
                {activeTab === "signin" ? "Paws In" : "Join the Pack"}
              </SparklingButton>

              {/* Alternative actions */}
              <div className="text-center space-y-2">
                {activeTab === "signin" ? (
                  <p className="text-sm text-gray-600">
                    New to our pack?{" "}
                    <button
                      type="button"
                      onClick={() => handleTabChange("signup")}
                      className="text-pink-600 hover:text-pink-700 font-medium transition-colors"
                    >
                      Join us today!
                    </button>
                  </p>
                ) : (
                  <p className="text-sm text-gray-600">
                    Already part of the pack?{" "}
                    <button
                      type="button"
                      onClick={() => handleTabChange("signin")}
                      className="text-pink-600 hover:text-pink-700 font-medium transition-colors"
                    >
                      Sign in here
                    </button>
                  </p>
                )}
                
                {activeTab === "signin" && (
                  <p className="text-xs text-gray-500">
                    <button
                      type="button"
                      className="text-pink-600 hover:text-pink-700 transition-colors"
                      onClick={() => toast.info("Password reset coming soon! 🔄")}
                    >
                      Forgot your password?
                    </button>
                  </p>
                )}
              </div>
            </form>
          </AuthTabs>
        </div>

        {/* Adoption Stories Sidebar */}
        <div className="hidden lg:block">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <AdoptionStoriesCarousel />
          </motion.div>
        </div>
      </div>

      {/* Mobile adoption stories */}
      <div className="lg:hidden mt-8">
        <AdoptionStoriesCarousel />
      </div>
    </MagicalAuthLayout>
  )
}
