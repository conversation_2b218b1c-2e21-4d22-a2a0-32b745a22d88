"use client"

import { useState } from "react"
import { signIn } from "next-auth/react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { toast } from "react-hot-toast"
import Link from "next/link"
import { MagicalAuthLayout } from "./MagicalAuthLayout"
import { PetInputField } from "./PetInputField"
import { SparklingButton } from "./SparklingButton"
import { AdoptionStoriesCarousel } from "./AdoptionStoriesCarousel"

interface FormData {
  email: string
  password: string
}

interface FormErrors {
  email?: string
  password?: string
}

export function SignInForm() {
  const [formData, setFormData] = useState<FormData>({
    email: "",
    password: "",
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.email) {
      newErrors.email = "Email is required"
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email"
    }

    if (!formData.password) {
      newErrors.password = "Password is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)
    try {
      const result = await signIn("credentials", {
        email: formData.email,
        password: formData.password,
        redirect: false,
      })

      if (result?.error) {
        toast.error("Invalid email or password")
      } else {
        toast.success("Welcome back! 🐾")
        router.push("/dashboard")
        router.refresh()
      }
    } catch (error) {
      toast.error("Something went wrong. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof FormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  return (
    <MagicalAuthLayout
      title="Welcome Back!"
      subtitle="Sign in to continue your pet adoption journey"
    >
      <div className="grid lg:grid-cols-2 gap-12 items-start">
        {/* Main Form */}
        <div className="space-y-8">
          {/* Header */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h1 className="text-4xl font-bold bg-gradient-to-r from-pink-600 via-purple-600 to-blue-600 bg-clip-text text-transparent mb-4">
              Paws In
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-lg">
              Welcome back to your pet adoption journey! 🐾
            </p>
          </motion.div>

          {/* Form Container */}
          <motion.div
            className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-3xl shadow-xl border border-white/20 dark:border-gray-700/20 p-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <form onSubmit={handleSubmit} className="space-y-6" autoComplete="on">
              {/* Email field */}
              <PetInputField
                label="Email Address"
                icon="mail"
                type="email"
                name="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleInputChange("email")}
                error={errors.email}
                required
              />

              {/* Password field */}
              <PetInputField
                label="Password"
                icon="lock"
                type="password"
                name="password"
                placeholder="Enter your password"
                value={formData.password}
                onChange={handleInputChange("password")}
                error={errors.password}
                showPasswordToggle
                required
              />

              {/* Submit button */}
              <SparklingButton
                type="submit"
                variant="primary"
                size="lg"
                loading={loading}
                className="w-full"
                sparkleOnHover
              >
                Sign In
              </SparklingButton>

              {/* Alternative actions */}
              <div className="text-center space-y-3">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  New to our pack?{" "}
                  <Link
                    href="/auth/signup"
                    className="text-pink-600 hover:text-pink-700 dark:text-pink-400 dark:hover:text-pink-300 font-medium transition-colors"
                  >
                    Join us today!
                  </Link>
                </p>

                <p className="text-xs text-gray-500 dark:text-gray-400">
                  <Link
                    href="/auth/forgot-password"
                    className="text-pink-600 hover:text-pink-700 dark:text-pink-400 dark:hover:text-pink-300 transition-colors"
                  >
                    Forgot your password?
                  </Link>
                </p>
              </div>
            </form>
          </motion.div>
        </div>

        {/* Adoption Stories Sidebar */}
        <div className="hidden lg:block">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <AdoptionStoriesCarousel />
          </motion.div>
        </div>
      </div>

      {/* Mobile adoption stories */}
      <div className="lg:hidden mt-8">
        <AdoptionStoriesCarousel />
      </div>
    </MagicalAuthLayout>
  )
}
