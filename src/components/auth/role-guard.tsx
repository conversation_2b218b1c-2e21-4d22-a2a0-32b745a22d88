"use client"

import { useSession } from "next-auth/react"
import { UserRole } from "@prisma/client"
import { PermissionManager, Permission } from "@/lib/permissions"
import { ReactNode } from "react"

interface RoleGuardProps {
  children: ReactNode
  allowedRoles?: UserRole[]
  requiredPermissions?: Permission[]
  fallback?: ReactNode
  requireAll?: boolean // If true, user must have ALL permissions, if false, ANY permission
}

/**
 * Component that conditionally renders children based on user role or permissions
 */
export function RoleGuard({
  children,
  allowedRoles = [],
  requiredPermissions = [],
  fallback = null,
  requireAll = false
}: RoleGuardProps) {
  const { data: session, status } = useSession()

  // Show nothing while loading
  if (status === "loading") {
    return null
  }

  // Show fallback if not authenticated
  if (status === "unauthenticated") {
    return <div>{fallback}</div>
  }

  const userRole = session?.user?.role as UserRole

  // Check role-based access
  if (allowedRoles.length > 0) {
    if (!allowedRoles.includes(userRole)) {
      return <div>{fallback}</div>
    }
  }

  // Check permission-based access
  if (requiredPermissions.length > 0) {
    const hasAccess = requireAll
      ? PermissionManager.hasAllPermissions(userRole, requiredPermissions)
      : PermissionManager.hasAnyPermission(userRole, requiredPermissions)

    if (!hasAccess) {
      return <div>{fallback}</div>
    }
  }

  return <div>{children}</div>
}

/**
 * Hook to check if current user has specific roles
 */
export function useHasRole(roles: UserRole[]): boolean {
  const { data: session, status } = useSession()

  if (status !== "authenticated" || !session?.user?.role) {
    return false
  }

  return roles.includes(session.user.role as UserRole)
}

/**
 * Hook to check if current user has specific permissions
 */
export function useHasPermission(permissions: Permission[], requireAll = false): boolean {
  const { data: session, status } = useSession()

  if (status !== "authenticated" || !session?.user?.role) {
    return false
  }

  const userRole = session.user.role as UserRole

  return requireAll
    ? PermissionManager.hasAllPermissions(userRole, permissions)
    : PermissionManager.hasAnyPermission(userRole, permissions)
}

/**
 * Hook to check if current user has any admin access
 */
export function useIsAdmin(): boolean {
  return useHasRole([UserRole.ADMIN])
}

/**
 * Hook to check if current user has staff or admin access
 */
export function useIsStaff(): boolean {
  return useHasRole([UserRole.STAFF, UserRole.ADMIN])
}

/**
 * Hook to get current user's role
 */
export function useUserRole(): UserRole | null {
  const { data: session, status } = useSession()

  if (status !== "authenticated" || !session?.user?.role) {
    return null
  }

  return session.user.role as UserRole
}

/**
 * Hook to get all permissions for current user
 */
export function useUserPermissions(): Permission[] {
  const { data: session, status } = useSession()

  if (status !== "authenticated" || !session?.user?.role) {
    return []
  }

  return PermissionManager.getRolePermissions(session.user.role as UserRole)
}

// Convenience components for common role checks

export function AdminOnly({ children, fallback = null }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={[UserRole.ADMIN]} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function StaffOnly({ children, fallback = null }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={[UserRole.STAFF, UserRole.ADMIN]} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function AuthenticatedOnly({ children, fallback = null }: { children: ReactNode; fallback?: ReactNode }) {
  const { status } = useSession()
  
  if (status === "loading") {
    return null
  }
  
  if (status === "unauthenticated") {
    return <div>{fallback}</div>
  }

  return <div>{children}</div>
}

// Higher-order component for role-based access
export function withRoleGuard<P extends object>(
  Component: React.ComponentType<P>,
  guardProps: Omit<RoleGuardProps, 'children'>
) {
  return function GuardedComponent(props: P) {
    return (
      <RoleGuard {...guardProps}>
        <Component {...props} />
      </RoleGuard>
    )
  }
}
