"use client"

import { useState, useRef, useEffect } from "react"
import Link from "next/link"
import {
  ChevronDown,
  Heart,
  Users,
  BookOpen,
  Calendar,
  DollarSign,
  Info,
  Phone,
  CheckSquare,
  Dog,
  Cat,
  Shield,
  MapPin,
  ShoppingBag,
  FileText,
  Search,
  CreditCard,
  Tag,
  Pill,
  Activity,
  Brain,
  Apple,
  Stethoscope,
  AlertTriangle,
  Home
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"

interface DropdownItem {
  title: string
  href: string
  description?: string
  icon?: React.ComponentType<{ className?: string }>
  badge?: string
}

interface NavDropdownProps {
  title: string
  items: DropdownItem[]
  className?: string
}

export function NavDropdown({ title, items, className }: NavDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const handleMouseEnter = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      setTimeoutId(null)
    }
    setIsOpen(true)
  }

  const handleMouseLeave = () => {
    const id = setTimeout(() => {
      setIsOpen(false)
    }, 150) // Small delay to prevent flickering
    setTimeoutId(id)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault()
      setIsOpen(!isOpen)
    } else if (e.key === "Escape") {
      setIsOpen(false)
    }
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  return (
    <div
      ref={dropdownRef}
      className={cn("relative", className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <button
        className={cn(
          "flex items-center space-x-1 text-sm font-medium transition-all duration-200 py-2 px-3 rounded-md",
          "hover:text-primary hover:bg-primary/5 focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20",
          "relative group",
          isOpen && "text-primary bg-primary/5"
        )}
        onKeyDown={handleKeyDown}
        aria-expanded={isOpen}
        aria-haspopup="true"
        role="button"
        tabIndex={0}
      >
        <span className="whitespace-nowrap">{title}</span>
        <ChevronDown
          className={cn(
            "h-4 w-4 transition-transform duration-200 flex-shrink-0",
            isOpen && "rotate-180"
          )}
        />
        {/* Hover indicator */}
        <div className={cn(
          "absolute bottom-0 left-1/2 transform -translate-x-1/2 h-0.5 bg-primary transition-all duration-200",
          "w-0 group-hover:w-full",
          isOpen && "w-full"
        )} />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className={cn(
              "absolute top-full left-0 mt-3 w-80 bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-100 dark:border-gray-700 z-50 backdrop-blur-sm",
              // Add max height and scroll for longer dropdowns
              items.length > 8 ? "max-h-96 overflow-y-auto dropdown-scroll" : "overflow-hidden"
            )}
            style={{
              boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
            }}
            role="menu"
            aria-orientation="vertical"
          >
            {/* Scroll indicator for long lists */}
            {items.length > 8 && (
              <div className="sticky top-0 bg-gradient-to-b from-white dark:from-gray-800 to-transparent h-4 z-10 flex items-center justify-center">
                <div className="w-8 h-1 bg-gray-300 dark:bg-gray-600 rounded-full opacity-50"></div>
              </div>
            )}

            <div className={cn("py-2", items.length > 8 && "pb-4")}>
              {items.map((item, index) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "block px-5 py-3 hover:bg-gradient-to-r hover:from-primary/5 hover:to-primary/10 transition-all duration-200",
                      "focus:bg-gradient-to-r focus:from-primary/5 focus:to-primary/10 focus:outline-none",
                      "border-l-2 border-transparent hover:border-primary/30 group",
                      "dark:hover:from-primary/10 dark:hover:to-primary/20"
                    )}
                    role="menuitem"
                    tabIndex={-1}
                    onClick={() => setIsOpen(false)}
                  >
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="flex items-start space-x-3"
                    >
                      {Icon && (
                        <div className="flex-shrink-0 mt-0.5">
                          <Icon className="h-4 w-4 text-primary/70 group-hover:text-primary transition-colors duration-200" />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <div className="font-medium text-gray-900 dark:text-gray-100 truncate group-hover:text-primary transition-colors duration-200">
                            {item.title}
                          </div>
                          {item.badge && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold bg-gradient-to-r from-primary/10 to-primary/20 text-primary border border-primary/20">
                              {item.badge}
                            </span>
                          )}
                        </div>
                        {item.description && (
                          <div className="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors duration-200">
                            {item.description}
                          </div>
                        )}
                      </div>
                    </motion.div>
                  </Link>
                )
              })}
            </div>

            {/* Scroll indicator for bottom */}
            {items.length > 8 && (
              <div className="sticky bottom-0 bg-gradient-to-t from-white dark:from-gray-800 to-transparent h-4 z-10 flex items-center justify-center">
                <div className="w-8 h-1 bg-gray-300 dark:bg-gray-600 rounded-full opacity-50"></div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// CATEGORY 1: Adopt & Care - Combined adoption and pet care
export const adoptAndCareDropdownItems: DropdownItem[] = [
  // Adoption Section (6 items)
  {
    title: "Find Pets",
    href: "/pets",
    description: "Browse available pets for adoption",
    icon: Heart
  },
  {
    title: "Adoption Checklist",
    href: "/adoption-checklist",
    description: "Essential steps for pet adoption",
    icon: CheckSquare
  },
  {
    title: "Dog Breed Guide",
    href: "/dog-breeds",
    description: "Learn about different dog breeds",
    icon: Dog
  },
  {
    title: "Cat Breeds",
    href: "/cat-breeds",
    description: "Discover various cat breeds",
    icon: Cat
  },
  {
    title: "Adoption Process",
    href: "/adoption-process",
    description: "Step-by-step adoption guide",
    icon: BookOpen
  },
  {
    title: "Schedule Visit",
    href: "/schedule-visit",
    description: "Book a visit with your future pet",
    icon: Calendar
  }
]

// CATEGORY 2: Pet Health - Comprehensive pet health and care management
export const petCareAndHealthDropdownItems: DropdownItem[] = [
  {
    title: "Pet Names",
    href: "/pet-care/names",
    description: "Find the perfect name for your pet",
    icon: Heart
  },
  {
    title: "Medications",
    href: "/pet-care/medications",
    description: "Pet medication guides and safety",
    icon: Pill
  },
  {
    title: "Illnesses & Symptoms",
    href: "/pet-care/illnesses",
    description: "Common pet health issues",
    icon: Stethoscope
  },
  {
    title: "Training & Behavior",
    href: "/pet-care/training",
    description: "Training guides and behavior tips",
    icon: Brain
  },
  {
    title: "Diet & Nutrition",
    href: "/pet-care/diet",
    description: "Proper nutrition for your pet",
    icon: Apple
  },
  {
    title: "Treatment & Remedies",
    href: "/pet-care/treatment",
    description: "Safe home remedies and first aid",
    icon: Activity
  },
  {
    title: "Poison Prevention",
    href: "/pet-care/poison-prevention",
    description: "Keep your pet safe from toxins",
    icon: AlertTriangle
  },
  {
    title: "Safety",
    href: "/pet-care/safety",
    description: "Comprehensive pet safety guide",
    icon: Shield
  }
]

// CATEGORY 3: Protection & Insurance - Pet insurance and lost pet protection
export const protectionAndInsuranceDropdownItems: DropdownItem[] = [
  // Pet Insurance Section (3 items)
  {
    title: "Compare Pet Insurance Plans",
    href: "/pet-insurance/compare",
    description: "Find the best insurance for your pet",
    icon: CreditCard
  },
  {
    title: "Pet Insurance by State",
    href: "/pet-insurance/by-state",
    description: "State-specific insurance options",
    icon: MapPin
  },
  {
    title: "Pet Insurance Articles",
    href: "/pet-insurance/articles",
    description: "Learn about pet insurance benefits",
    icon: BookOpen
  },
  // Lost Pet Protection Section (6 items)
  {
    title: "Lost Pet Protection Memberships",
    href: "/lost-pet-protection/memberships",
    description: "Comprehensive lost pet recovery services",
    icon: Shield
  },
  {
    title: "Report Lost Pet",
    href: "/lost-pet-protection/report-lost",
    description: "Report your missing pet immediately",
    icon: Search
  },
  {
    title: "Report Found Pet",
    href: "/lost-pet-protection/report-found",
    description: "Help reunite pets with their families",
    icon: Heart
  },
  {
    title: "ByteTag",
    href: "/lost-pet-protection/bytetag",
    description: "Smart pet identification technology",
    icon: Tag
  },
  {
    title: "Microchipping Services",
    href: "/lost-pet-protection/microchipping",
    description: "Permanent pet identification",
    icon: Shield
  },
  {
    title: "Lost Pet Resources",
    href: "/lost-pet-protection/resources",
    description: "Tips and tools for finding lost pets",
    icon: BookOpen
  }
]

// CATEGORY 4: Community & Resources - Shopping, involvement, resources, and about
export const communityAndResourcesDropdownItems: DropdownItem[] = [
  // Shop Section (3 items)
  {
    title: "Pumpkin Wellness Club",
    href: "/shop/pumpkin-wellness",
    description: "Comprehensive pet wellness plans",
    icon: Heart
  },
  {
    title: "ByteTag",
    href: "/shop/bytetag",
    description: "Smart pet identification devices",
    icon: Tag
  },
  {
    title: "PawPack",
    href: "/shop/pawpack",
    description: "Monthly pet supply subscriptions",
    icon: ShoppingBag
  },
  // Get Involved Section (4 items)
  {
    title: "Volunteer",
    href: "/get-involved/volunteer",
    description: "Help pets in your community",
    icon: Users
  },
  {
    title: "Foster",
    href: "/get-involved/foster",
    description: "Provide temporary homes for pets",
    icon: Home
  },
  {
    title: "Donate",
    href: "/get-involved/donate",
    description: "Support our mission",
    icon: DollarSign
  },
  {
    title: "Events",
    href: "/get-involved/events",
    description: "Join our community events",
    icon: Calendar
  },
  // Resources Section (3 items)
  {
    title: "Pet Care Guide",
    href: "/pet-care",
    description: "Comprehensive pet care information",
    icon: BookOpen
  },
  {
    title: "Blog",
    href: "/blog",
    description: "Latest pet care tips and stories",
    icon: FileText
  },
  {
    title: "Consumer Survey",
    href: "/consumer-survey",
    description: "Share your pet care experiences",
    icon: FileText
  },
  // About Section (5 items)
  {
    title: "About Us",
    href: "/about",
    description: "Learn about our mission",
    icon: Info
  },
  {
    title: "Adoption Stories",
    href: "/adoption-stories",
    description: "Heartwarming success stories",
    icon: Heart
  },
  {
    title: "Contact",
    href: "/contact",
    description: "Get in touch with us",
    icon: Phone
  },
  {
    title: "FAQ",
    href: "/faq",
    description: "Frequently asked questions",
    icon: Info
  },
  {
    title: "Privacy Policy",
    href: "/privacy",
    description: "Our privacy and data policies",
    icon: Shield
  }
]
