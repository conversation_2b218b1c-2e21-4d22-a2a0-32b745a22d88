"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, Sparkles, Heart, Wand2 } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { EnchantedPetCard } from "./EnchantedPetCard"
import { EnchantedFilters } from "./EnchantedFilters"
import { useFavorites } from "@/hooks/use-favorites"
import { toast } from "react-hot-toast"

interface Pet {
  id: string
  name: string
  species: string
  breed: string
  age: number
  size: string
  gender: string
  description: string
  personalityTraits: string[]
  photos: Array<{ url: string; isPrimary: boolean }>
  organization: {
    name: string
    city: string
    state: string
  }
  adoptionFee: number
  goodWithKids: boolean
  goodWithDogs: boolean
  goodWithCats: boolean
  _count: {
    favorites: number
    applications: number
  }
}

interface PetsResponse {
  pets: Pet[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

interface FilterState {
  species: string
  size: string
  age: string
  gender: string
  goodWithKids: string
  goodWithDogs: string
  goodWithCats: string
  personality: string[]
}

export function EnchantedGallery() {
  const [pets, setPets] = useState<Pet[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState<PetsResponse['pagination'] | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<FilterState>({
    species: '',
    size: '',
    age: '',
    gender: '',
    goodWithKids: '',
    goodWithDogs: '',
    goodWithCats: '',
    personality: [],
  })

  const { favorites, isFavorited, toggleFavorite } = useFavorites()

  const fetchPets = async (page = 1) => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        ...(searchTerm && { search: searchTerm }),
        ...Object.fromEntries(
          Object.entries(filters).filter(([key, value]) => {
            if (key === 'personality') return Array.isArray(value) && value.length > 0
            return value !== ''
          }).map(([key, value]) => [key, Array.isArray(value) ? value.join(',') : value])
        )
      })

      const response = await fetch(`/api/pets?${params}`)
      if (response.ok) {
        const data: PetsResponse = await response.json()
        setPets(data.pets || [])
        setPagination(data.pagination)
      } else {
        throw new Error("Failed to fetch pets")
      }
    } catch (error) {
      console.error('Error fetching pets:', error)
      toast.error("Failed to load pets. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPets()
  }, [searchTerm, filters])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchPets(1)
  }

  const handleFilterChange = (key: string, value: string | string[]) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      species: '',
      size: '',
      age: '',
      gender: '',
      goodWithKids: '',
      goodWithDogs: '',
      goodWithCats: '',
      personality: [],
    })
    setSearchTerm('')
  }

  const handleFavoriteChange = async (petId: string, newFavoriteState: boolean) => {
    const success = await toggleFavorite(petId)
    if (success !== newFavoriteState) {
      toast.error("Failed to update favorite")
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Magical Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <motion.div
            className="inline-flex items-center gap-2 mb-4"
            animate={{ 
              rotate: [0, 5, -5, 0],
            }}
            transition={{ 
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <Wand2 className="h-8 w-8 text-purple-600" />
            <Sparkles className="h-6 w-6 text-pink-500" />
          </motion.div>
          
          <h1 className="text-4xl lg:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent mb-4">
            Enchanted Pet Gallery
          </h1>
          <motion.p 
            className="text-xl text-gray-600 max-w-2xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            Where magical connections happen! ✨ Find your perfect furry companion in our parade of adorable friends.
          </motion.p>
        </motion.div>

        {/* Search Bar */}
        <motion.form
          onSubmit={handleSearch}
          className="max-w-2xl mx-auto mb-8"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.4 }}
        >
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="Search by name, breed, or magical qualities... 🔍"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-12 pr-4 py-4 text-lg rounded-2xl border-2 border-purple-200 focus:border-purple-400 focus:ring-4 focus:ring-purple-100 transition-all duration-300"
            />
          </div>
        </motion.form>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mb-8"
        >
          <EnchantedFilters
            filters={filters}
            onFilterChange={handleFilterChange}
            onClearFilters={clearFilters}
            showFilters={showFilters}
            onToggleFilters={() => setShowFilters(!showFilters)}
          />
        </motion.div>

        {/* Results Count */}
        {pagination && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="mb-6 text-center"
          >
            <p className="text-gray-600 text-lg">
              ✨ Discovered <span className="font-semibold text-purple-600">{pets.length}</span> of{' '}
              <span className="font-semibold text-purple-600">{pagination.total}</span> magical companions
            </p>
          </motion.div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: i * 0.1 }}
                className="bg-white rounded-2xl shadow-lg overflow-hidden animate-pulse"
              >
                <div className="aspect-square bg-gradient-to-br from-purple-100 to-pink-100"></div>
                <div className="p-4 space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* Pet Gallery */}
        {!loading && pets.length > 0 && (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <AnimatePresence>
              {pets.map((pet, index) => (
                <motion.div
                  key={pet.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -50 }}
                  transition={{ 
                    delay: index * 0.1,
                    type: "spring",
                    stiffness: 100,
                    damping: 15
                  }}
                >
                  <EnchantedPetCard
                    pet={pet}
                    isFavorited={isFavorited(pet.id)}
                    onFavoriteChange={handleFavoriteChange}
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>
        )}

        {/* No Results */}
        {!loading && pets.length === 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-16"
          >
            <motion.div
              className="text-8xl mb-6"
              animate={{ 
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              🔮
            </motion.div>
            <h3 className="text-3xl font-bold text-gray-900 mb-4">No magical companions found</h3>
            <p className="text-gray-600 mb-6 text-lg">
              The crystal ball shows no matches for your search. Try adjusting your magical filters!
            </p>
            <Button 
              onClick={clearFilters}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-8 py-3 rounded-full text-lg"
            >
              <Sparkles className="h-5 w-5 mr-2" />
              Clear All Filters
            </Button>
          </motion.div>
        )}

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-12 flex justify-center items-center space-x-4"
          >
            <Button
              variant="outline"
              disabled={!pagination.hasPrev}
              onClick={() => fetchPets(pagination.page - 1)}
              className="rounded-full px-6"
            >
              ← Previous
            </Button>
            
            <span className="px-6 py-2 text-lg text-gray-600 bg-white rounded-full shadow-sm">
              Page {pagination.page} of {pagination.totalPages}
            </span>
            
            <Button
              variant="outline"
              disabled={!pagination.hasNext}
              onClick={() => fetchPets(pagination.page + 1)}
              className="rounded-full px-6"
            >
              Next →
            </Button>
          </motion.div>
        )}
      </div>
    </div>
  )
}
