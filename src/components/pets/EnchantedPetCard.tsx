"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { motion, AnimatePresence } from "framer-motion"
import { Heart, MapPin, Sparkles, Star, Zap, Crown, Coffee, Music } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { toast } from "react-hot-toast"
import { cn } from "@/lib/utils"

interface Pet {
  id: string
  name: string
  species: string
  breed: string
  age: number
  size: string
  gender: string
  description: string
  personalityTraits: string[]
  photos: Array<{ url: string; isPrimary: boolean }>
  organization: {
    name: string
    city: string
    state: string
  }
  adoptionFee: number
  goodWithKids: boolean
  goodWithDogs: boolean
  goodWithCats: boolean
  _count: {
    favorites: number
    applications: number
  }
}

interface EnchantedPetCardProps {
  pet: Pet
  isFavorited?: boolean
  onFavoriteChange?: (petId: string, isFavorited: boolean) => void
}

const personalityIcons = {
  playful: <Zap className="h-3 w-3" />,
  cuddly: <Heart className="h-3 w-3" />,
  energetic: <Star className="h-3 w-3" />,
  calm: <Coffee className="h-3 w-3" />,
  social: <Music className="h-3 w-3" />,
  loyal: <Crown className="h-3 w-3" />,
  default: <Sparkles className="h-3 w-3" />
}

const getPersonalityIcon = (trait: string) => {
  const lowerTrait = trait.toLowerCase()
  if (lowerTrait.includes('play')) return personalityIcons.playful
  if (lowerTrait.includes('cudd') || lowerTrait.includes('affection')) return personalityIcons.cuddly
  if (lowerTrait.includes('energy') || lowerTrait.includes('active')) return personalityIcons.energetic
  if (lowerTrait.includes('calm') || lowerTrait.includes('gentle')) return personalityIcons.calm
  if (lowerTrait.includes('social') || lowerTrait.includes('friend')) return personalityIcons.social
  if (lowerTrait.includes('loyal') || lowerTrait.includes('devoted')) return personalityIcons.loyal
  return personalityIcons.default
}

const getCharmingMessage = (trait: string) => {
  const lowerTrait = trait.toLowerCase()
  if (lowerTrait.includes('play')) return "Loves to play! 🎾"
  if (lowerTrait.includes('cudd')) return "Loves to cuddle! 🤗"
  if (lowerTrait.includes('energy')) return "Dreams of fetch! ⚡"
  if (lowerTrait.includes('calm')) return "Zen master! 🧘"
  if (lowerTrait.includes('social')) return "Social butterfly! 🦋"
  if (lowerTrait.includes('loyal')) return "Forever faithful! 👑"
  if (lowerTrait.includes('smart')) return "Einstein in disguise! 🧠"
  if (lowerTrait.includes('gentle')) return "Gentle soul! 💕"
  if (lowerTrait.includes('active')) return "Adventure buddy! 🏃"
  if (lowerTrait.includes('quiet')) return "Peaceful companion! 🕊️"
  return `${trait}! ✨`
}

const formatAge = (ageInMonths: number) => {
  if (ageInMonths < 12) {
    return `${ageInMonths} month${ageInMonths !== 1 ? 's' : ''} old`
  } else {
    const years = Math.floor(ageInMonths / 12)
    const months = ageInMonths % 12
    if (months === 0) {
      return `${years} year${years !== 1 ? 's' : ''} old`
    } else {
      return `${years}y ${months}m old`
    }
  }
}

export function EnchantedPetCard({ pet, isFavorited = false, onFavoriteChange }: EnchantedPetCardProps) {
  const { data: session } = useSession()
  const [isHovered, setIsHovered] = useState(false)
  const [favoriteLoading, setFavoriteLoading] = useState(false)
  const [localIsFavorited, setLocalIsFavorited] = useState(isFavorited)

  const handleFavorite = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!session) {
      toast.error("Please sign in to save favorites")
      return
    }

    setFavoriteLoading(true)
    try {
      const response = await fetch(`/api/pets/${pet.id}/favorite`, {
        method: localIsFavorited ? 'DELETE' : 'POST',
      })

      if (response.ok) {
        const newFavoriteState = !localIsFavorited
        setLocalIsFavorited(newFavoriteState)
        onFavoriteChange?.(pet.id, newFavoriteState)
        toast.success(newFavoriteState ? "Added to favorites! ✨" : "Removed from favorites")
      } else {
        toast.error("Failed to update favorites")
      }
    } catch (error) {
      toast.error("Failed to update favorites")
    } finally {
      setFavoriteLoading(false)
    }
  }

  const primaryPhoto = pet.photos.find(p => p.isPrimary)?.url || pet.photos[0]?.url

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -8 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="group"
    >
      <Link href={`/pets/${pet.id}`}>
        <div className="relative bg-white rounded-2xl shadow-lg overflow-hidden transition-all duration-500 hover:shadow-2xl border border-gray-100">
          {/* Image Container */}
          <div className="relative aspect-square overflow-hidden">
            {primaryPhoto ? (
              <Image
                src={primaryPhoto}
                alt={pet.name}
                fill
                className="object-cover transition-transform duration-700 group-hover:scale-110"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                loading="lazy"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center">
                <motion.span 
                  className="text-6xl"
                  animate={{ rotate: isHovered ? 360 : 0 }}
                  transition={{ duration: 0.8 }}
                >
                  🐾
                </motion.span>
              </div>
            )}

            {/* Magical Overlay */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"
              initial={{ opacity: 0 }}
              animate={{ opacity: isHovered ? 1 : 0 }}
              transition={{ duration: 0.3 }}
            />

            {/* Sparkly Heart Favorite Button */}
            <motion.button
              onClick={handleFavorite}
              disabled={favoriteLoading}
              className={cn(
                "absolute top-3 right-3 p-2 rounded-full backdrop-blur-sm transition-all duration-300",
                localIsFavorited 
                  ? "bg-red-500/90 text-white shadow-lg" 
                  : "bg-white/80 text-gray-600 hover:bg-white hover:text-red-500"
              )}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              aria-label={localIsFavorited ? "Remove from favorites" : "Add to favorites"}
            >
              <AnimatePresence mode="wait">
                {favoriteLoading ? (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent"
                  />
                ) : (
                  <motion.div
                    key="heart"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                  >
                    <Heart 
                      className="h-5 w-5" 
                      fill={localIsFavorited ? "currentColor" : "none"}
                    />
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Sparkle Effect */}
              {localIsFavorited && (
                <motion.div
                  className="absolute inset-0 pointer-events-none"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: [0, 1, 0] }}
                  transition={{ duration: 1, repeat: Infinity, repeatDelay: 2 }}
                >
                  {[...Array(3)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute text-yellow-400"
                      style={{
                        top: `${20 + i * 15}%`,
                        left: `${20 + i * 20}%`,
                      }}
                      animate={{
                        y: [-5, -15, -5],
                        opacity: [0, 1, 0],
                        scale: [0.5, 1, 0.5],
                      }}
                      transition={{
                        duration: 1.5,
                        delay: i * 0.2,
                        repeat: Infinity,
                        repeatDelay: 3,
                      }}
                    >
                      ✨
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </motion.button>

            {/* Compatibility Badges */}
            <div className="absolute bottom-3 left-3 flex gap-1">
              {pet.goodWithKids && (
                <motion.span 
                  className="px-2 py-1 bg-green-500/90 text-white text-xs rounded-full backdrop-blur-sm"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.1 }}
                >
                  Kids ✓
                </motion.span>
              )}
              {pet.goodWithDogs && (
                <motion.span 
                  className="px-2 py-1 bg-blue-500/90 text-white text-xs rounded-full backdrop-blur-sm"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  Dogs ✓
                </motion.span>
              )}
              {pet.goodWithCats && (
                <motion.span 
                  className="px-2 py-1 bg-purple-500/90 text-white text-xs rounded-full backdrop-blur-sm"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  Cats ✓
                </motion.span>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="p-4">
            <div className="mb-2">
              <h3 className="text-xl font-bold text-gray-900 mb-1 group-hover:text-purple-600 transition-colors">
                {pet.name}
              </h3>
              <p className="text-sm text-gray-600">
                {pet.breed} • {formatAge(pet.age)} • {pet.gender}
              </p>
            </div>

            <div className="flex items-center text-sm text-gray-500 mb-3">
              <MapPin className="h-3 w-3 mr-1" />
              {pet.organization.city}, {pet.organization.state}
            </div>

            {/* Personality Traits - Show on Hover */}
            <AnimatePresence>
              {isHovered && pet.personalityTraits.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="mb-3 overflow-hidden"
                >
                  <div className="flex flex-wrap gap-1">
                    {pet.personalityTraits.slice(0, 3).map((trait, index) => (
                      <motion.div
                        key={trait}
                        initial={{ opacity: 0, scale: 0.8, y: 10 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        transition={{ delay: index * 0.1, type: "spring", stiffness: 300 }}
                        className="relative group/trait"
                      >
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 text-xs rounded-full border border-purple-200 hover:from-purple-200 hover:to-pink-200 transition-all duration-200 cursor-default">
                          {getPersonalityIcon(trait)}
                          {trait}
                        </span>

                        {/* Charming Message Tooltip */}
                        <motion.div
                          initial={{ opacity: 0, scale: 0.8, y: 5 }}
                          animate={{ opacity: 1, scale: 1, y: 0 }}
                          className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded-lg whitespace-nowrap opacity-0 group-hover/trait:opacity-100 transition-opacity duration-200 pointer-events-none z-10"
                        >
                          {getCharmingMessage(trait)}
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
                        </motion.div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            <p className="text-sm text-gray-600 line-clamp-2 mb-3">
              {pet.description}
            </p>

            {pet.adoptionFee && (
              <div className="text-lg font-semibold text-green-600 mb-2">
                ${pet.adoptionFee} adoption fee
              </div>
            )}

            <div className="flex justify-between items-center text-xs text-gray-500">
              <span className="flex items-center gap-1">
                <Heart className="h-3 w-3" />
                {pet._count.favorites} favorites
              </span>
              <span>{pet._count.applications} applications</span>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  )
}
