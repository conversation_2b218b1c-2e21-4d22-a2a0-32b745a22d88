"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Filter, X, <PERSON>, Zap, Coffee, Music, Crown, Sparkles } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface FilterState {
  species: string
  size: string
  age: string
  gender: string
  goodWithKids: string
  goodWithDogs: string
  goodWithCats: string
  personality: string[]
}

interface EnchantedFiltersProps {
  filters: FilterState
  onFilterChange: (key: string, value: string | string[]) => void
  onClearFilters: () => void
  showFilters: boolean
  onToggleFilters: () => void
}

const personalityOptions = [
  { value: "playful", label: "Playful", icon: Zap, color: "from-yellow-400 to-orange-400" },
  { value: "cuddly", label: "<PERSON>uddly", icon: Heart, color: "from-pink-400 to-red-400" },
  { value: "energetic", label: "Energetic", icon: Spark<PERSON>, color: "from-green-400 to-blue-400" },
  { value: "calm", label: "Calm", icon: Coffee, color: "from-blue-400 to-purple-400" },
  { value: "social", label: "Social", icon: Music, color: "from-purple-400 to-pink-400" },
  { value: "loyal", label: "Loyal", icon: Crown, color: "from-amber-400 to-yellow-400" },
]

export function EnchantedFilters({ 
  filters, 
  onFilterChange, 
  onClearFilters, 
  showFilters, 
  onToggleFilters 
}: EnchantedFiltersProps) {
  const [activePersonality, setActivePersonality] = useState<string[]>(filters.personality || [])

  const handlePersonalityToggle = (personality: string) => {
    const newPersonalities = activePersonality.includes(personality)
      ? activePersonality.filter(p => p !== personality)
      : [...activePersonality, personality]
    
    setActivePersonality(newPersonalities)
    onFilterChange('personality', newPersonalities)
  }

  const hasActiveFilters = Object.entries(filters).some(([key, value]) => {
    if (key === 'personality') return Array.isArray(value) && value.length > 0
    return value !== ''
  })

  return (
    <div className="space-y-4">
      {/* Filter Toggle Button */}
      <div className="flex items-center justify-between">
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Button
            onClick={onToggleFilters}
            variant="outline"
            className={cn(
              "relative overflow-hidden transition-all duration-300",
              showFilters && "bg-purple-50 border-purple-200 text-purple-700"
            )}
          >
            <Filter className="h-4 w-4 mr-2" />
            Magical Filters
            {hasActiveFilters && (
              <motion.span
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="absolute -top-1 -right-1 h-3 w-3 bg-purple-500 rounded-full"
              />
            )}
          </Button>
        </motion.div>

        {hasActiveFilters && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
          >
            <Button
              onClick={onClearFilters}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-red-500"
            >
              <X className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          </motion.div>
        )}
      </div>

      {/* Filters Panel */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <Card className="border-purple-100 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Find Your Perfect Match ✨
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Filters */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Species</label>
                    <select
                      value={filters.species}
                      onChange={(e) => onFilterChange('species', e.target.value)}
                      className="w-full p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                    >
                      <option value="">All Species</option>
                      <option value="Dog">🐕 Dogs</option>
                      <option value="Cat">🐱 Cats</option>
                      <option value="Rabbit">🐰 Rabbits</option>
                      <option value="Bird">🐦 Birds</option>
                      <option value="Other">🐾 Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Size</label>
                    <select
                      value={filters.size}
                      onChange={(e) => onFilterChange('size', e.target.value)}
                      className="w-full p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                    >
                      <option value="">All Sizes</option>
                      <option value="EXTRA_SMALL">Extra Small</option>
                      <option value="SMALL">Small</option>
                      <option value="MEDIUM">Medium</option>
                      <option value="LARGE">Large</option>
                      <option value="EXTRA_LARGE">Extra Large</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Age</label>
                    <select
                      value={filters.age}
                      onChange={(e) => onFilterChange('age', e.target.value)}
                      className="w-full p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                    >
                      <option value="">All Ages</option>
                      <option value="puppy">🍼 Puppy/Kitten</option>
                      <option value="young">🌱 Young</option>
                      <option value="adult">🌟 Adult</option>
                      <option value="senior">👑 Senior</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2 text-gray-700">Gender</label>
                    <select
                      value={filters.gender}
                      onChange={(e) => onFilterChange('gender', e.target.value)}
                      className="w-full p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                    >
                      <option value="">All Genders</option>
                      <option value="MALE">♂️ Male</option>
                      <option value="FEMALE">♀️ Female</option>
                    </select>
                  </div>
                </div>

                {/* Compatibility Filters */}
                <div>
                  <label className="block text-sm font-medium mb-3 text-gray-700">Good With</label>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <select
                        value={filters.goodWithKids}
                        onChange={(e) => onFilterChange('goodWithKids', e.target.value)}
                        className="w-full p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                      >
                        <option value="">Kids - Any</option>
                        <option value="true">👶 Good with Kids</option>
                        <option value="false">❌ Not with Kids</option>
                      </select>
                    </div>
                    <div>
                      <select
                        value={filters.goodWithDogs}
                        onChange={(e) => onFilterChange('goodWithDogs', e.target.value)}
                        className="w-full p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                      >
                        <option value="">Dogs - Any</option>
                        <option value="true">🐕 Good with Dogs</option>
                        <option value="false">❌ Not with Dogs</option>
                      </select>
                    </div>
                    <div>
                      <select
                        value={filters.goodWithCats}
                        onChange={(e) => onFilterChange('goodWithCats', e.target.value)}
                        className="w-full p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                      >
                        <option value="">Cats - Any</option>
                        <option value="true">🐱 Good with Cats</option>
                        <option value="false">❌ Not with Cats</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Personality Filters */}
                <div>
                  <label className="block text-sm font-medium mb-3 text-gray-700">Personality Traits</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {personalityOptions.map((option) => {
                      const Icon = option.icon
                      const isActive = activePersonality.includes(option.value)
                      
                      return (
                        <motion.button
                          key={option.value}
                          onClick={() => handlePersonalityToggle(option.value)}
                          className={cn(
                            "relative p-3 rounded-xl border-2 transition-all duration-300 text-left",
                            isActive
                              ? "border-purple-300 bg-gradient-to-r " + option.color + " text-white shadow-lg"
                              : "border-gray-200 bg-white hover:border-purple-200 hover:bg-purple-50"
                          )}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="flex items-center gap-2">
                            <Icon className="h-4 w-4" />
                            <span className="text-sm font-medium">{option.label}</span>
                          </div>
                          
                          {isActive && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="absolute -top-1 -right-1 h-3 w-3 bg-white rounded-full border-2 border-purple-400"
                            />
                          )}
                        </motion.button>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
